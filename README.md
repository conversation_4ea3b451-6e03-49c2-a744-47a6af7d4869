# add-motor-shop

## Project setup

```
npm install
```

### Compiles and hot-reloads for development

```
npm run serve
```

### Compiles and minifies for production

```
npm run build
```

### Customize configuration

See [Configuration Reference](https://cli.vuejs.org/config/).

# HTML内容渲染功能

## 功能说明
实现了调用API接口获取HTML内容并渲染到页面的功能。

## API接口
```javascript
// 文件：src/api/common.js
import { getPageContent } from '@/api/common.js';

// 获取页面HTML内容
const response = await getPageContent(Aid);
```

## 页面使用
```javascript
// 跳转到HTML内容页面
this.$tab.navigateTo(`/pages/blank/blank?Aid=${Aid}`);
```

## 参数说明
- `Aid`: 页面ID，用于获取对应的HTML内容

## 使用示例

### 1. 在任意页面中跳转到HTML内容页面
```javascript
// 方法1：使用默认Aid（137）
this.$tab.navigateTo('/pages/blank/blank');

// 方法2：指定Aid参数
this.$tab.navigateTo('/pages/blank/blank?Aid=137');
```

### 2. 在组件中调用API获取HTML内容
```javascript
import { getPageContent } from '@/api/common.js';

export default {
  methods: {
    async loadHtmlContent() {
      try {
        const response = await getPageContent('137');
        // 处理HTML内容
        const htmlContent = response.data?.html || response.html;
        console.log('获取到的HTML内容:', htmlContent);
      } catch (error) {
        console.error('获取HTML内容失败:', error);
      }
    }
  }
}
```

## 功能特性
- ✅ 支持完整HTML内容渲染
- ✅ 自动适配移动端样式
- ✅ 支持图片自适应
- ✅ 支持错误处理和重试
- ✅ 支持加载状态显示
- ✅ 支持多种数据格式兼容

## 注意事项
1. HTML内容中的样式会被正确渲染，但建议测试样式兼容性
2. 图片链接需要确保可访问性
3. 大量HTML内容可能影响页面性能，建议适当优化
4. 外部链接在uni-app中可能需要特殊处理

## 技术实现
- 使用 `v-html` 指令渲染HTML内容
- 使用 CSS深度选择器处理样式适配
- 支持多种API响应格式自动识别
