{"name": "add-motor-shop", "version": "0.1.0", "private": true, "scripts": {"setup": "./setup-env.sh", "serve": "npm run dev:h5", "build": "npm run build:h5 && node rename-to-web.js", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:custom": "cross-env NODE_ENV=production uniapp-cli custom", "build:h5": "node increment-version.js && cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:web": "npm run build:h5 && node rename-to-web.js", "build:test": "cross-env VUE_APP_ENV=test npm run build:h5 && node rename-to-web.js", "build:prod": "cross-env VUE_APP_ENV=production npm run build:h5 && node rename-to-web.js && node scripts/optimize-build.js && node scripts/performance-test.js", "test:performance": "node scripts/performance-test.js", "build:mp-360": "cross-env NODE_ENV=production UNI_PLATFORM=mp-360 vue-cli-service uni-build", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-harmony": "cross-env NODE_ENV=production UNI_PLATFORM=mp-harmony vue-cli-service uni-build", "build:mp-jd": "cross-env NODE_ENV=production UNI_PLATFORM=mp-jd vue-cli-service uni-build", "build:mp-kuaishou": "cross-env NODE_ENV=production UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build", "build:mp-lark": "cross-env NODE_ENV=production UNI_PLATFORM=mp-lark vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "build:mp-xhs": "cross-env NODE_ENV=production UNI_PLATFORM=mp-xhs vue-cli-service uni-build", "build:quickapp-native": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-native vue-cli-service uni-build", "build:quickapp-webview": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview vue-cli-service uni-build", "build:quickapp-webview-huawei": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build", "build:quickapp-webview-union": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev": "cross-env NODE_ENV=development UNI_PLATFORM=h5 SASS_SILENCE_DEPRECATIONS=legacy-js-api vue-cli-service uni-serve", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 SASS_SILENCE_DEPRECATIONS=legacy-js-api vue-cli-service uni-serve", "dev:mp-360": "cross-env NODE_ENV=development UNI_PLATFORM=mp-360 vue-cli-service uni-build --watch", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-harmony": "cross-env NODE_ENV=development UNI_PLATFORM=mp-harmony vue-cli-service uni-build --watch", "dev:mp-jd": "cross-env NODE_ENV=development UNI_PLATFORM=mp-jd vue-cli-service uni-build --watch", "dev:mp-kuaishou": "cross-env NODE_ENV=development UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build --watch", "dev:mp-lark": "cross-env NODE_ENV=development UNI_PLATFORM=mp-lark vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "dev:mp-xhs": "cross-env NODE_ENV=development UNI_PLATFORM=mp-xhs vue-cli-service uni-build --watch", "dev:quickapp-native": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-native vue-cli-service uni-build --watch", "dev:quickapp-webview": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview vue-cli-service uni-build --watch", "dev:quickapp-webview-huawei": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build --watch", "dev:quickapp-webview-union": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build --watch", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js", "serve:quickapp-native": "node node_modules/@dcloudio/uni-quickapp-native/bin/serve.js", "test:android": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=android jest -i", "test:h5": "cross-env UNI_PLATFORM=h5 jest -i", "test:ios": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=ios jest -i", "test:mp-baidu": "cross-env UNI_PLATFORM=mp-baidu jest -i", "test:mp-weixin": "cross-env UNI_PLATFORM=mp-weixin jest -i"}, "dependencies": {"@dcloudio/uni-app": "^2.0.2-4060620250520001", "@dcloudio/uni-app-plus": "^2.0.2-4060620250520001", "@dcloudio/uni-h5": "^2.0.2-4060620250520001", "@dcloudio/uni-i18n": "^2.0.2-4060620250520001", "@dcloudio/uni-mp-360": "^2.0.2-4060620250520001", "@dcloudio/uni-mp-alipay": "^2.0.2-4060620250520001", "@dcloudio/uni-mp-baidu": "^2.0.2-4060620250520001", "@dcloudio/uni-mp-harmony": "^2.0.2-4060620250520001", "@dcloudio/uni-mp-jd": "^2.0.2-4060620250520001", "@dcloudio/uni-mp-kuaishou": "^2.0.2-4060620250520001", "@dcloudio/uni-mp-lark": "^2.0.2-4060620250520001", "@dcloudio/uni-mp-qq": "^2.0.2-4060620250520001", "@dcloudio/uni-mp-toutiao": "^2.0.2-4060620250520001", "@dcloudio/uni-mp-vue": "^2.0.2-4060620250520001", "@dcloudio/uni-mp-weixin": "^2.0.2-4060620250520001", "@dcloudio/uni-mp-xhs": "^2.0.2-4060620250520001", "@dcloudio/uni-quickapp-native": "^2.0.2-4060620250520001", "@dcloudio/uni-quickapp-webview": "^2.0.2-4060620250520001", "@dcloudio/uni-stacktracey": "^2.0.2-4060620250520001", "@dcloudio/uni-stat": "^2.0.2-4060620250520001", "@paypal/paypal-js": "^8.2.0", "@stripe/stripe-js": "^7.5.0", "@vue/shared": "^3.0.0", "axios": "^1.9.0", "core-js": "^3.43.0", "decimal.js": "^10.5.0", "jsencrypt": "^3.3.2", "lodash-es": "^4.17.21", "regenerator-runtime": "^0.14.1", "three": "0.158.0", "vconsole": "^3.15.1", "vue": ">= 2.6.14 < 2.7", "vuex": "^3.2.0"}, "devDependencies": {"@dcloudio/types": "^3.3.2", "@dcloudio/uni-automator": "^2.0.2-4060620250520001", "@dcloudio/uni-cli-i18n": "^2.0.2-4060620250520001", "@dcloudio/uni-cli-shared": "^2.0.2-4060620250520001", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-migration": "^2.0.2-4060620250520001", "@dcloudio/uni-template-compiler": "^2.0.2-4060620250520001", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.2-4060620250520001", "@dcloudio/vue-cli-plugin-uni": "^2.0.2-4060620250520001", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.2-4060620250520001", "@dcloudio/webpack-uni-mp-loader": "^2.0.2-4060620250520001", "@dcloudio/webpack-uni-pages-loader": "^2.0.2-4060620250520001", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-service": "~5.0.0", "babel-plugin-import": "^1.11.0", "cross-env": "^7.0.2", "jest": "^25.4.0", "postcss-comment": "^2.0.0", "postcss-gap-properties": "^6.0.0", "sass": "^1.63.6", "sass-loader": "^10.0.1", "vue-template-compiler": ">= 2.6.14 < 2.7"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}}