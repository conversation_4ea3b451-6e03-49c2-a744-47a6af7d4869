const path = require('path')

// 抑制 Sass 弃用警告
process.env.SASS_SILENCE_DEPRECATIONS = 'legacy-js-api'
// 抑制导入弃用警告  
process.env.SASS_QUIET_DEPS = 'true'
// 设置详细模式为 false
process.env.SASS_VERBOSE = 'false'

module.exports = {
  // 设置公共路径为相对路径（关键配置）
  publicPath: './',
  
  // 输出目录
  outputDir: 'dist/build/web',
   
  // 静态资源目录
  assetsDir: 'static',
  
  // 禁用source map（减少文件大小）
  productionSourceMap: false,
  
  configureWebpack: {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
        // 使用优化版本的Three.js导入
        'three-optimized': path.resolve(__dirname, 'src/utils/threeOptimized.js')
      }
    },
    // 代码分割和优化
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          // Three.js单独打包
          three: {
            test: /[\\/]node_modules[\\/]three[\\/]/,
            name: 'three',
            chunks: 'all',
            priority: 20
          },
          // 支付SDK单独打包
          payment: {
            test: /[\\/]node_modules[\\/](@paypal|@stripe)[\\/]/,
            name: 'payment-sdk',
            chunks: 'all',
            priority: 15
          },
          // uview-ui组件库单独打包
          uview: {
            test: /[\\/]src[\\/]uni_modules[\\/]uview-ui[\\/]/,
            name: 'uview-ui',
            chunks: 'all',
            priority: 10
          },
          // 通用vendor
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
            priority: 5
          }
        }
      },
      // 启用压缩
      minimize: true
    },
    // 外部化大型库（如果从CDN加载）
    externals: process.env.NODE_ENV === 'production' ? {
      // 可选：如果使用CDN加载Three.js
      // 'three': 'THREE'
    } : {},
    // 简化 webpack 输出信息
    stats: {
      modules: false,
      children: false,
      chunks: false,
      chunkModules: false,
      entrypoints: false,
      warningsFilter: [
        /export .* was not found in/,
        /System.import/
      ]
    }
  },
  
  css: {
    loaderOptions: {
      sass: {
        // 尝试使用 sassOptions 配置（如果支持的话）
        sassOptions: {
          quietDeps: true,
          verbose: false,
          silenceDeprecations: ['legacy-js-api', 'import']
        },
        // 全局导入 uview-ui 主题变量
        prependData: `@import "@/uni_modules/uview-ui/theme.scss";`
      },
      scss: {
        // 尝试使用 sassOptions 配置（如果支持的话）
        sassOptions: {
          quietDeps: true,
          verbose: false,
          silenceDeprecations: ['legacy-js-api', 'import']
        },
        // 全局导入 uview-ui 主题变量
        prependData: `
          @import "@/uni_modules/uview-ui/theme.scss";
          @import "@/static/style/mixins.scss";
        `
      },
      postcss: {
        postcssOptions: {
          plugins: [
            require('postcss-gap-properties')
          ]
        }
      }
    }
  }
} 