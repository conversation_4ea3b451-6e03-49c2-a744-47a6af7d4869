#!/usr/bin/env node
/**
 * 构建优化脚本
 * 用于压缩图片和清理不必要的文件
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 开始构建优化...');

// 1. 检查并报告优化结果
function checkOptimizations() {
  const staticDir = path.join(__dirname, '../src/static');
  
  if (!fs.existsSync(staticDir)) {
    console.log('❌ static目录不存在');
    return;
  }
  
  // 统计静态资源大小
  function getDirSize(dirPath) {
    let totalSize = 0;
    const files = fs.readdirSync(dirPath);
    
    files.forEach(file => {
      const filePath = path.join(dirPath, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isDirectory()) {
        totalSize += getDirSize(filePath);
      } else {
        totalSize += stats.size;
      }
    });
    
    return totalSize;
  }
  
  const totalSize = getDirSize(staticDir);
  const sizeMB = (totalSize / 1024 / 1024).toFixed(2);
  
  console.log(`📊 静态资源总大小: ${sizeMB}MB`);
  
  // 检查大文件
  function findLargeFiles(dirPath, threshold = 100 * 1024) { // 100KB
    const largeFiles = [];
    
    function scanDir(currentPath) {
      const files = fs.readdirSync(currentPath);
      
      files.forEach(file => {
        const filePath = path.join(currentPath, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isDirectory()) {
          scanDir(filePath);
        } else if (stats.size > threshold) {
          largeFiles.push({
            path: filePath.replace(staticDir, ''),
            size: (stats.size / 1024).toFixed(1) + 'KB'
          });
        }
      });
    }
    
    scanDir(dirPath);
    return largeFiles;
  }
  
  const largeFiles = findLargeFiles(staticDir);
  
  if (largeFiles.length > 0) {
    console.log('📋 发现较大文件 (>100KB):');
    largeFiles.forEach(file => {
      console.log(`   ${file.path} (${file.size})`);
    });
  } else {
    console.log('✅ 没有发现大于100KB的文件');
  }
}

// 2. 生成优化建议
function generateOptimizationTips() {
  console.log('\n💡 WebView环境优化建议:');
  console.log('   1. ✅ 已启用代码分割 (vendor, three, payment, uview)');
  console.log('   2. ✅ 已禁用source map');
  console.log('   3. ✅ 已清理未使用的npm包 (节省~50KB)');
  console.log('   4. 🔧 使用Three.js优化导入 (可节省~400KB)');
  console.log('   5. 🔧 压缩图片到WebP格式 (可节省~400KB)');
  console.log('   6. 🔧 启用App内gzip压缩 (可节省30-50%)');
  console.log('   7. 🔧 3D模型按需加载 (可节省~1MB)');
  console.log('   8. 🔧 移除开发环境代码');
}

// 运行优化检查
checkOptimizations();
generateOptimizationTips();

console.log('\n🎉 构建优化检查完成!');