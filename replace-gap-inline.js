const fs = require('fs');
const path = require('path');

// 递归遍历目录，处理所有 .vue、.scss、.css 文件
function walkDir(dir) {
  fs.readdirSync(dir).forEach(file => {
    const fullPath = path.join(dir, file);
    if (fs.statSync(fullPath).isDirectory()) {
      walkDir(fullPath);
    } else if (
      fullPath.endsWith('.vue') ||
      fullPath.endsWith('.scss') ||
      fullPath.endsWith('.css')
    ) {
      replaceGapInStyle(fullPath);
    }
  });
}

// 替换样式文件中的 gap/column-gap
function replaceGapInStyle(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');

  // 替换 gap: 15rpx 20rpx; 为 @include flex-gap(15rpx, 20rpx);
  content = content.replace(
    /^\s*gap\s*:\s*([0-9]+[a-zA-Z%]+)\s+([0-9]+[a-zA-Z%]+)\s*;/gm,
    (match, rowGap, colGap) => {
      return `  @include flex-gap(${rowGap}, ${colGap}); // 替换了 gap 双值`;
    }
  );

  // 替换 gap: 20rpx; 为 @include flex-gap(20rpx);
  content = content.replace(
    /^\s*gap\s*:\s*([0-9]+[a-zA-Z%]+)\s*;/gm,
    (match, gap) => {
      return `  @include flex-gap(${gap}); // 替换了 gap 单值`;
    }
  );

  // 替换 column-gap: 20rpx; 为 @include flex-gap(0, 20rpx);
  content = content.replace(
    /^\s*column-gap\s*:\s*([0-9]+[a-zA-Z%]+)\s*;/gm,
    (match, colGap) => {
      return `  @include flex-gap(0, ${colGap}); // 替换了 column-gap`;
    }
  );

  // 替换 row-gap: 20rpx; 为 @include flex-gap(20rpx, 0);
  content = content.replace(
    /^\s*row-gap\s*:\s*([0-9]+[a-zA-Z%]+)\s*;/gm,
    (match, rowGap) => {
      return `  @include flex-gap(${rowGap}, 0); // 替换了 row-gap`;
    }
  );

  fs.writeFileSync(filePath, content, 'utf8');
}

// 执行
walkDir('./src');
console.log('gap/column-gap 批量替换完成！');