/**
 * uni-toast
*/
.myToast {
	.uni-toast {
		width: auto !important;
		color: #fff;
		background: rgba(0,0,0,0.7);
		padding: 60rpx 30rpx 29rpx;
		border-radius: 31rpx;
		
		.uni-toast__icon {
			margin: 0;
			width: 83rpx;
			height: 83rpx;
		}
		
		.uni-toast__content {
			font-weight: 600;
			font-size: 46rpx;
			color: #FFFFFF;
			margin: 0;
			white-space: nowrap;
		}
	}	
}

/**
 * 默认toast
*/
.my-toast-box {
	.uni-toast {
		width: auto;
		padding: 0 10px;
		
		.uni-icon_toast {
			&::before {
				font-size: 38px !important;
			}
		
		}	
	}
	
	.uni-sample-toast {
		.uni-simple-toast__text {
			padding: 10px;
		}
	}
}

/**
 * cell 组件的 border 颜色
*/
.u-cell {
	.u-line {
		border-color: #F7F7F7 !important;
	}
}

.common-box-style {
	background: #FFFFFF;
	border-radius: 31rpx;
}
		
		
 