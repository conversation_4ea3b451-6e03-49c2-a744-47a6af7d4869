import { mapGetters } from "vuex";
import {
  payCommonAuth,
  payCommonCancel,
  payCommonError,
  payCommonCreate,
  payPalCapture,
} from "@/api/orderDetails.js";

export default {
  computed: {
    ...mapGetters(["affirm_public_api_key", "locale", "country_code"]),
  },
  methods: {
    loadAffirmJs() {
      return new Promise((resolve, reject) => {
        if (window._affirm_config) {
          resolve();
          return;
        }

        const script = document.createElement("script");
        script.src = "https://cdn1-sandbox.affirm.com/js/v2/affirm.js"; // 使用 Affirm 提供的最新 URL
        script.onload = () => {
          console.log("Affirm.js loaded successfully");
          resolve();
        };
        script.onerror = () => {
          console.error("Failed to load Affirm.js");
          reject(new Error("Failed to load Affirm.js"));
        };

        document.head.appendChild(script);
      });
    },
    loadAffirm(checkoutObject, orderNumber, OrderId, affirmCancelCallback) {
      return new Promise((resolve, reject) => {
        window._affirm_config = {
          public_api_key: this.affirm_public_api_key,
          locale: this.locale,
          country_code: this.country_code,
        };

        const that = this;
        affirm.ui.ready(function () {
          affirm.checkout(checkoutObject);
          affirm.checkout.open({
            onFail: function (error) {
              // Error handling
              console.log("error", error);
              that.isPaymentLoading = false;
              if (error.reason === "canceled") {
                payCommonCancel({
                  OId: OrderId,
                })
                  .then(() => {
                    that.$tab.redirectTo(
                      `/pages/tabs/mine/OrderStatus/OrderStatus?OrderId=${OrderId}`
                    );
                  })
                  .finally(() => {
                    resolve(true);
                  });
              } else {
                payCommonError({
                  OId: OrderId,
                  error: [error],
                })
                  .then(() => {
                    that.$tab.redirectTo(
                      `/pages/paymentResult/paymentResult?orderNumber=${orderNumber}&OrderId=${OrderId}&isResult=false`
                    );
                  })
                  .finally(() => {
                    resolve(true);
                  });
              }
            },
            onSuccess: function (checkout) {
              that.isPaymentLoading = false;
              // Success procedures
              console.log("Success procedures", checkout);

              // 授权支付
              payCommonAuth({
                OId: OrderId,
                checkout_token: checkout.checkout_token,
              })
                .then((res) => {
                  console.log("结果", res, OrderId);
                  that.$tab.redirectTo(
                    `/pages/paymentResult/paymentResult?orderNumber=${orderNumber}&OrderId=${OrderId}&isResult=true`
                  );
                })
                .finally(() => {
                  resolve(true);
                });
            },
            onOpen: function (token) {
              // Initialization successful
              console.log("token", token);
            },
            onValidationError: function (checkout_validation_error) {
              // Validation error handling
              console.log(
                "checkout_validation_error",
                checkout_validation_error
              );
              that.isPaymentLoading = false;
              resolve(true);
            },
          });
        });
      });
    },
    /**
     * 	1 Paypal支付
      84 Affirm支付
      86 Stripe New支付
      87 Authorize_Net支付
      88 Wallets钱包支付 (Stripe钱包支付)
      89 Amazon Pay支付
    */
    paymentCommonCreateFn(payData) {
      return new Promise(async (resolve, reject) => {
        let payCommonData = {
          OId: payData?.OId,
          PId: payData?.PId,
        };

        if (payData.PId == 84) {
          await this.loadAffirmJs();
        }

        if (payData.PId == 87) {
          const {
            IssuingBank,
            CardNo,
            CardSecurityCode,
            CardExpireMonth,
            CardExpireYear,
          } = payData?.authorized;
          payCommonData["authorized[IssuingBank]"] =
            this.$rsaEncrypt(IssuingBank);
          payCommonData["authorized[CardNo]"] = this.$rsaEncrypt(CardNo);
          payCommonData["authorized[CardSecurityCode]"] =
            this.$rsaEncrypt(CardSecurityCode);
          payCommonData["authorized[CardExpireMonth]"] =
            this.$rsaEncrypt(CardExpireMonth);
          payCommonData["authorized[CardExpireYear]"] =
            this.$rsaEncrypt(CardExpireYear);
        }

        console.log("payCommonData", payCommonData);

        payCommonCreate(payCommonData)
          .then(async (data) => {
            if (payData?.PId == 1) {
              // this.$tab.redirectTo(
              // 	`/pages/paypal/paypal?paymentNumber=${data.orderID}&OId=${payData?.OId}&OrderId=${payData?.OrderId}`
              // 	)
              const paymentNumber = data.orderID;
              console.log("PayPal payment number resolved:", paymentNumber);

              if (!paymentNumber) {
                console.error(
                  "Error: No payment number found in response",
                  data
                );
              }

              // 返回一个对象而非字符串，便于后续处理
              resolve({
                paymentNumber: paymentNumber,
                OId: payData?.OId,
                OrderId: payData?.OrderId,
                data: data,
              });
            }

            if (payData.PId == 84) {
              await this.loadAffirm(data, payData?.OId, payData?.OrderId);
            }

            if (payData.PId == 87) {
              this.$tab.redirectTo(
                `/pages/paymentResult/paymentResult?orderNumber=${payData?.OId}&OrderId=${payData?.OrderId}&isResult=true`
              );
            }

            // 钱包支付处理 (Stripe钱包支付)
            if (payData.PId == 88) {
              console.log('🎯 钱包支付数据处理:', data);

              // 钱包支付使用与Stripe相同的数据结构
              // 返回包含client_secret的数据，供StripeButton组件使用
              resolve({
                client_secret: data.client_secret,
                payment_intent_id: data.payment_intent_id,
                OId: payData?.OId,
                OrderId: payData?.OrderId,
                paymentType: 'wallet', // 标识为钱包支付
                data: data
              });
              return; // 提前返回，不执行后续逻辑
            }

            if (payData.PId == 89) {
              this.$store.commit("paymentStore/SET_AMAZON_PAY_DATA", data);
            }
            resolve(data);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
  },
};
