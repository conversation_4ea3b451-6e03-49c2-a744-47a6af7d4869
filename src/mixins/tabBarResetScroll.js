// tabBar 页面滚动重置 mixin
export default {
  onShow() {
    // 只对非首页的 tabBar 页面重置滚动位置
    // 获取当前页面路径
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const route = currentPage.route;
    
    // 如果不是首页，才重置滚动位置
    if (route !== 'pages/tabs/index/index') {
      // tabBar 页面切换时重置滚动位置
      // 使用 setTimeout 确保在页面完全激活后执行
      setTimeout(() => {
        uni.pageScrollTo({
          scrollTop: 0,
          duration: 0
        });
      }, 50);
    }
  }
} 