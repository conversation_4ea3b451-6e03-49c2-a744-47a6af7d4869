export default {
  onLoad() {
    // 监听物理返回键（Android）
    // #ifdef APP-PLUS
    this.backButtonListener = () => {
      console.log('Physical back button pressed');
      this.handleBackButton();
      return true; // 阻止默认返回行为
    };
    
    // 获取当前页面对象并设置监听器
    const currentWebview = this.$scope.$getAppWebview();
    if (currentWebview) {
      currentWebview.addEventListener('backbutton', this.backButtonListener, false);
    }
    // #endif
  },

  onUnload() {
    // 移除物理返回键监听器
    // #ifdef APP-PLUS
    if (this.backButtonListener) {
      const currentWebview = this.$scope.$getAppWebview();
      if (currentWebview) {
        currentWebview.removeEventListener('backbutton', this.backButtonListener, false);
      }
    }
    // #endif
  },

  methods: {
    // 处理返回按钮点击
    handleBackButton() {
      // 使用智能后退：有历史记录则后退，无记录则退出商城
      this.$tab.navigateBack();
    },

    // 强制使用原生后退（用于特殊情况）
    forceBack() {
      this.$tab.forceBack();
    },

    // 手动退出商城
    exitStore() {
      this.$tab.exitStore();
    },

    // 获取路由历史记录（用于调试）
    getRouteHistory() {
      return this.$tab.getHistory();
    },

    // 获取当前路由（用于调试）
    getCurrentRoute() {
      return this.$tab.getCurrentRoute();
    }
  }
}; 