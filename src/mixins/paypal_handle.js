import PaypalButton from "@/components/PaypalButton.vue";
import { payPalCapture, payPalCancel } from "@/api/orderDetails";
export default {
  components: {
    PaypalButton,
  },
  data() {
    return {
      // PayPal specific data
      isPaypalView: false,
      isPaymentPayPalLoad: false,
      isPaypalPageOpacity: 0,
      isPayPalFullScreen: false,
      previousIframeStyle: null,
      orderData: null,
    };
  },
  methods: {
    /**
     * Handle payment approval
     */
    onPaymentApprove(data) {
      console.log("Payment approved", data);
      this.isPaypalView = false;

      // Extract payment details
      const paymentDetails = {
        orderID: data.orderID,
        payerID: data.payerID,
        paymentID: data.paymentID || "",
        orderNo: this.orderData?.orderNo,
      };

      // Show success message
      uni.showToast({
        title: "Payment successful!",
        icon: "success",
      });

      // Reload order details or navigate to success page
      setTimeout(() => {
        this.getOrderInfo();
      }, 1000);
    },
    onPaymentSuccess(data) {
      this.$showLoading();
      payPalCapture({
        ...data,
      })
        .then((res) => {
          this.$toast({
            title: "Payment success",
          });
          this.$tab.redirectTo(
            `/pages/paymentResult/paymentResult?orderNumber=${this.payData.OId}&OrderId=${this.payData.paymentNumber}&isResult=true`
          );
        })
        .finally(() => {
          this.$hideLoading();
        });
    },
    /**
     * Handle payment cancellation
     */
    onPaymentCancel() {
      this.$showLoading();
      payPalCancel({
        orderID: this.payData.paymentNumber,
        OId: this.payData.OId,
      })
      .then((res) => {
          this.$toast({
            title: "Cancel payment",
          });

          this.$tab.redirectTo(
            `/pages/tabs/mine/OrderStatus/OrderStatus?OrderId=${this.payData.OId}`
          );
        })
        .finally(() => {
          this.$hideLoading();
        });
    },

    /**
     * Handle payment errors
     */
    onPaymentError(error) {
      uni.showToast({
        title: "Payment failed",
        icon: "none",
      });
    },

    /**
     * Adjust PayPal UI for fullscreen display
     */
    onOpenView() {
      // For PayPal SDK UI adjustments
      const paypalCheckoutSandboxEle = document.querySelector(
        ".paypal-checkout-sandbox"
      );
 
      const paypalCheckoutIframeEle2 = document.querySelector(
        ".paypal-checkout-sandbox iframe"
      );

      if (paypalCheckoutSandboxEle) {
        paypalCheckoutSandboxEle.style.height = `calc(100vh - ${this.statusBarAndNavHeight}px)`;
        paypalCheckoutSandboxEle.style.top = this.statusBarAndNavHeight + "px";
      }

      if (paypalCheckoutIframeEle2) {
        paypalCheckoutIframeEle2.style.height = `calc(100vh - ${this.statusBarAndNavHeight}px)`;
      }

      // Add a flag to indicate fullscreen mode
      this.isPayPalFullScreen = true;
      this.isPaypalPageOpacity = 1;
    },
    onOrderDetailPaymentCancel(){
      this.$hideLoading();
    },
    /**
     * Adjust PayPal UI styles if needed
     */
    adjustPaypalUIStyles() {
      // Add any specific styling requirements for your UI
      const paypalElements = document.querySelectorAll(
        ".paypal-overlay-context-popup"
      );
      if (paypalElements && paypalElements.length > 0) {
        paypalElements.forEach((el) => {
          // Apply custom styling to PayPal elements if needed
          if (this.statusBarAndNavHeight) {
            el.style.top = this.statusBarAndNavHeight + "px";
            el.style.height = `calc(100vh - ${this.statusBarAndNavHeight}px)`;
          }
        });
      }

      // Also apply opener view styles
      this.onOpenView();
    },
  },
};
