/**
 * SDK检查混入 - 每个页面都会自动检查SDK状态
 * 实现全局的路由级别SDK检查
 */

import { triggerSDKCheck } from '@/utils/payment/routeSDKGuard.js';

export default {
  name: 'SDKCheckMixin',
  
  onShow() {
    // 页面显示时触发SDK检查
    const currentPage = getCurrentPages().pop();
    const pagePath = currentPage?.route || 'unknown';
    
    console.log(`📱 页面显示 [${pagePath}]，触发SDK检查`);
    
    // 异步执行，不阻塞页面显示
    this.$nextTick(() => {
      triggerSDKCheck().catch(error => {
        console.warn('⚠️ 页面级SDK检查失败:', error);
      });
    });
  },
  
  onLoad() {
    // 页面加载时也触发一次检查（用于首次进入应用的页面）
    const currentPage = getCurrentPages().pop();
    const pagePath = currentPage?.route || 'unknown';
    
    console.log(`🔄 页面加载 [${pagePath}]，触发SDK检查`);
    
    // 延迟执行，让页面先完成基础加载
    setTimeout(() => {
      triggerSDKCheck().catch(error => {
        console.warn('⚠️ 页面加载时SDK检查失败:', error);
      });
    }, 100);
  }
};