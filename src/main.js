// 引入polyfills
import "core-js/stable";
import "regenerator-runtime/runtime";

// WebView安全优化工具 - 在应用启动时初始化
import webviewSafeOptimizer from '@/utils/webviewOptimization'

// 性能监控开始
const startTime = performance.now()
import App from "./App";
// #ifndef VUE3
import Vue from "vue";
import store from "./store";
import plugins from "./plugins"; // plugins
import "@/utils/bridge/communication";
import "@/utils/mainOption/mainDirective.js";
import preloadPaymentSDKs from "@/utils/payment/sdkPreloader";
import appBridge from "./utils/bridge/appBridge";
import uView from "@/uni_modules/uview-ui";
import config from "./config/index.js"; // 导入配置
import sdkCheckMixin from "@/mixins/sdkCheckMixin.js"; // SDK检查混入

// 开发环境导入桥接调试工具
if (process.env.NODE_ENV === 'development') {
  import("@/utils/bridge/bridgeDebug");
}
window.setDevConfig = (config) => {
  console.log("Dev Config Set Success");
  uni.setStorageSync('isDev', true);
};
// 预加载所有支付SDK
preloadPaymentSDKs();
Vue.prototype.$appBridge = appBridge;
Vue.prototype.$config = config; // 添加全局配置
Vue.use(uView);
Vue.use(plugins);

// 全局注册SDK检查混入
Vue.mixin(sdkCheckMixin);


// #ifdef H5
// 只在开发环境和测试环境启用 VConsole
if (process.env.VUE_APP_ENV === 'test') {
  import("vconsole").then(({ default: VConsole }) => {
    new VConsole({
      onReady: () => {
        const vcSwitch = document.querySelector(".vc-switch");
        const vcPanel = document.querySelector(".vc-panel");
        if (vcSwitch) {
          vcSwitch.style.bottom = "200px"; // 调整开关按钮的位置
        }
      },
    });
  });
} else {
  // 生产环境移除console输出，提升WebView性能
  console.log = () => { }
  console.warn = () => { }
  console.error = (error) => {
    // 只保留错误日志用于bug追踪
    if (window.reportError) {
      window.reportError(error)
    }
  }
}
// #endif

import "./uni.promisify.adaptor";
Vue.config.productionTip = false;
App.mpType = "app";

const app = new Vue({
  ...App,
  store,
  mounted() {
    // 应用挂载完成，记录性能数据
    const mountTime = performance.now() - startTime
    console.log(`🚀 应用启动耗时: ${mountTime.toFixed(2)}ms`)

    // 初始化WebView优化
    this.$nextTick(() => {
      // 尝试优化Three.js（如果已加载）
      webviewSafeOptimizer.optimizeThreeJS()

      // 开发环境输出性能报告
      if (process.env.NODE_ENV === 'development') {
        setTimeout(() => {
          webviewSafeOptimizer.getPerformanceReport()
        }, 2000)
      }
    })
  }
});
app.$mount();
// #endif

// #ifdef VUE3
import { createSSRApp } from "vue";
export function createApp() {
  const app = createSSRApp(App);
  return {
    app,
  };
}
// #endif
