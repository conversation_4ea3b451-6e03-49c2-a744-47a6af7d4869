import routeManager from "./routeManager";
import { toast, previewImage, showLoading, hideLoading } from "@/utils/common.js";
import { rsaEncrypt } from "@/utils/security/rsa.js"

export default {
	install(Vue) {
		// 路由管理器（增强版页签操作，支持历史记录管理和智能后退）
		Vue.prototype.$tab = routeManager;
		Vue.prototype.$toast = toast;
		Vue.prototype.$previewImage = previewImage;
		Vue.prototype.$showLoading = showLoading;
		Vue.prototype.$hideLoading = hideLoading;
		Vue.prototype.$rsaEncrypt = rsaEncrypt;
	}
}