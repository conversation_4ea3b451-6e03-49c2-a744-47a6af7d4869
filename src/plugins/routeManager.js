class RouteManager {
  constructor() {
    this.history = []; // 路由历史记录
    this.currentIndex = -1; // 当前路由索引
    this.isInitialized = false;
  }

  // 初始化路由管理器
  init() {
    if (this.isInitialized) return;

    // 获取当前页面路径作为初始页面
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      const currentRoute = "/" + currentPage.route;
      this.history = [currentRoute];
      this.currentIndex = 0;
    }

    this.isInitialized = true;
    console.log("RouteManager initialized:", this.history);
  }

  // 记录新的路由
  push(url) {
    this.init();

    // 如果当前不在历史记录的末尾，删除后面的记录
    if (this.currentIndex < this.history.length - 1) {
      this.history = this.history.slice(0, this.currentIndex + 1);
    }

    // 添加新路由到历史记录
    this.history.push(url);
    this.currentIndex++;

    console.log("Route pushed:", {
      url,
      history: this.history,
      currentIndex: this.currentIndex,
    });
  }

  // 后退
  back() {
    this.init();

    console.log("Attempting to go back:", {
      history: this.history,
      currentIndex: this.currentIndex,
    });

    // 如果当前索引大于0，可以后退
    if (this.currentIndex > 0) {
      this.currentIndex--;
      console.log("Going back to:", this.history[this.currentIndex]);
      return true; // 可以后退
    } else {
      // 没有历史记录，需要退出商城
      console.log("No more history, exiting store");
      this.exitStore();
      return false; // 无法后退，已退出
    }
  }

  // 替换当前路由
  replace(url) {
    this.init();

    if (this.history.length === 0) {
      this.history.push(url);
      this.currentIndex = 0;
    } else {
      this.history[this.currentIndex] = url;
    }

    console.log("Route replaced:", {
      url,
      history: this.history,
      currentIndex: this.currentIndex,
    });
  }

  // 清空历史记录并设置新路由
  reLaunch(url) {
    this.history = [url];
    this.currentIndex = 0;
    console.log("Route relaunched:", { url, history: this.history });
  }

  // 切换到TabBar页面
  switchTab(url) {
    this.init();

    // TabBar页面切换时，清空历史记录
    this.history = [url];
    this.currentIndex = 0;
    console.log("Tab switched:", { url, history: this.history });
  }

  // 退出商城
  exitStore() {
    try {
      // 检查是否在开发环境
      const isDev = process.env.NODE_ENV === "development";

      // 获取平台信息
      const systemInfo = uni.getSystemInfoSync();
      const platform = systemInfo.platform;

      console.log(
        "Attempting to exit store, platform:",
        platform,
        "isDev:",
        isDev
      );

      // 开发环境处理
      if (isDev && platform === "web") {
        console.log("🚀 开发环境模拟：退出商城");
        uni.showModal({
          title: "开发环境提示",
          content: "这里应该退出商城\n(在真机环境中会调用原生方法)",
          showCancel: false,
          confirmText: "知道了",
        });
        return;
      }

      // 生产环境或真机环境
      if (platform === "android") {
        // Android平台：优先尝试exitStore
        if (window.zlbridge) {
          if (typeof window.zlbridge.exitStore === "function") {
            window.zlbridge.exitStore();
            console.log("Android exitStore called successfully");
            return;
          } else if (typeof window.zlbridge.goPreviousPage === "function") {
            // 备用方案：使用goPreviousPage
            console.warn(
              "exitStore not available, using goPreviousPage as fallback"
            );
            window.zlbridge.goPreviousPage();
            return;
          } else {
            console.warn(
              "Neither exitStore nor goPreviousPage are available on zlbridge"
            );
          }
        } else {
          console.warn("window.zlbridge is not available");
        }
      } else if (platform === "ios") {
        // iOS平台：通过webkit消息处理器
        if (
          window.webkit &&
          window.webkit.messageHandlers &&
          window.webkit.messageHandlers.exitStore
        ) {
          window.webkit.messageHandlers.exitStore.postMessage(null);
          console.log("iOS exitStore called successfully");
          return;
        } else {
          console.warn(
            "window.webkit.messageHandlers.exitStore is not available"
          );
        }
      }

      // 如果是真机环境但原生方法不可用，显示提示
      console.warn(
        "Native exit methods not available, showing fallback message"
      );
      uni.showModal({
        title: "提示",
        content: "无法退出商城，请手动关闭应用",
        showCancel: false,
        confirmText: "知道了",
      });
      location.href = window.location.origin;
    } catch (error) {
      console.error("Failed to exit store:", error);
      // 显示用户友好的错误提示
      uni.showModal({
        title: "提示",
        content: "退出商城功能暂时不可用",
        showCancel: false,
        confirmText: "知道了",
      });
    }
  }

  // 获取当前路由
  getCurrentRoute() {
    if (this.currentIndex >= 0 && this.currentIndex < this.history.length) {
      return this.history[this.currentIndex];
    }
    return null;
  }

  // 获取历史记录
  getHistory() {
    return [...this.history];
  }

  // 清空历史记录
  clearHistory() {
    this.history = [];
    this.currentIndex = -1;
  }
}

// 创建单例实例
const routeManager = new RouteManager();

// 增强版的导航方法
export default {
  // 保留当前页面，跳转到应用内的某个页面
  navigateTo(url) {
    if (url.indexOf('pages/order_details/order_details') !== -1) {
      uni.removeStorageSync('paymentOrderId');
    }
    routeManager.push(url);
    return uni.navigateTo({
      url: url,
    });
  },

  // 关闭当前页面，跳转到应用内的某个页面
  redirectTo(url) {
    routeManager.replace(url);
    return uni.redirectTo({
      url: url,
    });
  },

  // 关闭所有页面，打开到应用内的某个页面
  reLaunch(url) {
    routeManager.reLaunch(url);
    return uni.reLaunch({
      url: url,
    });
  },

  // 跳转到tabBar页面，并关闭其他所有非tabBar页面
  switchTab(url) {
    routeManager.switchTab(url);
    return uni.switchTab({
      url: url,
      success: () => { },
    });
  },

  // 智能后退：有历史记录则后退，无记录则退出商城
  navigateBack() {
    return uni.navigateBack();
  },

  // 强制后退（原生后退）
  forceBack() {
    return uni.navigateBack();
  },

  // 路由管理器相关方法
  getCurrentRoute() {
    return routeManager.getCurrentRoute();
  },

  getHistory() {
    return routeManager.getHistory();
  },

  clearHistory() {
    routeManager.clearHistory();
  },

  // 手动退出商城
  exitStore() {
    routeManager.exitStore();
  },
};
