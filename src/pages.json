{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/tabs/index/index",
			"style": {
				"navigationBarTitleText": "Shop",
				// "transparentTitle": "auto",
				"navigationStyle": "custom",
				"enablePullDownRefresh": true
				// "h5": {
				// 	"titleNView": {
				// 		"buttons": [{
				// 			"float": "left",
				// 			"type": "back"
				// 		}]
				// 	}
				// }
			}
		},
		{
			"path": "pages/tabs/sort/sort",
			"style": {
				"navigationBarTitleText": "Shop"
			}
		},
		{
			"path": "pages/tabs/cart/cart",
			"style": {
				"navigationBarTitleText": "Shop"
			}
		},
		{
			"path": "pages/tabs/mine/mine",
			"style": {
				"navigationBarTitleText": "Shop",
				"disableScroll": true
			}
		},
		{
			"path": "pages/sortGoods/sortGoods",
			"style": {
				"navigationBarTitleText": "Shop"
			}
		},
		{
			"path": "pages/tabs/mine/myFavorite/myFavorite",
			"style": {
				"navigationBarTitleText": "Shop"
			}
		},
		{
			"path": "pages/tabs/mine/myRecords/myRecords",
			"style": {
				"navigationBarTitleText": "Shop"
			}
		},
		{
			"path": "pages/tabs/mine/myCoupons/myCoupons",
			"style": {
				"navigationBarTitleText": "Shop",
				"transparentTitle": "auto"
			}
		},
		{
			"path": "pages/tabs/mine/myOrder/myOrder",
			"style": {
				"navigationBarTitleText": "Shop"
			}
		},
		{
			"path": "pages/tabs/mine/OrderStatus/OrderStatus",
			"style": {
				"navigationBarTitleText": "Shop"
				// "navigationStyle": "custom" a
			}
		},
		{
			"path": "pages/tabs/mine/logisticsDetail/logisticsDetail",
			"style": {
				"navigationBarTitleText": "Shop",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/tabs/mine/orderReview/orderReview",
			"style": {
				"navigationBarTitleText": "Shop"
			}
		},
		{
			"path": "pages/tabs/cart/addressBook/addressBook",
			"style": {
				"navigationBarTitleText": "Shop"
			}
		},
		{
			"path": "pages/tabs/cart/addAddress/addAddress",
			"style": {
				"navigationBarTitleText": "Shop"
			}
		},
		{
			"path": "pages/tabs/mine/services/services",
			"style": {
				"navigationBarTitleText": "services"
			}
		},
		{
			"path": "pages/tabs/mine/CoinsRecord/CoinsRecord",
			"style": {
				"navigationBarTitleText": "Shop"
			}
		},
		{
			"path": "pages/order_details/order_details",
			"style": {
				"navigationBarTitleText": "Shop"
			}
		},
		{
			"path": "pages/paymentResult/paymentResult",
			"style": {
				"navigationBarTitleText": "Result of payment",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/goodsDetail/goodsDetail",
			"style": {
				"navigationBarTitleText": "Shop"
				// "transparentTitle": "auto",
				// "h5": {
				// 	"titleNView": {
				// 		"buttons": [{
				// 			"float": "left",
				// 			"type": "back"
				// 		}]
				// 	}
				// }
			}
		},
		{
			"path": "pages/review/review",
			"style": {
				"navigationBarTitleText": "Shop"
			}
		},
		{
			"path": "pages/QA/QA",
			"style": {
				"navigationBarTitleText": "Shop"
			}
		},
		{
			"path": "pages/qaQuestion/qaQuestion",
			"style": {
				"navigationBarTitleText": "Shop"
			}
		},
		{
			"path": "pages/Search/Search",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/previewer/previewer",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/blank/blank",
			"style": {
				"navigationBarTitleText": ""
			}
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "Shop",
		"navigationBarBackgroundColor": "#F0F0F0",
		"backgroundColor": "#F8F8F8",
		"navigationStyle": "custom"
	},
	"tabBar": {
		"borderStyle": "white",
		"backgroundColor": "#ffffff",
		"iconWidth": "54rpx",
		"height": "94rpx",
		"list": [
			{
				"pagePath": "pages/tabs/index/index",
				"iconPath": "static/assets/tabs/home.png",
				"selectedIconPath": "static/assets/tabs/home_selected.png"
			},
			{
				"pagePath": "pages/tabs/sort/sort",
				"iconPath": "static/assets/tabs/sort.png",
				"selectedIconPath": "static/assets/tabs/sort_selected.png"
			},
			{
				"pagePath": "pages/tabs/cart/cart",
				"iconPath": "static/assets/tabs/cart.png",
				"selectedIconPath": "static/assets/tabs/cart_selected.png"
			},
			{
				"pagePath": "pages/tabs/mine/mine",
				"iconPath": "static/assets/tabs/mine.png",
				"selectedIconPath": "static/assets/tabs/mine_selected.png"
			}
		]
	},
	"uniIdRouter": {}
}