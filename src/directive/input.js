const inputLimit = {
	// 当指令绑定到元素时调用
	bind(el, binding, vnode) {
		const {
			min,
			max,
			decimalPlaces,
			allowNegative
		} = binding.value;


		const handleInput = (event) => {
			let value = event.target.value;

			// 限制负数
			if (!allowNegative && value.startsWith('-')) {
				value = value.replace(/^-/, '');
			}

			// 正整数
			if (decimalPlaces === 0) {
				value = value.replace(/[^\d]/g, '');
			}

			// 限制小数位数
			if (decimalPlaces > 0) {
				value = value.match(/^\d*(\.?\d{0,`${decimalPlaces}`})/g)[0];
			}

			// 转换为数字
			const numValue = parseFloat(value);

			// 限制最大值和最小值
			if (!isNaN(numValue)) {
				if (numValue > max) {
					value = max.toString();
				} else if (numValue < min) {
					value = min.toString();
				}
			}

			// 更新输入框的值
			event.target.value = value;
		};

		// 监听 input 事件
		el.querySelector('input').addEventListener('input', handleInput);
	},
	// 当指令值更新时调用
	update(el, binding) {
		// 如果最大值或最小值更新，重新绑定事件
		if (binding.oldValue.max !== binding.value.max || binding.oldValue.min !== binding.value.min) {
			el._v_model.value = el.value; // 更新绑定的值
		}
	}
}


export {
	inputLimit
}