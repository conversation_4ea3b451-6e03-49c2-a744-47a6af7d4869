import request from '@/utils/request';

// 确认订单页面
export function getCartCheckout(data) {
	return request({
		url: "/app-store/cart/checkout",
		method: 'post',
		data
	})
}

// 获取物流方式
export function getLogistics(data) {
	return request({
		url: "/app-store/cart/get-logistics-mode",
		method: 'post',
		data
	})
}

// 获取优惠券
export function getCoupon(data) {
	return request({
		url: "/app-store/cart/get-coupon/",
		method: 'post',
		data
	})
}

// 使用优惠券
export function useCoupon(data) {
	return request({
		url: "/app-store/cart/use-coupon",
		method: 'post',
		data
	})
}

// 清除使用优惠券
export function clearCoupon(data) {
	return request({
		url: "/app-store/cart/del-coupon",
		method: 'post',
		data
	})
}

// 使用卡券
export function useCard(data) {
	return request({
		url: "/app-store/cart/use-card/",
		method: 'post',
		data
	})
}

// 推荐加购商品
export function getRecommendAddCart(data) {
	return request({
		url: "/app-store/products/get-recommend-products",
		method: 'post',
		data
	})
}

// 生成订单
export function createOrder(data) {
	return request({
		url: "/app-store/cart/create-order",
		method: 'post',
		data
	})
}

// 获取付款通道
export function getPayChannels() {
	return request({
		url: "/app-store/cart/get-payment",
		method: 'get'
	})
}

// 支付订单创建
export function payCommonCreate(data) {
	return request({
		url: "/app-store/payment/pay-common-create",
		method: 'post',
		data
	})
}

// 订单详情
export function getOrderDetail(data) {
	return request({
		url: "/app-store/user/order-detail",
		method: 'post',
		data
	})
}

// 授权支付
export function payCommonAuth(data) {
	return request({
		url: "/app-store/payment/affirm-capture",
		method: 'post',
		data
	})
}

// 取消支付
export function payCommonCancel(data) {
	return request({
		url: "/app-store/payment/affirm-cancel",
		method: 'post',
		data
	})
}

// 支付出错
export function payCommonError(data) {
	return request({
		url: "/app-store/payment/affirm-error",
		method: 'post',
		data
	})
}

// 商品详情-评论列表
export function getCommentList(params) {
	return request({
		url: "/app-store/products/reviews",
		method: 'get',
		params
	})
}

// 商品详情-咨询问题列表
export function getConsultList(params) {
	return request({
		url: "/app-store/products/questions",
		method: 'get',
		params
	})
}

// 商品详情-详情介绍
export function getProductsContent(params) {
	return request({
		url: "/app-store/products/content",
		method: 'get',
		params
	})
}

// 商品详情-获取组合商品
export function getProductsGroup(data) {
	return request({
		url: "/app-store/products/combine",
		method: 'post',
		data
	})
}

// 购物车赠品列表
export function getCartGiftList(data) {
	return request({
		url: "/app-store/cart/gift-product/",
		method: 'post',
		data
	})
}

// 订单物流（注册）
export function getOrderTrackRegister(data) {
	return request({
		url: "/app-store/user/order-track-register/",
		method: 'post',
		data
	})
}

// 订单物流(查询)
export function getOrderTrackDetail(data) {
	return request({
		url: "/app-store/user/order-track-detail/",
		method: 'post',
		data
	})
}

// 商品详情-评论/追评 点赞
export function commentLike(data) {
	return request({
		url: "/app-store/products/reviews-add-like",
		method: 'post',
		data
	})
}

// 商品详情-添加评论
export function addComment(data) {
	return request({
		url: "/app-store/products/add-reviews/",
		method: 'post',
		data,
		'contentType': 'multipart/form-data'
	})
}

// 商品详情-添加追评
export function addCommentAgain(data) {
	return request({
		url: "/app-store/products/add-append-reviews",
		method: 'post',
		data
	})
}

// 商品详情-咨询问题列表
export function addQuestionList(params) {
	return request({
		url: "/app-store/products/questions/",
		method: 'get',
		params
	})
}

// 商品详情-提交咨询问题
export function addQuestion(data) {
	return request({
		url: "/app-store/products/questions-submit",
		method: 'post',
		data
	})
}

// 商品详情-回复咨询问题
export function replyQuestion(data) {
	return request({
		url: "/app-store/products/questions-answer",
		method: 'post',
		data
	})
}

// 商品详情-点赞/取消点赞 咨询回复
export function likeQuestion(data) {
	return request({
		url: "/app-store/products/like-answer",
		method: 'post',
		data
	})
}

// paypal捕获支付订单
export function payPalCapture(data) {
	return request({
		url: "/app-store/payment/paypal-capture/",
		method: 'post',
		data
	})
}

// paypal取消支付订单
export function payPalCancel(data) {
	return request({
		url: "/app-store/payment/paypal-cancel/",
		method: 'post',
		data
	})
}

// 获取金币抵扣信息
export function getGoldInfo() {
	return request({
		url: "/app-store/cart/get-gold-law/",
		method: 'post'
	})
}

// stripe捕获支付订单
export function stripeCapture(data) {
	return request({
		url: "/app-store/payment/stripe-new-capture/",
		method: 'post',
		data
	})
}
// 订单状态查询
export function getOrderStatusDetail(data) {
	return request({
		url: "/app-store/user/order-status-detail",
		method: 'post',
		data
	})
}

// Amazon Pay 捕获支付
export function amazonPayCapture(params) {
	return request({
		url: "/app-store/payment/amazonPay-capture",
		method: 'get',
		params
	})
}