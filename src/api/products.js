import request from '@/utils/request';
import { getRawData, postRawData } from '@/utils/request';

// 规格属性弹窗
export function getCartPopCart(data) {
	return request({
		url: "/app-store/cart/pop-cart",
		method: 'post',
		data
	})
}

// 规格属性选择
export function getCartSelectPopCart(data) {
	return request({
		url: "/app-store/cart/select-pop-cart",
		method: 'post',
		data
	})
}

// 加入购物车
export function getCartAddToCart(data) {
	return request({
		url: "/app-store/cart/add-to-cart",
		method: 'post',
		data
	})
}

// 3D商品加入购物车 - 如果这个接口需要返回原始data，可以这样使用
export function add3DToCart(data) {
	return request({
		url: "/app-store/cart/add-to-cart/",
		method: 'post',
		data,
		// 如果这个接口返回格式特殊，需要原始data，可以取消下面的注释
		returnRawData: true
	})
}

// 示例：使用辅助函数的方式（如果某个接口需要返回原始data）
// export function add3DToCartRaw(data) {
// 	return postRawData("/app-store/cart/add-to-cart/", data);
// }

// 购物车商品项列表
export function getCartCartItemList(data) {
	return request({
		url: "/app-store/cart/cart/",
		method: 'post',
		data
	})
}

// 新增：组合促销加入购物车
export function packageAddToCart(data) {
	return request({
		url: "/app-store/cart/package-add-to-cart/",
		method: 'post',
		data
	})
}

// 编辑购物车商品项
export function getCartEditCart(data) {
	return request({
		url: "/app-store/cart/modify-cart",
		method: 'post',
		data
	})
}

// 批量删除购物车商品项
export function getCartBatchDelete(data) {
	return request({
		url: "/app-store/cart/bacth-delete-to-cart",
		method: 'post',
		data
	})
}

// 商品详情-获取组合商品
export function getCartGetCombination(data) {
	return request({
		url: "/app-store/products/combine",
		method: 'post',
		data
	})
}

// 浏览记录
export function getCartGetHistory(data) {
	return request({
		url: "/app-store/user/browser-history/",
		method: 'post',
		data
	})
}

// 商品搜索-搜索记录
export function getSearchHistory(params) {
	return request({
		url: "/app-store/collections/search-log/",
		method: 'get',
		params
	})
}