import request from '@/utils/request';

// 用户地址
export function userAddress(data) {
	return request({
		url: "/app-store/user/user-address",
		method: 'post',
		data
	})
}

// 获取国家
export function getCountry() {
	return request({
		url: "/app-store/user/get-country/",
		method: 'post'
	})
}


// 地址联动
export function addressSelectCountry(data) {
	return request({
		url: "/app-store/user/address-select-country",
		method: 'post',
		data
	})
}

// 新增、编辑地址
export function addressAdd(data) {
	return request({
		url: "/app-store/user/address-increase",
		method: 'post',
		data
	})
}

// 删除地址
export function addressDelete(data) {
	return request({
		url: "/app-store/user/address-delete",
		method: 'post',
		data
	})
}

// 设置默认地址
export function setDefaultAddress(data) {
	return request({
		url: "/app-store/user/address-default/",
		method: 'post',
		data
	})
}