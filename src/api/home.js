import request from '@/utils/request';

// 首页
export function getHomeBike() {
	return request({
		url: ""
	})
}


// 商品列表
export function getArrivalList(params) {
	return request({
		url: "/app-store/collections/products/",
		method: 'get',
		params
	})
}

// 商城系列导航
export function collectionsList(data) {
	return request({
		url: "/app-store/collections/list",
		method: 'post',
		data
	})
}

// 收藏
export function productFavorite(data) {
	return request({
		url: "/app-store/products/product-favorite",
		method: 'post',
		data
		// header: {
		// 	"Content-Type" : "application/x-www-form-urlencoded"
		// },
	})
}

// 商品详情
export function productDetail(params) {
	return request({
		url: "/app-store/products",
		method: 'get',
		params
	})
}

// 购物车跳转商品详情
export function productDetailCart(params) {
	return request({
		url: "/app-store/cart/cart-transfer-products",
		method: 'get',
		params
	})
}

// 会员中心页面 我的
export function getUserCenterInfo() {
	return request({
		url: "/app-store/user/user-center-info/",
		method: 'post'
	})
}

// 我的收藏
export function getMyFavorite(params) {
	return request({
		url: "/app-store/user/user-favorite/",
		method: 'get',
		params
	})
}

// 删除收藏
export function deleteFavorite(data) {
	return request({
		url: "/app-store/user/favorite-delete/",
		method: 'post',
		data
	})
}

// 我的优惠券
export function getMyCoupon(data) {
	return request({
		url: "/app-store/user/user-coupon/",
		method: 'post',
		data
	})
}

// 我的订单列表
export function getMyOrderList(data) {
	return request({
		url: "/app-store/user/order-lists/",
		method: 'post',
		data
	})
}

// 首页
export function getHome() {
	return request({
		url: "/app-store/",
		method: 'get'
	})
}