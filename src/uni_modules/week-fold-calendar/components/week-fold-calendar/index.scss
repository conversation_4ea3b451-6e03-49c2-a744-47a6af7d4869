.controller {
	position: relative;
	margin-bottom: 30rpx;
	padding:61rpx 0;
	.today-btn {
		position: absolute;
		right: 40rpx;
		top: 50%;
		transform: translateY(-50%);
		font-size: 24rpx;
	}
	.action {
		display: flex;
		justify-content: center;
		align-items: center;
		.date {
			width: 240rpx;
			font-size: 36rpx;
			text-align: center;
			flex:1;
		}
		.arrow {
			width: 150rpx;
			height: 40rpx;
			text-align: center;
			&.disabled {
				opacity: 0.2;
			}
			&.right {
				transform: rotate(180deg);
			}
			.icon {
				width: 36rpx;
				height: 36rpx;
				margin: 0 auto;
				display: flex;
				justify-content: center;
				align-items: center;
				position: relative;
				&::after {
					content: '';
					height: 16rpx;
					width: 16rpx;
					top: 12rpx;
					border-width: 0 0 2rpx 2rpx;
					border-color: #000;
					border-style: solid;
					transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
					position: absolute;
				}
			}
		}
	}
}

.calender {
	height: 622rpx;
	overflow: hidden;
	transition: height 0.2s ease;
	&.month {
		height: 622rpx;
	}
	&.week {
		height: 150rpx;
	}
	.week-box {
		display: grid;
		grid-template-columns: repeat(7, 1fr);
		font-weight: bold;
		.week {
			width: 70rpx;
			color: #ccc;
			text-align: center;
			margin: 0 auto;
		}
	}

	.day-box {
		margin-top: 60rpx;
		position: relative;
	}

	.days,
	.placeholder {
		display: grid;
		grid-template-columns: repeat(7, 1fr);
		@include flex-gap(16rpx); // 替换了 gap 单值
		font-weight: bold;

		.item {
			width: 57rpx;
			height: 58rpx;
			line-height: 1;
			text-align: center;
			display: flex;
			justify-content: center;
			align-items: center;
			background: #fff;
			border-radius: 50%;
			transition: all 0.2s ease;
			margin: 0 auto;
			font-size: 28rpx;
			position: relative;

			&.in {
				background: #f2f2ff;
			}

			&.after,
			&.before,
			&.prev,
			&.next {
				background: transparent !important;
				color: #d2d2d2 !important;
			}

			&.active {
				background: var(--active-bg) !important;
				color: var(--active) !important;
				transition: all 0.1s ease;
			}
			.lc {
				min-width: max-content;
				/* #ifndef H5 */
				font-size: 16rpx;
				margin-top: 4rpx;
				/* #endif */
				
				/* #ifdef H5 */
				font-size: 30rpx;
				transform: scale(0.5);
				/* #endif */
			}
			.stn {
				position: absolute;
				top: -12rpx;
				right: -14rpx;
				color: #2440b3;
				z-index: 2;
				
				/* #ifndef H5 */
				font-size: 16rpx;
				margin-top: 4rpx;
				/* #endif */
				
				/* #ifdef H5 */
				font-size: 30rpx;
				transform: scale(0.5);
				/* #endif */
			}
			.dots {
				width: 8rpx;
				height: 8rpx;
				border-radius: 50%;
				background: red;
				position: absolute;
				left: 50%;
				bottom: 0;
				transform: translateX(-50%);
			}
		}
	}

	.days {
		opacity: 1;
		transform: translateX(0);
		position: relative;
		z-index: 3;

		&.prev,
		&.next {
			animation: switch 0.3s ease forwards;
			animation-delay: 0.1s;
		}

		&.prev {
			transform: translateX(-100vw);
		}

		&.next {
			transform: translateX(100vw);
		}
	}

	.placeholder {
		position: absolute;
		width: 100%;
		height: 100%;
		left: 0;
		top: 0;
		z-index: 1;
		opacity: 0;

		&.prev,
		&.next {
			animation: opacity0 0.3s ease forwards;
		}
	}
}

.view-change-btn {
	text-align: center;
	color: #888;
	font-size: 26rpx;
	padding: 20rpx;
	position: relative;

	&::before,
	&::after {
		content: '';
		width: 260rpx;
		height: 1px;
		background: #f7f8f9;
		position: absolute;
		top: 50%;
		z-index: 1;
	}

	&::before {
		left: 50rpx;
	}

	&::after {
		right: 50rpx;
	}
}

@keyframes switch {
	0% {
		opacity: 0;
	}

	40% {
		opacity: 0;
	}

	100% {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes opacity0 {
	0% {
		opacity: 1;
	}

	100% {
		opacity: 0;
	}
}
