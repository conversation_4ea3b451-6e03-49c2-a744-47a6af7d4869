<template>
	<view class="content">
		<sk-container :safeAreaTop="false" :safeAreaBottom="false" :scroll="true">
			<sk-nav-bar :fixedTop="true" :safeAreaTop="true" :showBottomLine="true"></sk-nav-bar>
			<view style="padding: 30rpx;">
				<sk-swiper height='600rpx'></sk-swiper>
			</view>
			<sk-tabs height="88rpx"></sk-tabs>
			<sk-tabs  height="288rpx" length='2'></sk-tabs>
			<!-- <sk-menu cols="5"></sk-menu> -->
				<sk-tabs height="50rpx"></sk-tabs>
			<view style="padding: 30rpx 30rpx 60rpx 30rpx;">
				<sk-waterfall :waterfall="false"></sk-waterfall>
			</view>
		</sk-container>
	</view>
</template>

<script>
	// 
	import skContainer from '../../components/container.vue'
	import skNavBar from '../../components/nav-bar.vue'
	import skSwiper from '../../components/swiper.vue'
	import skWaterfall from '../../components/waterfall.vue'
	import skTabBar from '../../components/tab-bar.vue'
	import skMenu from '../../components/menu.vue'
	import skTabs from '../../components/tabs.vue'
	export default {
		components: {
			skTabBar,
			skWaterfall,
			skSwiper,
			skNavBar,
			skContainer,
			skMenu,
			skTabs
		},
		data() {
			return {

			}
		},
		onLoad() {

		},
		methods: {

		}
	}
</script>

<style>

</style>