{"id": "skeleton-ui", "displayName": "skeleton-ui（骨架屏UI）", "version": "1.1.3", "description": "skeleton-ui 是专用于 uni-app 生态的 骨架屏效果框架。致力于以简单便捷的方式实现 uni-app 生态应用的骨架屏加载效果。", "keywords": ["骨架屏", "加载效果", "skeleton"], "repository": "https://gitee.com/myDarling/skeleton-ui", "engines": {}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "", "type": "component-vue"}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"App": {"app-vue": "y", "app-nvue": "n"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "u", "百度": "u", "字节跳动": "u", "QQ": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}}