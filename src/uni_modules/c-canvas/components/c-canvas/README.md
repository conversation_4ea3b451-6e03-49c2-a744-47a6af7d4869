# c-canvas 绘制海报组件

一个用于绘制海报的Canvas组件，支持图片等比例缩放和文本边界处理。

## 功能特性

- ✅ 图片等比例缩放，避免变形
- ✅ 文本边界检查，防止溢出
- ✅ 支持圆角图片
- ✅ 支持多行文本和省略号
- ✅ 自动居中显示
- ✅ 错误处理和容错机制

## 使用方法

### 基本用法

```vue
<template>
  <view>
    <c-canvas 
      :canvas-id="'poster'" 
      :width="375" 
      :height="500"
      :keep-aspect-ratio="true"
      :draw-data="drawData"
      @drawSuccess="onDrawSuccess"
    />
  </view>
</template>

<script>
export default {
  data() {
    return {
      drawData: [
        // 背景图片 - 会自动保持比例
        {
          type: 'image',
          x: 0,
          y: 0,
          value: 'https://example.com/background.jpg',
          width: 375,
          height: 500
        },
        // 商品图片 - 保持比例居中显示
        {
          type: 'image',
          x: 20,
          y: 100,
          value: 'https://example.com/product.jpg',
          width: 150,
          height: 150,
          radius: 8 // 圆角
        },
        // 价格文本 - 自动处理边界
        {
          type: 'text',
          x: 20,
          y: 280,
          value: '$99.99',
          color: '#FF4444',
          font: 'bold 24px Arial',
          textAlign: 'left',
          lineHeight: 30,
          lineMaxWidth: 200,
          lineNum: 1
        },
        // 商品标题 - 多行文本
        {
          type: 'text',
          x: 20,
          y: 320,
          value: '这是一个很长的商品标题，会自动换行并添加省略号',
          color: '#333333',
          font: 'normal 16px Arial',
          textAlign: 'left',
          lineHeight: 20,
          lineMaxWidth: 335,
          lineNum: 2
        }
      ]
    }
  },
  methods: {
    onDrawSuccess(imagePath) {
      console.log('海报生成成功:', imagePath);
      // 可以保存或分享图片
    }
  }
}
</script>
```

## 属性说明

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| canvasId | String | 'myCanvas' | 画布ID |
| isAuto | Boolean | true | 是否自动绘制 |
| width | Number | 375 | 画布宽度 |
| height | Number | 375 | 画布高度 |
| keepAspectRatio | Boolean | true | 是否保持图片比例 |
| drawData | Array | [] | 绘制数据 |

## 绘制数据类型

### 图片类型 (type: 'image')

```javascript
{
  type: 'image',
  x: 0,                    // X坐标
  y: 0,                    // Y坐标
  value: '图片URL',         // 图片地址
  width: 375,              // 目标宽度
  height: 375,             // 目标高度
  radius: 8                // 圆角半径（可选）
}
```

### 文本类型 (type: 'text')

```javascript
{
  type: 'text',
  x: 10,                   // X坐标
  y: 20,                   // Y坐标
  value: '文本内容',        // 文本内容
  color: '#333333',        // 文本颜色
  font: '16px Arial',      // 字体样式
  textAlign: 'left',       // 文本对齐方式
  lineHeight: 20,          // 行高
  lineMaxWidth: 200,       // 最大行宽
  lineNum: 2               // 最大行数
}
```

## 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| drawSuccess | 绘制成功 | imagePath: 生成的图片路径 |

## 注意事项

1. **图片比例保持**：当 `keepAspectRatio` 为 `true` 时，图片会自动保持原始比例，并在指定区域内居中显示
2. **文本边界处理**：文本会自动检查画布边界，超出部分会被截断或添加省略号
3. **错误处理**：图片加载失败时会自动降级使用原始尺寸
4. **性能优化**：组件会自动缓存生成的图片，避免重复绘制

## 更新日志

### v1.1.0
- ✅ 新增图片等比例缩放功能
- ✅ 改进文本边界处理
- ✅ 添加错误处理和容错机制
- ✅ 新增 `keepAspectRatio` 属性
- ✅ 优化代码结构和性能 