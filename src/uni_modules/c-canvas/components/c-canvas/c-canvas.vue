<template>
	<view>
		<canvas type='2d' :canvas-id="canvasId" :id="canvasId" :style="{ width: `${width}px`, height: `${height}px` }" :width="width" :height="height" class="cCanvas"></canvas>
	</view>
</template>

<script>
/**
 * c-canvas 绘制海报组件
 * @property {String} canvasId 画布id
 * @property {Boolean} isAuto 是否自动绘制
 * @property {Number} width 画布宽度
 * @property {Number} height 画布高度
 * @property {Boolean} keepAspectRatio 是否保持图片比例，默认true
 * @property {Array} drawData 绘制的数据  [{type: 'image',x: 0,y: 0,value: 'https://vkceyugu.cdn.bspapp.com/VKCEYUGU-3e51af12-a055-4f58-b1ad-7710dd05bfc4/17d972b6-6c4e-4cab-a8c0-e9ee832ecc63.jpg',width:375,height:375},
	{type: 'text',x: 10,y: 20,value: '这是一段文字',color: '#F1D7A4',textAlign: 'center',font:'normal 400 12px sans-serif',lineHeight:20,lineMaxWidth:200,lineNum:1},]
 * @event {Function()} drawSuccess 绘制成功 返回导出的图片路径
 * */
let ctx = null, thumb = null
export default {
	name: 'c-canvas',
	props: {
		canvasId: {
			type: String,
			default: 'myCanvas'
		},
		isAuto: {
			type: Boolean,
			default: true
		},
		//画布宽度
		width: {
			type: Number,
			default: 375
		},
		//画布高度
		height: {
			type: Number,
			default: 375
		},
		drawData: {
			type: Array,
			default: () => {
				return []
			}
		},
		// 是否保持图片比例
		keepAspectRatio: {
			type: Boolean,
			default: true
		}
	},
	emits: ['drawSuccess'],
	data() {
		return {

		}
	},
	methods: {
		drawText(item) {
			// 参数验证
			if (!item || !item.value) {
				return;
			}
			
			// 边界检查
			const canvasWidth = this.width;
			const canvasHeight = this.height;
			const x = item.x || 0;
			const y = item.y || 0;
			
			// 检查是否完全超出画布边界
			if (x > canvasWidth || y > canvasHeight) {
				return;
			}
			
			if (item.font) ctx.font = item.font;
			// #ifdef MP
			if (item.color) ctx.fillStyle = item.color
			if (item.textAlign && x && y) ctx.textAlign = item.textAlign
			// #endif
			// #ifndef MP
			if (item.color) ctx.setFillStyle(item.color)
			if (item.textAlign && x && y) ctx.setTextAlign(item.textAlign)
			// #endif
			
			// 调用文本换行处理方法，传递textAlign和font参数
			this.textPrewrap(ctx, item.value, x, y, item.lineHeight || 20, item.lineMaxWidth, item.lineNum || 1, item.textAlign || 'left', item.font || '')
		},
		async drawImage(item) {
			// 参数验证
			if (!item || !item.value) {
				return;
			}
			
			let Img = item.value
			// #ifdef H5
			// if(this.checkUrl(Img)){
			// 	let res = await uni.downloadFile({url:item.value})
			// 	Img = res.tempFilePath
			// }
			// #endif
			
			// 计算保持比例的尺寸
			const targetWidth = item.width || 0;
			const targetHeight = item.height || 0;
			
			// 如果指定了宽高且需要保持比例
			if (targetWidth && targetHeight && this.keepAspectRatio) {
				try {
					// 加载图片获取原始尺寸
					const imgInfo = await this.getImageInfo(Img);
					const originalWidth = imgInfo.width;
					const originalHeight = imgInfo.height;
					
					// 计算等比例尺寸
					const scaled = this.calculateAspectRatioFit(originalWidth, originalHeight, targetWidth, targetHeight);
					
					// 计算居中位置
					const offsetX = item.x + (targetWidth - scaled.width) / 2;
					const offsetY = item.y + (targetHeight - scaled.height) / 2;
					
					if (item.radius) {
						await this.drawRoundRect(item.radius, offsetX, offsetY, scaled.width, scaled.height, Img)
					} else {
						// #ifdef MP
						let imgBit = await this.loadImg(Img)
						ctx.drawImage(imgBit, offsetX, offsetY, scaled.width, scaled.height)
						// #endif
						// #ifndef MP
						ctx.drawImage(Img, offsetX, offsetY, scaled.width, scaled.height)
						// #endif
					}
				} catch (error) {
					console.error('图片加载失败:', error);
					// 如果图片加载失败，使用原始尺寸绘制
					if (item.radius) {
						await this.drawRoundRect(item.radius, item.x || 0, item.y || 0, targetWidth, targetHeight, Img)
					} else {
						// #ifdef MP
						let imgBit = await this.loadImg(Img)
						ctx.drawImage(imgBit, item.x || 0, item.y || 0, targetWidth, targetHeight)
						// #endif
						// #ifndef MP
						ctx.drawImage(Img, item.x || 0, item.y || 0, targetWidth, targetHeight)
						// #endif
					}
				}
			} else {
				// 不保持比例或没有指定宽高，使用原始尺寸
				if (item.radius) {
					await this.drawRoundRect(item.radius, item.x || 0, item.y || 0, item.width, item.height, Img)
				} else {
					// #ifdef MP
					let imgBit = await this.loadImg(Img)
					ctx.drawImage(imgBit, item.x || 0, item.y || 0, item.width, item.height)
					// #endif
					// #ifndef MP
					ctx.drawImage(Img, item.x || 0, item.y || 0, item.width, item.height)
					// #endif
				}
			}
		},
		// #ifdef MP
		loadImg(src) {
			return new Promise((resolve, reject) => {
				let imgBit = this.canvas.createImage();
				imgBit.src = src;
				imgBit.onload = (e) => {
					resolve(imgBit)
				}
			})
		},
		// #endif
		/*
			 *  参数说明
			 *  ctx Canvas实例
			 *  img 图片地址
			 *   x  x轴坐标
			 *   y  y轴坐标
			 *   w  宽度
			 *   h  高度
			 *   r  弧度大小
			 */
		async drawRoundRect(r, x, y, w, h, img) {
			ctx.save();
			if (w < 2 * r) r = w / 2;
			if (h < 2 * r) r = h / 2;
			ctx.beginPath();
			ctx.moveTo(x + r, y);
			ctx.arcTo(x + w, y, x + w, y + h, r);
			ctx.arcTo(x + w, y + h, x, y + h, r);
			ctx.arcTo(x, y + h, x, y, r);
			ctx.arcTo(x, y, x + w, y, r);
			ctx.closePath();
			ctx.clip();
			// #ifdef MP
			let imgBit = await this.loadImg(img);
			ctx.drawImage(imgBit, x, y, w, h)
			// #endif
			// #ifndef MP
			ctx.drawImage(img, x, y, w, h);
			// #endif

			ctx.restore(); // 返回上一状态
		},
		getContext() {
			return new Promise((resolve) => {
				const { pixelRatio } = uni.getSystemInfoSync()
				uni.createSelectorQuery()
					.in(this)
					.select(`#${this.canvasId}`)
					.fields({ node: true, size: true })
					.exec(res => {
						const { width, height } = res[0]
						const canvas = res[0].node
						// #ifdef MP
						canvas.width = res[0].width * pixelRatio
						canvas.height = res[0].height * pixelRatio
						// #endif
						// #ifndef MP
						canvas.width = res[0].width
						canvas.height = res[0].height
						// #endif
						resolve({ canvas, width, height, pixelRatio })
					})
			})
		},
		async draw() {
			// #ifdef MP
			const { canvas, pixelRatio } = await this.getContext()
			this.canvas = canvas
			ctx = canvas.getContext('2d')
			ctx.scale(pixelRatio, pixelRatio)
			// #endif
			// #ifndef MP
			ctx = uni.createCanvasContext(this.canvasId, this)
			// #endif
			// 首先绘制背景
			ctx.fillStyle = '#FFFFFF'; // 设置背景颜色，例如白色
			ctx.fillRect(0, 0, this.width, this.height); // 绘制一个覆盖整个画布的矩形

			// --- 辅助线：画布最右侧 ---
			ctx.beginPath();
			ctx.moveTo(this.width, 0);
			ctx.lineTo(this.width, this.height);
			ctx.strokeStyle = '#fff';
			ctx.stroke();
			// --- END ---

			for (let item of this.drawData) {
				if (item.type == 'text') {
					this.drawText(item)
				} else if (item.type == 'image') {
					await this.drawImage(item)
				}
			}
			// #ifdef MP
			setTimeout(() => {
				uni.canvasToTempFilePath({
					canvas: this.canvas,
					quality: 0.5,
					destWidth:900,
					destHeight:900,
					success: (ret) => {
						// 在H5平台下，tempFilePath 为 base64
						thumb = ret.tempFilePath
						this.$emit('drawSuccess', thumb)
					},
					fail: (err) => {
						console.log(err);
					}
				})
			}, 80)
			// #endif
			// #ifndef MP
			ctx.draw(false, () => {
				uni.canvasToTempFilePath({
					canvasId: this.canvasId,
					quality: 0.5,
					destWidth:900,
					destHeight:900,
					success: (ret) => {
						// 在H5平台下，tempFilePath 为 base64
						thumb = ret.tempFilePath
						this.$emit('drawSuccess', thumb)
					},
					fail: (err) => {
						console.log(err);
					}
				})
			})
			// #endif

		},
		checkUrl(url) {
			return /(http|https):\/\/([\w.]+\/?)\S*/.test(url)
		},
		textPrewrap(ctx, content, drawX, drawY, lineHeight, lineMaxWidth, lineNum,textAlign,font) {
			// 参数验证和边界检查
			if (!content || !lineMaxWidth || lineNum <= 0) {
				return;
			}
			if (font) ctx.font = font;
			// 确保绘制位置在画布范围内
			const canvasWidth = this.width;
			const canvasHeight = this.height;
			
			// 根据文本对齐方式调整绘制位置和可用宽度
			let actualDrawX = drawX;
			let maxAvailableWidth = lineMaxWidth || canvasWidth;
			if (textAlign === 'right') {
				if (drawX > canvasWidth) {
					actualDrawX = canvasWidth;
				}
				maxAvailableWidth = Math.min(lineMaxWidth, actualDrawX);
			} else if (textAlign === 'center') {
				if (drawX < 0) actualDrawX = 0;
				if (drawX > canvasWidth) actualDrawX = canvasWidth;
				maxAvailableWidth = Math.min(lineMaxWidth, Math.min(actualDrawX * 2, canvasWidth - actualDrawX));
			} else {
				if (drawX < 0) actualDrawX = 0;
				if (drawX > canvasWidth) return;
				maxAvailableWidth = Math.min(lineMaxWidth, canvasWidth - actualDrawX);
			}
			if (maxAvailableWidth <= 0) return;
			if (drawY < 0) drawY = 0;
			if (drawY > canvasHeight) return;
			if (font) ctx.font = font;
			if (ctx.measureText(content).width <= maxAvailableWidth) {
				if (drawY + lineHeight <= canvasHeight) {
					let finalDrawX = actualDrawX;
					if (textAlign === 'right') {
						if (font) ctx.font = font;
						
						finalDrawX = actualDrawX - ctx.measureText(content).width +content.length*10;
					} else if (textAlign === 'center') {
						if (font) ctx.font = font;
						finalDrawX = actualDrawX - ctx.measureText(content).width / 2;
					}
					ctx.fillText(content, finalDrawX, drawY);
				}
				return;
			}
			let currentLine = 1;
			let startIndex = 0;
			let lastFittedIndex = 0;
			for (let i = 0; i <= content.length; i++) {
				if (font) ctx.font = font;
				const subStr = content.substring(startIndex, i);
				const textWidth = ctx.measureText(subStr).width;
				if (textWidth <= maxAvailableWidth) {
					lastFittedIndex = i;
					continue;
				}
				if (currentLine < lineNum) {
					if (drawY + lineHeight > canvasHeight) {
						return;
					}
					let lineText = content.substring(startIndex, lastFittedIndex);
					let finalDrawX = actualDrawX;
					if (textAlign === 'right') {
						if (font) ctx.font = font;
						finalDrawX = actualDrawX - ctx.measureText(lineText).width;
					} else if (textAlign === 'center') {
						if (font) ctx.font = font;
						finalDrawX = actualDrawX - ctx.measureText(lineText).width / 2;
					}
					ctx.fillText(lineText, finalDrawX, drawY);
					drawY += lineHeight;
					currentLine++;
					startIndex = lastFittedIndex;
					i = lastFittedIndex;
				} else {
					let displayText = content.substring(startIndex, lastFittedIndex);
					if (lastFittedIndex < content.length) {
						if (font) ctx.font = font;
						let ellipsisText = displayText + '...';
						while (ctx.measureText(ellipsisText).width > maxAvailableWidth && displayText.length > 0) {
							displayText = displayText.substring(0, displayText.length - 1);
							ellipsisText = displayText + '...';
						}
						displayText = ellipsisText;
					}
					if (drawY + lineHeight <= canvasHeight) {
						let finalDrawX = actualDrawX;
						if (textAlign === 'right') {
							if (font) ctx.font = font;
							finalDrawX = actualDrawX - ctx.measureText(displayText).width;
						} else if (textAlign === 'center') {
							if (font) ctx.font = font;
							finalDrawX = actualDrawX - ctx.measureText(displayText).width / 2;
						}
						ctx.fillText(displayText, finalDrawX, drawY);
					}
					return;
				}
			}
			if (startIndex < content.length && currentLine <= lineNum) {
				if (drawY + lineHeight <= canvasHeight) {
					let remainingText = content.substring(startIndex);
					let finalDrawX = actualDrawX;
					if (textAlign === 'right') {
						if (font) ctx.font = font;
						finalDrawX = actualDrawX - ctx.measureText(remainingText).width;
					} else if (textAlign === 'center') {
						if (font) ctx.font = font;
						finalDrawX = actualDrawX - ctx.measureText(remainingText).width / 2;
					}
					ctx.fillText(remainingText, finalDrawX, drawY);
				}
			}
		},
		// 获取图片信息
		getImageInfo(src) {
			return new Promise((resolve, reject) => {
				// #ifdef MP
				let imgBit = this.canvas.createImage();
				imgBit.src = src;
				imgBit.onload = () => {
					resolve({
						width: imgBit.width,
						height: imgBit.height
					});
				};
				imgBit.onerror = reject;
				// #endif
				// #ifndef MP
				uni.getImageInfo({
					src: src,
					success: resolve,
					fail: reject
				});
				// #endif
			});
		},
		
		// 计算等比例尺寸
		calculateAspectRatioFit(originalWidth, originalHeight, targetWidth, targetHeight) {
			const scaleX = targetWidth / originalWidth;
			const scaleY = targetHeight / originalHeight;
			const scale = Math.min(scaleX, scaleY);
			
			return {
				width: originalWidth * scale,
				height: originalHeight * scale,
				scale: scale
			};
		},
	},
	mounted() {
		if (this.isAuto) this.draw()
	}
}



</script>

<style lang="scss">
.cCanvas {
	position: fixed;
	top: -10000px;
}
</style>
