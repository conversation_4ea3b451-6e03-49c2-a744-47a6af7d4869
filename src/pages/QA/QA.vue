<template>
	<view class="review-box">
		<navbar autoBack />

		<view class="self-headerImg-box" :style="{ top: isActive ? isStatusBarAndNavHeight : '-44px' }"
			@click="$tab.navigateTo(`/pages/qaQuestion/qaQuestion?ProId=${ProId}`)">
			<view class="self-headerImg">
				<u-avatar :src="avatarImg" size="96rpx"></u-avatar>
				<u--text text="Write..." color="#8C8C8C" size="27rpx" prefixIcon="edit-pen"
					iconStyle="color: #8C8C8C;font-size: 37rpx;margin-right: 3px;"></u--text>
			</view>
		</view>

		<mescroll-body ref="mescrollRef" :up="upOption" @init="mescrollInit" @down="downCallback" @up="upCallback">
			<view class="review-box-list">
				<view class="self-headerImg" @click="$tab.navigateTo(`/pages/qaQuestion/qaQuestion?ProId=${ProId}`)">
					<u-avatar :src="avatarImg" size="96rpx"></u-avatar>
					<u--text text="Write..." color="#8C8C8C" size="27rpx" prefixIcon="edit-pen"
						iconStyle="color: #8C8C8C;font-size: 37rpx;margin-right: 3px;"></u--text>
				</view>

				<view class="review-item-box" v-for="item in list" :key="item.QId">
					<view class="flex"
						@click="$tab.navigateTo(`/pages/qaQuestion/qaQuestion?ProId=${ProId}&QId=${item.QId}`)">
						<u-avatar :src="item.PicPath" size="77rpx" style="padding-right: 19rpx;"></u-avatar>
						<view class="flex align-center">
							<u--text :text="item.Question" size="30rpx" bold color="#262626"
								margin="0 98rpx 0 0"></u--text>
							<!-- <u--text :text="item.AccTime" color="#8C8C8C" size="24rpx"></u--text> -->
						</view>
					</view>

					<template v-if="item.children && item.children.length">
						<seeMore :dataList="item.children" :styleObject="styleObject">
							<template v-slot:content="{ item: cItem }">
								<view class="qa-item">
									<view class="flex  flex-sub" style="overflow: hidden;">
										<u-avatar :src="cItem.PicPath" size="65rpx"
											style="flex-shrink: 0; padding-right: 19rpx;"></u-avatar>
										<view class="content-wrapper flex align-center" style="min-width: 0; flex: 1;">
											<u--text v-if="cItem.FirstName || cItem.NickName"
												:text="`${cItem.NickName}${cItem.FirstName ? ` ${cItem.FirstName} ${cItem.LastName}` : ''}`"
												size="28rpx" bold color="#8C8C8C">
											</u--text>
											<u--text :text="cItem.Answer" size="26rpx" bold color="#666666"
												style="word-break: break-all;">
											</u--text>
										</view>
									</view>
									<view class="icon-box flex flex-direction align-center"
										style="flex-shrink: 0; min-width: 80rpx; margin-left: 20rpx;"
										@click.stop="handleCommentLike(cItem)">
										<template v-if="cItem.Votes > 0">
											<u--image v-if="cItem.IsAgree == 1" src="/static/assets/home/<USER>"
												width="38rpx" height="38rpx" duration="0">
											</u--image>
											<u--image v-else src="/static/assets/home/<USER>" width="38rpx"
												height="38rpx" duration="0">
											</u--image>
											<u--text :text="cItem.Votes || 0" color="#8C8C8C" size="23rpx" bold
												align="center">
											</u--text>
										</template>
										<template v-else>
											<u--image src="/static/assets/home/<USER>" width="38rpx" height="38rpx"
												duration="0">
											</u--image>
											<u--text text="Helpful" color="#8C8C8C" size="23rpx" bold align="center">
											</u--text>
										</template>
									</view>
								</view>
							</template>
						</seeMore>
					</template>
					<u-line color="#F7F7F7" margin="30rpx 0"></u-line>
				</view>
			</view>
		</mescroll-body>
	</view>
</template>

<script>
import {
	addQuestionList,
	addQuestion,
	replyQuestion,
	likeQuestion
} from "@/api/orderDetails.js";
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import MoteLinesDivide from "@/components/mote-lines-divide/mote-lines-divide"
import {
	mapGetters
} from "vuex";

export default {
	mixins: [MescrollMixin], // 使用mixin
	components: {
		MoteLinesDivide
	},
	data() {
		return {
			avatarImg: '',
			ProId: '',
			list: [],
			isActive: false,
			styleObject: {
				'margin-left': '125rpx'
			},
			upOption: {
				toTop: {
					duration: 0
				}
			}
		};
	},

	onLoad(e) {
		this.ProId = e?.ProId;
	},
	onShow() {
		uni.$once('handleQaRefresh', ({
			refresh,
			isToTop
		}) => {
			if (refresh) {
				this.reloadList();
			}
		})
	},
	computed: {
		...mapGetters(['statusBarAndNavHeight']),
		isStatusBarAndNavHeight() {
			return `${this.statusBarAndNavHeight}px`;
		},
	},
	onPageScroll(e) {
		this.isActive = e.scrollTop > 150;
	},
	methods: {
		reloadList(isToTop) {
			this.mescroll.resetUpScroll();
		},
		upCallback(page) {
			addQuestionList({
				ProId: this.ProId,
				page: page.num
			}).then(res => {
				if (page.num == 1) {
					this.list = [];
				}

				const resultList = Object.values(res.result)

				this.list = this.list.concat(resultList);
				this.list.forEach(item => {
					this.$set(item, 'showAll', false);
				});
				this.mescroll.endByPage(resultList.length, res.total_pages);
			}).catch(() => {
				//联网失败, 结束加载
				this.mescroll.endErr();
			})
		},
		handleCommentLike(item) {
			likeQuestion({
				QId: item.QId,
				Status: item.IsAgree == 0 ? 1 : 0
			}).then(res => {
				if (item.IsAgree == 0) {
					this.$toast({
						title: 'Like successfully'
					})
				}

				item.IsAgree = item.IsAgree == 0 ? 1 : 0;
				item.Votes = item.IsAgree == 0 ? (item.Votes > 1 ? item.Votes - 1 : 0) : (+item.Votes + 1)
			}).catch(() => {
				this.$toast({
					title: 'Failed to like'
				})
			})
		},
		handleMoreReply(item) {
			item.showAll = !item.showAll;
		}
	}
}
</script>

<style lang="scss" scoped>
.review-box {
	padding: 27rpx 30rpx;
	box-sizing: border-box;

	.self-headerImg-box {
		position: fixed;
		left: 0;
		right: 0;
		top: -44px;
		z-index: 666;
		background: #fff;
		box-shadow: 0 0px 10px rgba(0, 0, 0, .1);
		transition: top .3s ease;

		.self-headerImg {
			height: 96rpx;
			background: #F0F0F0;
			border-radius: 96rpx;
			display: flex;
			@include flex-gap(0, 13rpx); // 替换了 column-gap
			margin: 10rpx 30rpx;
		}
	}

	.review-box-list {
		background: #fff;
		padding: 30rpx;
		border-radius: 30rpx;
		box-sizing: border-box;
		// min-height: calc(100vh - 70px);

		.self-headerImg {
			height: 96rpx;
			background: #F0F0F0;
			border-radius: 96rpx;
			display: flex;
			@include flex-gap(0, 13rpx); // 替换了 column-gap
			margin-bottom: 73rpx;
		}

		.qa-item {
			display: flex;
			justify-content: space-between;
			align-items: center;

			padding: 30rpx 0 40rpx 38rpx;

			.icon-box {
				width: 40px;
			}
		}
	}


	.comment-box {
		padding: 30rpx 30rpx 0;
		height: 70vh;

		.review-item-box {
			display: flex;
			flex-direction: column;
			@include flex-gap(50rpx, 0); // 替换了 row-gap
		}
	}

	.comment-input-box {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background: #fff;
		box-shadow: 0px -4rpx 8rpx 0px rgba(0, 0, 0, 0.1);

		.comment-input {
			padding: 25rpx 37rpx;
			margin: 5px 30rpx 10px;
			background: #F0F0F0;
			border-radius: 58rpx;
			display: flex;
		}
	}

	.commentInput {
		margin: 17rpx 30rpx;
		background: #F0F0F0;

		::v-deep .u-input__content {
			padding: 20rpx 30rpx;

			.u-icon__icon {
				color: #8C8C8C !important;
			}

			.input-placeholder {
				color: #8C8C8C !important;
			}
		}
	}

	.comment-detail-box {
		background: #fff;
	}

	.comment-submit-box {
		position: fixed;
		display: flex;
		align-items: flex-end;
		z-index: 9900;
		left: 0;
		top: var(--window-top);
		bottom: 0;
		background-color: rgba($color: #000000, $alpha: 0.5);
		width: 100%;

		.comment-add {
			position: fixed;
			background-color: #FFFFFF;
			width: 100%;
			padding: 5rpx;
			border: 1px solid #ddd;
			transition: .3s;
			-webkit-transition: .3s;

			.textarea {
				height: 100px;
				padding: 16rpx;
				width: 100%;
			}
		}
	}

	.comment-detail-popup {
		.comment-detail-box {
			margin: 10px 15px 20px;

			.u-textarea {
				background: #F0F0F0;
			}
		}
	}

	.qa-item {
		display: flex;
		align-items: flex-start;
		justify-content: space-between;
		padding: 30rpx 31rpx 40rpx 38rpx;
		width: 100%;
		box-sizing: border-box;

		.flex-sub {
			flex: 1;
			min-width: 0;
			margin-right: 20rpx;
			overflow: hidden;
		}

		.content-wrapper {
			overflow: hidden;
			text-overflow: ellipsis;
		}

		.icon-box {
			flex-shrink: 0;
			min-width: 80rpx;
			text-align: center;
		}
	}
}
</style>