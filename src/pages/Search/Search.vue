<template>
	<view class="search-container">
		<navbar class="navbar-header" autoBack titleWidth="calc(100% - 60px)" :hiddenCloseApp="true">
			<template slot="center">
				<view class="search-box">
					<u-search class="search-input" v-model="searchTxt" focus placeholder="Search for bicycles"
						height="90rpx" bgColor="#fff" placeholderColor="#595959" :showAction="false"
						searchIcon="/static/assets/home/<USER>" @change="onChangeSearch"
						@search="onSearchGoods" @input="onChangeSearch"></u-search>
					<u-button class="search-btn" color="#FF5A1E" @click="searchBut">Search</u-button>
				</view>
			</template>
		</navbar>
		
		<!-- 热门搜索 -->
		<template v-if="!isSearchShow && hotSearchList.length">
			<view class="hot-search-box">
				<view class="flex justify-between align-center">
					<u--text text="Hot Search" size="14" bold></u--text>
					<view class="flex align-center">
						<u-icon color="#7e7777" name="reload" size="20" @click="handleReloadHot"></u-icon>
						<u-line direction="col" margin="0 8px" length="15"></u-line>
						<u-icon color="#7e7777" :name="isHotShow ? 'eye-fill' : 'eye-off'" size="20"
							@click="isHotShow = !isHotShow"></u-icon>
					</view>
				</view>
				<view class="margin-top-sm hot-search-list" v-show="isHotShow">
					<view class="tag-item" v-for="(item,index) in hotSearchList" :key="index"
						@click="onHotSearchClick(item.Name_en)">
						<u--text :text="item.Name_en" align="center" color="#8C8C8C" size="26rpx"></u--text>
					</view>
				</view>
			</view>
		</template>
		<!-- 搜索结果 -->
		<scroll-view v-show="isSearchShow" scroll-y="true" class="search-list-box">
			<!-- 搜索结果列表 -->
			<template v-if="searchList.length">
				<u-cell v-for="item in searchList" :key="item.ProId"
					@click="$tab.navigateTo(`/pages/sortGoods/sortGoods?Keyword=${item.Name_en}&searchTxt=${item.Name_en}`)">
					<template slot="icon">
						<u-icon color="#8C8C8C" name="search" size="38rpx"></u-icon>
					</template>
					<template slot="title">
						<u--text :text="item.Name_en" color="#262626" size="26rpx" bold lines="1"></u--text>
					</template>
				</u-cell>
			</template>
			
			<!-- 无搜索结果 -->
			<u-empty v-else mode="search" text="No search results." iconSize="60" style="height: 100%;">
			</u-empty>
		</scroll-view>
	</view>
</template>

<script>
	import {
		getSearchHistory
	} from '@/api/products.js';

	export default {
		data() {
			return {
				searchTxt: '',
				focus: true,
				isHotShow: true,
				isSearchShow: false,
				hotSearchList: [],
				searchList: [],
				searchTimer: null, // 搜索防抖定时器
				isSearching: false // 搜索状态
			};
		},
		onLoad() {
			this.getHotHistory();
		},
		methods: {
			async handleReloadHot() {
				this.$showLoading()
				await this.getHotHistory();
				this.$hideLoading();
			},
			// 猜你想搜
			getHotHistory() {
				return new Promise((resolve, reject) => {
					getSearchHistory().then(res => {
						this.hotSearchList = res;
						setTimeout(() => {
							resolve();
						}, 300);
					}).catch(err => {
						reject(err)
					});
				})
			},
			// 输入变化时的联动搜索
			onChangeSearch() {
				// 清除之前的定时器
				if (this.searchTimer) {
					clearTimeout(this.searchTimer);
				}
				
				// 如果输入为空，隐藏搜索结果
				if (this.searchTxt.trim() === '') {
					this.isSearchShow = false;
					this.searchList = [];
					return;
				}
				
				// 设置防抖，用户停止输入300ms后执行搜索
				this.searchTimer = setTimeout(() => {
					this.performSearch();
				}, 300);
			},
			
			// 执行搜索
			performSearch() {
				if (this.searchTxt.trim() === '') {
					return;
				}
				
				this.isSearchShow = true;
				this.isSearching = true;
				
				getSearchHistory({
					Keyword: this.searchTxt.trim()
				}).then(res => {
					console.log('搜索结果:', res);
					this.searchList = res || [];
				}).catch(err => {
					console.error('搜索失败:', err);
					this.searchList = [];
				}).finally(() => {
					this.isSearching = false;
				});
			},
			
			// 搜索框回车事件（保留原有功能）
			onSearchGoods() {
				if (this.searchTxt.trim() === '') {
					return this.$toast({
						title: 'Please enter the search content'
					});
				}
				
				// 直接跳转到搜索结果页
				this.$tab.navigateTo(`/pages/sortGoods/sortGoods?Keyword=${this.searchTxt.trim()}&searchTxt=${this.searchTxt.trim()}`);
			},
			// 搜索按钮点击事件
			searchBut() {
				if (this.searchTxt.trim() === '') {
					return this.$toast({
						title: 'Please enter the search content'
					});
				}

				// 跳转到搜索结果页面
				this.$tab.navigateTo(`/pages/sortGoods/sortGoods?Keyword=${this.searchTxt.trim()}&searchTxt=${this.searchTxt.trim()}`);
			},
			
			// 点击热门搜索标签
			onHotSearchClick(keyword) {
				this.searchTxt = keyword;
				this.performSearch();
			}
		},
		
		// 页面销毁时清除定时器
		beforeDestroy() {
			if (this.searchTimer) {
				clearTimeout(this.searchTimer);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.search-container {
		padding: 0 30rpx;

		.navbar-header {
			::v-deep .u-navbar__content {
				display: inline-block;
			}

			.search-box {
				width: 100%;
				display: flex;
				justify-content: space-between;
				align-items: center;
				background-color: #fff;
				border-radius: 30rpx;
				padding: 0 23rpx;
				margin-left: 15px;
				box-sizing: border-box;

				.search-input {
					::v-deep .u-search__content {
						padding-left: 0;
					}
				}

				.search-btn {
					border-radius: 16rpx;
					height: 69rpx;
					width: 113rpx;
					font-size: 27rpx;
					font-weight: 500;
					color: #fff;
				}
			}
		}

		.hot-search-box {
			background-color: #fff;
			border-radius: 30rpx;
			margin-top: 30rpx;
			padding: 30rpx;

			.hot-search-list {
				display: grid;
				grid-template-columns: repeat(3, 1fr);
				grid-gap: 30rpx;

				.tag-item {
					height: 56rpx;
					line-height: 56rpx;
					border-radius: 56rpx;
					border: 2rpx solid #F0F0F0;

					::v-deep .u-tag {
						display: flex;
						justify-content: center;
					}
				}
			}
		}

		.search-list-box {
			height: 80vh;
			border-radius: 30rpx;
			background-color: #fff;
			box-sizing: border-box;
			margin-top: 30rpx;

			::v-deep .u-cell__body {
				padding: 30rpx 38rpx;
			}

			::v-deep .u-line {
				border-color: #F7F7F7 !important;
			}
			
			.search-loading {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				padding: 100rpx 0;
			}
		}
	}
</style>