<template>
	<view class="sort-goods">
		<navbar autoBack />
		<view class="search-fixed-box">
			<u-search v-model="searchTxt" placeholder="Search for bicycles" height="90rpx" bgColor="#fff"
				placeholderColor="#595959" actionText="Search" :actionStyle="actionStyle" :animation="true"
				searchIcon="/static/assets/home/<USER>" :showAction="false" @custom="onSearchGoods">
			</u-search>
			<u-button class="search-btn" color="#FF5A1E" @click="onSearchGoods">Search</u-button>
		</view>
		<view class="top-warp" v-if='CateName'>
			<u--text :text="CateName" color="#262626" size="38rpx" lines="1"></u--text>
		</view>
		<mescroll-body :up="upOption" @init="mescrollInit" top="20" @down="downCallback" @up="upCallback"
			class="mescroll-body-container">
			<view class="myRecords-box">
				<water-fall :dataList="list">
					<template v-slot:itemLeft="slotProps">
						<productCover :product="slotProps.item" :scrollTop="scrollTop" imgName="Pic" titleName="Name"
							@click.native="handleToGoodsDetail(slotProps.item)" @addFavoriteFn="onAddFavoriteFn"
							@onAddCart="onAddCart"></productCover>
					</template>

					<template v-slot:advertiseSlot>
						<easy-loadimage v-if="path_app && list.length"
							:imgStyle="{ height: '463rpx', borderRadius: '16px' }" mode="widthFix"
							:scroll-top="scrollTop" loading-mode="skeleton-1" :image-src="path_app"></easy-loadimage>
					</template>
					<template v-slot:itemRight="slotProps">
						<productCover :product="slotProps.item" :scrollTop="scrollTop" imgName="Pic" titleName="Name"
							@click.native="handleToGoodsDetail(slotProps.item)" @addFavoriteFn="onAddFavoriteFn"
							@onAddCart="onAddCart"></productCover>
					</template>
				</water-fall>
			</view>
		</mescroll-body>

		<addPopup ref="addPopupRef"></addPopup>
	</view>
</template>

<script>
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import {
	getArrivalList,
	productFavorite,
	productDetail
} from "@/api/home.js";

export default {
	mixins: [MescrollMixin], // 使用mixin
	data() {
		return {
			scrollTop: 0,
			Keyword: "",
			CateId: "",
			list: [],
			productDetailData: {},
			path_app: "",
			searchTxt: "",
			hideKeyWord: false,
			CateName: "",
			columns: [
				[],
				[]
			],
			upOption: {
				toTop: {
					duration: 0
				}
			},
			actionStyle: {
				fontSize: '12px'
			}
		};
	},
	onLoad(e) {
		const {
			Keyword,
			CateId,
			searchTxt
		} = e;
		this.Keyword = Keyword;
		if (CateId) {
			this.CateName = Keyword;
		}
		if (searchTxt) {
			this.searchTxt = searchTxt;
		}
		this.CateId = CateId;
	},
	onPageScroll({
		scrollTop
	}) {
		this.scrollTop = scrollTop;
	},
	methods: {
		onSearchGoods() {
			this.hideKeyWord = true;
			this.mescroll.resetUpScroll();
		},
		onAddFavoriteFn({
			ProId
		}) {
			productFavorite({
				ProId
			}).then(res => {
				if (res.Status === 1) {
					this.$toast({
						title: 'Successful collection',
						image: "/static/assets/common/add_successfully.png"
					})
				}

				this.mescroll.resetUpScroll();
			}).catch(() => {
				this.$toast({
					title: 'Collection failure',
					icon: 'error'
				})
			})
		},
		// 加入购物车
		onAddCart(params) {
			uni.showLoading()
			productDetail({
				ProId: params.ProId
			}).then(res => {
				this.$refs.addPopupRef.isAddCartShow = true;
				this.$refs.addPopupRef.productImg = params.Pic;
				this.$refs.addPopupRef.productDetailData = res;
			}).finally(() => {
				uni.hideLoading()
			})
		},
		handleToGoodsDetail(item) {
			this.$tab.navigateTo(`/pages/goodsDetail/goodsDetail?ProId=${item.ProId}`)
		},
		/*下拉刷新的回调 */
		downCallback() {
			this.mescroll.resetUpScroll()
		},
		/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */
		upCallback(page) {
			getArrivalList({
				CateId: this.CateId,
				Keyword: this.searchTxt,
				page: page.num,
			}).then(res => {
				if (page.num === 1) {
					this.list = []
				}
				this.path_app = res.path_app;
				this.CateName = res.category_name;
				this.list = this.list.concat(res.data);
				this.mescroll.endByPage(res.data.length, res.total_pages); // 隐藏加载状态栏
			}).catch(() => {
				this.mescroll.endErr();
			})
		},
	}
}
</script>
<style lang="scss" scoped>
.sort-goods {
	min-height: 100vh;
}

.top-warp {
	position: relative;
	left: 0;
	right: 0;
	height: 45rpx;
	padding: 15rpx 30rpx;
	background-color: #F0F0F0;
}

.search-fixed-box {
	font-family: PingFang SC-Bold !important;
	padding: 0 32rpx 5rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: #fff;
	border-radius: 30rpx;
	padding: 0 23rpx;
	margin: 10rpx 15px 0;
	box-sizing: border-box;
	font-size: 26rpx;

	.search-input {
		::v-deep .u-search__content {
			padding-left: 0;
		}
	}

	.search-btn {
		border-radius: 17rpx;
		height: 69rpx;
		width: 113rpx;
		font-size: 27rpx;
		font-weight: 500;
		color: #fff;
	}
}

.mescroll-body-container {
	min-height: 60vh !important;
	padding-top: 30rpx !important;
	;
}
</style>