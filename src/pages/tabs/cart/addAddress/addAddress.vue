<template>
	<view class="add-address-box">
		<navbar autoBack />

		<view class="common-box-style form-box">
			<u--form :model="addressForm">
				<!-- 1. First Name -->
				<u-form-item borderBottom>
					<u--input v-model="addressForm.FirstName" placeholder="First Name" border="none"></u--input>
				</u-form-item>
				<!-- 2. Last Name -->
				<u-form-item borderBottom>
					<u--input v-model="addressForm.LastName" placeholder="Last Name" border="none"></u--input>
				</u-form-item>
				<!-- 3. Country -->
				<u-form-item borderBottom @click="isCountryShow = true;">
					<u--input v-model="addressForm.Country" disabled disabledColor="#ffffff" placeholder="Country"
						border="none" style="pointer-events: none"></u--input>
					<u-icon slot="right" name="arrow-right" color="#CCCCCC" bold></u-icon>
				</u-form-item>
				<!-- 4. State/Province -->
				<u-form-item borderBottom @click="handleOpenProvince">
					<u--input v-model="addressForm.State" disabled disabledColor="#ffffff" placeholder="Please Select"
						border="none" style="pointer-events: none"></u--input>
					<u-icon slot="right" name="arrow-right" color="#CCCCCC" bold></u-icon>
				</u-form-item>
				<!-- 5. City -->
				<u-form-item borderBottom>
					<u--input v-model="addressForm.City" placeholder="City" border="none"></u--input>
				</u-form-item>
				<!-- 6. Address line 1 -->
				<u-form-item borderBottom>
					<u--input v-model="addressForm.AddressLine1" placeholder="Address line 1" border="none"></u--input>
				</u-form-item>
				<!-- 7. Address line 2 -->
				<u-form-item borderBottom>
					<u--input v-model="addressForm.AddressLine2" placeholder="Address line 2" border="none"></u--input>
				</u-form-item>
				<!-- 8. ZIP / Postal Code -->
				<u-form-item borderBottom>
					<u--input v-model="addressForm.ZipCode" placeholder="ZIP / Postal Code" border="none"></u--input>
				</u-form-item>
				<!-- 9. Phone Number with Country Code -->
				<u-form-item :borderBottom="false">
					<view class="phone-input-container">
						<view class="country-code-selector">
							<text class="country-code">{{ addressForm.CountryCode || '+1' }}</text>
						</view>
						<u--input v-model="addressForm.PhoneNumber" type="number" placeholder="Phone Number"
							border="none" class="phone-input">
						</u--input>
					</view>
				</u-form-item>
			</u--form>
		</view>

		<SubmitButton class="confirm-btn" v-show="isButtonVisible" @handleConfirm="handleConfirm"></SubmitButton>

		<!-- 国家 -->
		<u-picker :title="addressForm.Country || 'Select Country'" cancelText="Cancel" cancelColor="#8C8C8C"
			confirmText="Confirm" confirmColor="#FF5A1E" :show="isCountryShow" :columns="countryData" keyName="Country"
			:defaultIndex="getCountryDefaultIndex()" closeOnClickOverlay @close="isCountryShow = false"
			@cancel="isCountryShow = false" @confirm="chooseCountry"></u-picker>

		<!-- 省 -->
		<u-picker :title="addressForm.State || 'Select State'" cancelText="Cancel" cancelColor="#8C8C8C"
			confirmText="Confirm" confirmColor="#FF5A1E" :show="isProvinceShow" :columns="provinceData" keyName="States"
			:defaultIndex="getProvinceDefaultIndex()" closeOnClickOverlay @close="isProvinceShow = false"
			@cancel="isProvinceShow = false" @confirm="chooseProvince"></u-picker>

		<!-- 区号选择 -->
		<u-picker title="Country Code" cancelText="Cancel" cancelColor="#8C8C8C" confirmText="Confirm"
			confirmColor="#FF5A1E" :show="isCountryCodeShow" :columns="countryCodeData" keyName="Code"
			closeOnClickOverlay @close="isCountryCodeShow = false" @cancel="isCountryCodeShow = false"
			@confirm="chooseCountryCode"></u-picker>
	</view>
</template>

<script>
import {
	getCountry,
	addressSelectCountry,
	addressAdd
} from "@/api/address.js";
import { keyboardDetectorMixin } from "@/utils/ui/keyboardDetector.js";

export default {
	mixins: [keyboardDetectorMixin],
	data() {
		return {
			news: 0,
			isCountryShow: false,
			countryData: [],
			isProvinceShow: false,
			provinceData: [],
			isCountryCodeShow: false,
			countryCodeData: [],
			addressForm: {
				FirstName: "",
				LastName: "",
				AddressLine1: "",
				AddressLine2: "",
				City: "",
				ZipCode: "",
				position: "",
				country_id: "",
				Country: "",
				CountryCode: "",
				Province: "",
				State: "",
				PhoneNumber: ""
			},
			docmHeight: 0,
			isButtonVisible: true,
		};

	},
	onLoad(e) {
		if (e.editData) {
			const editData = JSON.parse(e.editData);
			console.log(editData)
			uni.setNavigationBarTitle({
				title: editData.AId ? `Modify address` : `Add address`
			})
			this.addressForm = editData;
			this.addressForm.country_id = editData.CId;
			this.addressForm.Province = editData.SId;
			// 如果编辑时没有区号，设置默认值
			if (!this.addressForm.CountryCode) {
				this.addressForm.CountryCode = '+1';
			}
		}
		this.news = e?.news;
		this.getCountry();
	},
	methods: {
		// 键盘显示时的回调
		onKeyboardShow(height) {
			this.isButtonVisible = false;
			console.log('✅ 键盘弹出，高度：' + height + 'px');
		},

		// 键盘隐藏时的回调
		onKeyboardHide() {
			this.isButtonVisible = true;
			console.log('❌ 键盘收起');
		},
		// 获取国家
		getCountry() {
			getCountry().then(data => {
				console.log(data);
				this.countryData = [data];
				// 设置区号数据
				this.countryCodeData = [data.map(item => ({
					...item,
					Code: `+${item.Code}`
				}))];

				// 如果是编辑模式且已有国家ID，预加载省份数据
				if (this.addressForm.country_id && this.addressForm.Province) {
					this.preloadProvinceData();
				}
			})
		},
		handleCountry() {
			this.isCountryShow = true;
		},
		chooseCountry(e) {
			const {
				CId,
				Country,
				Code
			} = e.value[0];
			if (Country !== this.addressForm.Country) {
				this.addressForm.Province = "";
				this.addressForm.State = "";
			}
			this.addressForm.country_id = CId;
			this.addressForm.Country = Country;
			// 手机号码区号自动跟随国家设置
			this.addressForm.CountryCode = `+${Code}`;
			this.isCountryShow = false;
		},
		changeCountry() {
			this.addressForm.Province = "";
			this.addressForm.State = "";
		},
		chooseProvince(e) {
			const {
				SId,
				States
			} = e.value[0];
			this.addressForm.Province = SId;
			this.addressForm.State = States;
			this.isProvinceShow = false;
		},

		// 选择区号
		chooseCountryCode(e) {
			const { Code } = e.value[0];
			this.addressForm.CountryCode = Code;
			this.isCountryCodeShow = false;
		},

		/**
		 * 获取国家选择器的默认索引
		 */
		getCountryDefaultIndex() {
			if (!this.addressForm.country_id || !this.countryData.length) {
				return [0];
			}

			const index = this.countryData[0].findIndex(item => item.CId == this.addressForm.country_id);
			const defaultIndex = Math.max(0, index);
			console.log('🌍 国家默认索引:', defaultIndex, '国家ID:', this.addressForm.country_id);
			return [defaultIndex];
		},

		/**
		 * 获取省份选择器的默认索引
		 */
		getProvinceDefaultIndex() {
			if (!this.addressForm.Province || !this.provinceData.length) {
				return [0];
			}

			const index = this.provinceData[0].findIndex(item => item.SId == this.addressForm.Province);
			const defaultIndex = Math.max(0, index);
			console.log('🏛️ 省份默认索引:', defaultIndex, '省份ID:', this.addressForm.Province);
			return [defaultIndex];
		},
		handleOpenProvince() {
			if (!this.addressForm.country_id) {
				return this.$toast({
					title: 'Please select country first'
				});
			}
			this.isProvinceShow = true;

			this.getAddressSelectCountry()
		},
		getAddressSelectCountry() {
			addressSelectCountry({
				CId: this.addressForm.country_id
			}).then(res => {
				console.log(res);
				this.provinceData = [res.contents];
			})
		},

		/**
		 * 预加载省份数据（用于编辑模式）
		 */
		preloadProvinceData() {
			if (!this.addressForm.country_id) {
				return;
			}

			addressSelectCountry({
				CId: this.addressForm.country_id
			}).then(res => {
				console.log('预加载省份数据:', res);
				this.provinceData = [res.contents];
			}).catch(error => {
				console.error('预加载省份数据失败:', error);
			});
		},

		// 验证邮政编码格式
		validateZipCode(zipCode, countryId) {
			if (!zipCode.trim()) return false;

			// 美国 (country_id == 226)
			if (countryId == 226) {
				const zipCodeRegex = /(^\d{5}$)|(^\d{5}-\d{4}$)/;
				return zipCodeRegex.test(zipCode);
			}
			// 加拿大 (country_id == 38)
			else if (countryId == 38) {
				const zipCodeRegex = /^[ABCEGHJKLMNPRSTVXYabceghjklmnprstvxy]{1}\d{1}[A-Za-z]{1}\s\d{1}[A-Za-z]{1}\d{1}$/;
				return zipCodeRegex.test(zipCode);
			}
			// 其他国家，只检查是否为空
			else {
				return zipCode.trim().length > 0;
			}
		},
		handleConfirm() {
			if (!this.addressForm.FirstName.trim()) return this.$toast({
				title: 'Please fill in FirstName'
			});
			if (!this.addressForm.LastName.trim()) return this.$toast({
				title: 'Please fill in LastName'
			});
			if (!this.addressForm.AddressLine1.trim()) return this.$toast({
				title: 'Please fill in AddressLine1'
			});
			if (!this.addressForm.City.trim()) return this.$toast({
				title: 'Please fill in City'
			});
			// 验证邮政编码格式
			if (!this.validateZipCode(this.addressForm.ZipCode, this.addressForm.country_id)) {
				let errorMessage = 'Please fill in ZipCode';
				if (this.addressForm.country_id == 226) {
					errorMessage = 'Please fill in with the correct format xxxxx / xxxxx-xxxx.';
				} else if (this.addressForm.country_id == 38) {
					errorMessage = 'Please fill in with the correct format "A9A 9A9".';
				}
				return this.$toast({
					title: errorMessage
				});
			}
			if (!this.addressForm.country_id) return this.$toast({
				title: 'Please select a country'
			});
			if (!this.addressForm.Province) return this.$toast({
				title: 'Please select a Province'
			});
			if (!this.addressForm.PhoneNumber.trim()) return this.$toast({
				title: 'Please fill in PhoneNumber'
			});

			// 验证手机号码格式（大部分国家是10位数）
			const phoneRegex = /^\d{10}$/;
			if (!phoneRegex.test(this.addressForm.PhoneNumber)) {
				return this.$toast({
					title: 'Please enter a valid 10-digit phone number'
				});
			}

			uni.showLoading({
				title: "Be saving",
				mask: true
			})

			addressAdd(this.addressForm).then(res => {
				if (this.addressForm.AId) {
					this.$toast({
						title: 'Edit success'
					});
				} else {
					this.$toast({
						title: 'Add success'
					});
				}

				if (this.news == 1) {
					uni.$emit('handleAddFirstAddress', true)
				} else {
					uni.$emit('handleAddRefresh', true)
				}
				setTimeout(() => {
					this.$tab.navigateBack();
				}, 300);
			})
		}
	},
}
</script>

<style lang="scss" scoped>
.add-address-box {
	padding: 23rpx 31rpx;

	.form-box {
		padding: 0 31rpx;

		.uni-input-placeholder {
			font-weight: bold;
			font-size: 31rpx;
			color: #8C8C8C;
		}

		::v-deep .u-line {
			border-color: #F7F7F7 !important;
		}

		.phone-input-container {
			display: flex;
			align-items: center;
			width: 100%;

			.country-code-selector {
				display: flex;
				align-items: center;
				padding-right: 20rpx;
				border-right: 1px solid #F7F7F7;
				margin-right: 20rpx;
				min-width: 70rpx;
				cursor: pointer;
				justify-content: center;

				.country-code {
					font-size: 31rpx;
					color: #262626;
					font-weight: bold;
					margin-right: 8rpx;
				}
			}

			.phone-input {
				flex: 1;
			}
		}
	}

	.confirm-btn {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 96rpx;
	}
}
</style>