<template>
    <view class="collapsible-wrapper" :class="{ 'is-expanded': isExpanded }" @click="toggleExpand">
        <view class="collapsible-content">
            <slot name="content"></slot>
        </view>
        <view class="expand-icon">
            <view class="icon-wrapper">
                <u-icon name="arrow-down" color="#8C8C8C" size="12"></u-icon>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: 'CollapsibleComponent',
    data() {
        return {
            isExpanded: false
        }
    },
    methods: {
        toggleExpand() {
            this.isExpanded = !this.isExpanded;
        }
    }
}
</script>

<style lang="scss" scoped>
.collapsible-wrapper {
    width: 265rpx;
    min-height: 92rpx;
    background: #F0F0F0;
    border-radius: 12rpx;
    position: relative;
    padding: 11rpx 35rpx 11rpx 15rpx;
    box-sizing: border-box;
    cursor: pointer;

    .collapsible-content {
        min-height: 70rpx;
        padding-right: 10rpx;
        /* 添加平滑过渡效果 */
        transition: height 0.3s ease-in-out;

        ::v-deep uni-view {
            font-family: "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
            font-weight: 400;
            font-size: 25rpx;
            line-height: 35rpx;
            color: #8C8C8C;
            width: 100%;
            box-sizing: border-box;
            padding: 0;
            margin: 0;
            word-break: break-word;
            word-wrap: break-word;
            white-space: normal;
            /* 统一使用normal，避免跳动 */
            /* 添加字体渲染优化 */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
            /* 字体加载优化 */
            font-display: swap;
            /* 防止字体跳动 */
            font-variant-numeric: tabular-nums;
            font-feature-settings: "tnum";
        }
    }

    &:not(.is-expanded) {
        height: 92rpx;
        overflow: hidden;

        .collapsible-content {
            height: 70rpx;
            overflow: hidden;

            ::v-deep uni-view {
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                /* 标准属性 */
                overflow: hidden;
                white-space: normal;
                /* 确保字体在截断时保持一致 */
                font-variant-numeric: tabular-nums;
                /* 确保收起状态下也没有额外的padding */
                padding-top: 0 !important;
                padding-bottom: 0 !important;
                margin: 0;
            }
        }
    }

    &.is-expanded {
        height: auto;

        .collapsible-content {
            height: auto;
            overflow: visible;

            ::v-deep uni-view {
                /* 展开时重置line-clamp相关属性 */
                display: block;
                -webkit-line-clamp: unset;
                line-clamp: unset;
                -webkit-box-orient: unset;
                white-space: normal;
                /* 确保字体渲染保持一致 */
                font-variant-numeric: tabular-nums;
                /* 强制移除可能导致跳动的padding */
                padding-top: 0 !important;
                padding-bottom: 0 !important;
                /* 如果需要间距，统一使用margin */
                margin: 0;
            }
        }

        .expand-icon {
            .icon-wrapper {
                transform: rotate(180deg);
            }
        }
    }

    .expand-icon {
        position: absolute;
        right: 10rpx;
        top: 46rpx;
        transform: translateY(-50%);
        width: 24rpx;
        height: 24rpx;
        z-index: 1;

        .icon-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            transition: transform 0.2s ease;
        }
    }
}
</style>