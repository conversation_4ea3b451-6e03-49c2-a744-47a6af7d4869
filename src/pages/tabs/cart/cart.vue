<template>
	<view>
		<u-loading-page :loading="isPageLoading" bg-color="#F0F0F0" iconSize="30"
			loadingText="Loading"></u-loading-page>

		<view class="cart-container" v-if="!isPageLoading">
			<navbar :isLeftShow="false"></navbar>
			<template v-if="cartData.length">
				<view class="header-options">
					<view>
						<checkbox-group @change="checkboxAllChange">
							<label class="flex align-center">
								<checkbox value="all" :checked="!!isAllSelect" class="checkbox-flg checkbox-all"
									v-bind="checkboxObj" />
								<text class='checkAll'>All</text>
							</label>
						</checkbox-group>
					</view>
					<view class="administrate" @click="footerswitch = !footerswitch">
						{{ footerswitch ? 'Manage' : 'Cancel' }}
					</view>
				</view>

				<view class="cart-goods-box">
					<checkbox-group class="checkbox-group-box" @change="checkboxChange">
						<view class="cart-item" v-for="(item, index) in cartData" :key="index">
							<view class="cart-item-box">
								<view class="cart-item-left">
									<checkbox :value="item.CId" :checked="item.checked" class="checkbox-flg"
										v-bind="checkboxObj" />
								</view>

								<view class="cart-item-right">
									<view class="cart-main-box" hover-class='none'>
										<view class="cart-main-box-img"
											style="width:185rpx;display:flex; align-items:self-start; overflow:hidden;">
											<u-image :src="item.PicPath" radius="15rpx" mode="widthFix" width="185rpx"
												height='132rpx'
												@click="$tab.navigateTo(`/pages/goodsDetail/goodsDetail?ProId=${item.ProId}&CId=${item.CId}`)">
											</u-image>
										</view>
										<view class="goods-info">
											<u--text :text="item.Name_en" lines="1" color="#262626" bold
												class="PingFang-SC-Bold" style="line-height: 46rpx;"></u--text>
											<!-- 普通商品Sku -->
											<view v-if="item.Property != 0" :style="{ margin: '11rpx 0 15rpx' }">
												<block
													v-if="item.BuyType === '0' || item.BuyType === '5' && Object.keys(item.Property).length">
													<MyCollapse>
														<template #content>
															<view v-for="(value, key) in item.Property" :key="key">
																{{ key }}: {{ value }}
															</view>
														</template>
													</MyCollapse>
												</block>

												<!-- 组合促销 主Sku -->
												<block
													v-if="item.BuyType === '4' && mainSku(item.ProId, item.Property) && Object.keys(item.Property)">
													<MyCollapse>
														<template #content>
															<view
																v-for="(value, key) in mainSku(item.ProId, item.Property)"
																:key="key">
																{{ key }}: {{ value }}
															</view>
														</template>
													</MyCollapse>
												</block>
											</view>

											<view class="goods-price flex justify-between align-center">
												<u-number-box :disabled="item.BuyType === '4'" v-model="item.Qty"
													:min="1" :max="item.Stock" @change="changeQty(item, $event)">
													<view slot="minus" class="minus">
														<!-- <u-icon name="minus" size="12" color="#8C8C8C"></u-icon> -->
														<image src="@/static/assets/common/minus.png"
															style="width:35rpx;height:35rpx"></image>
													</view>
													<text slot="input" class="input">{{ item.Qty }}</text>
													<view slot="plus" class="plus">
														<!-- <u-icon name="plus" size="12" color="#8C8C8C"></u-icon> -->
														<image src="@/static/assets/common/plus.png"
															style="width:35rpx;height:35rpx"></image>
													</view>
												</u-number-box>
												<u--text :text="`${priceSymbol}${item.Price}`" color="#FF5A1E" bold
													size="34rpx" align="right"></u--text>
											</view>
										</view>
									</view>

									<!-- 组合分类 -->
									<view v-if="item.package_row.length">
										<view class="assembly-sort-box">
											<view class="left-icon">
												<u-icon name="plus" color="#CFCFCF" size="20"></u-icon>
											</view>
											<view class="assembly-sort-right">
												<view class="child-item" v-for="(cItem, cIndex) in item.package_row"
													:key="cIndex">
													<u--image :src="cItem.PicPath_0" width="158rpx" height="106rpx"
														radius="15rpx" mode="aspectFit"></u--image>
													<view class="goods-info">
														<u--text :text="cItem.Name_en" lines="1" color="#262626"
															size="13px" bold></u--text>

														<!-- 组合促销 子Sku -->
														<MyCollapse>
															<template #content>
																<view
																	v-for="(value, key) in mainSku(cItem.ProId, item.Property)"
																	:key="key">
																	{{ key }}: {{ value }}
																</view>
															</template>
														</MyCollapse>
													</view>
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>


							<view class="free-gift" v-if="(index === cartData.length - 1) && giftNum > 0">
								<!-- <i class="iconfont icon-gift"></i> -->
								<image src="@/static/assets/common/gift.png"
									style="width:28rpx;height:28rpx;margin-right:10rpx"></image>
								<text>
									Free Gift({{ giftNum }} Pieces)
								</text>
							</view>
						</view>
					</checkbox-group>
				</view>
				<view class="footer-payment-box fixed-bottom-compatible" style="bottom: calc(var(--custom-window-bottom) + 2rpx)">
					<view class="payment-left">
						<view v-show="footerswitch" class="payment-left-wrap">
							<text class="title">Grand Total:</text>
							<text class="total-price">{{ priceSymbol }}{{ selectCountPrice }}</text>
						</view>
						<view v-show="!footerswitch">
							<text class="title">Item({{ selectValue.length }})</text>
						</view>
					</view>
					<view class="payment-right">
						<u-button :loading="isBtnLoading"
							:text="footerswitch ? `Checkout${selectValue.length > 0 ? `(${selectValue.length})` : ''}` : 'Delete'"
							class="payment-btn" color="#FF5A1E" @click="handleGroupOption">
						</u-button>
					</view>
				</view>
			</template>
			<template v-else>
				<u-empty mode="car" text="The shopping cart is empty" marginTop="150">
				</u-empty>
			</template>
		</view>
	</view>
</template>

<script>
import {
	getCartCartItemList,
	getCartEditCart,
	getCartBatchDelete
} from '@/api/products.js';
import {
	mapGetters
} from 'vuex';
import SkuMixins from '@/mixins/sku.js';
import tabBarResetScroll from "@/mixins/tabBarResetScroll.js";
import MyCollapse from './components/MyCollapse.vue';

export default {
	mixins: [SkuMixins, tabBarResetScroll],
	components: {
		MyCollapse
	},
	data() {
		return {
			isPageLoading: true,
			isAllSelect: false,
			selectValue: [], //选中的数据
			footerswitch: true,
			isBtnLoading: false,
			cartData: [],
			giftNum: 0,
			selectCountPrice: 0,
			checkboxObj: {
				activeBackgroundColor: '#FF5A1E',
				activeBorderColor: '#FF5A1E',
				iconColor: '#fff'
			}
		};
	},
	computed: {
		...mapGetters(['isLogin', 'priceSymbol', 'cartNum'])
	},
	onShow() {
		if (!this.isLogin) {
			this.$toast({
				title: '请重新登录'
			})
		}
		// if (this.isLogin) {
		this.getCartCartItemList();
		this.selectValue = []; //选中的数据
		this.isAllSelect = false;
		this.footerswitch = true;
		this.selectCountPrice = 0;
		// }
	},
	methods: {
		// 获取购物车列表
		async getCartCartItemList() {
			const res = await getCartCartItemList();
			this.cartData = res?.cart_row ?? [];
			this.giftNum = res?.gift_num;
			this.isPageLoading = false;
			this.$store.commit('SET_CARTNUM', res?.row_count);

			// 恢复勾选状态
			const checked = uni.getStorageSync('cart_checked') || [];
			let validChecked = [];
			this.cartData = this.cartData.map(item => {
				if (checked.includes(item.CId)) {
					item.checked = true;
					validChecked.push(item.CId);
				} else {
					item.checked = false;
				}
				return item;
			});
			this.selectValue = validChecked;
			this.isAllSelect = validChecked.length === this.cartData.length && this.cartData.length > 0;
			this.switchSelect();

			// 清理无效的勾选项
			uni.setStorageSync('cart_checked', validChecked);
		},
		checkboxAllChange(event) {
			console.log(event)
			const {
				value
			} = event.detail;
			if (value.length) {
				this.setAllSelectValue(1)
			} else {
				this.setAllSelectValue(0)
			}
		},
		setAllSelectValue(status) {
			let selectValue = [];

			let newValid = this.cartData.map(item => {
				if (status === 1) {
					item.checked = true;
					this.isAllSelect = true;
					selectValue.push(item.CId)
				} else {
					item.checked = false;
					this.isAllSelect = false;
				}

				return item;
			})

			this.cartData = newValid;
			this.selectValue = selectValue;
			this.switchSelect();
			// 这里调用
			this.saveCheckedToLocal();
		},
		checkboxChange(event) {
			let value = event.detail.value;
			let cartData = this.cartData;

			let newCardData = cartData.map(item => {
				if (value.includes(item.CId)) {
					item.checked = true;
				} else {
					item.checked = false;
				}
				return item;
			})

			this.cartData = newCardData;
			this.selectValue = value;
			this.isAllSelect = value.length === cartData.length;
			this.switchSelect();
			// 这里调用
			this.saveCheckedToLocal();
		},
		saveCheckedToLocal() {
			uni.setStorageSync('cart_checked', this.selectValue);
		},
		switchSelect() {
			let selectCountPrice = 0;
			let cartData = this.cartData;
			let selectValue = this.selectValue;

			if (selectValue.length < 1) {
				this.selectCountPrice = selectCountPrice;
			} else {
				for (const index in cartData) {
					if (selectValue.includes(cartData[index]?.CId)) {
						selectCountPrice += cartData[index].Price * cartData[index].Qty;
					}
				}
				this.selectCountPrice = selectCountPrice.toFixed(2);
			}
		},
		changeQty(item, event) {
			const data = {
				CId: item.CId,
				Qty: event.value,
				ProId: item.ProId,
				CIdAry: `0,${item.CId}`
			}
			getCartEditCart(data).then(res => {
				console.log(res)
				item.Qty = res.qty
				this.switchSelect();
			})
		},
		// 组合操作
		handleGroupOption() {
			if (!this.selectValue.length) {
				this.$toast({
					title: 'Please select product'
				})
				return;
			}

			this.isBtnLoading = true;

			if (this.footerswitch) {
				this.isBtnLoading = false;
				// 支付
				this.$tab.navigateTo(
					`/pages/order_details/order_details?CIdData=${this.selectValue.join('.')}&isBuyType=0`)
			} else {
				// 删除	
				let data = {};
				this.selectValue.forEach(CId => {
					data[`cid_list[${CId}]`] = CId;
				})

				getCartBatchDelete(data).then(res => {
					this.$toast({
						title: 'Delete successfully'
					})
					this.selectValue = [];
					this.isAllSelect = false;
					this.getCartCartItemList();
				}).catch(err => {
					this.$toast({
						title: 'Delete failed'
					})
				}).finally(() => {
					this.isBtnLoading = false;
				})
			}
		}
	},
}
</script>

<style lang="scss" scoped>
.goods-info ::v-deep .collapsible-container {
	.sku-left-box {
		line-height: 1.2;
	}
}

.cart-container {
	padding: 0 31rpx;
	box-sizing: border-box;
	font-family: PingFang SC-Bold !important;

	.checkbox-flg {
		::v-deep .uni-checkbox-input {
			border-radius: 50%;
			width: 42rpx;
			height: 42rpx;
		}

		&.checkbox-all ::v-deep.uni-checkbox-input {
			border-radius: 50%;
			width: 42rpx;
			height: 42rpx;
			border: 0;
		}
	}

	.sku-list {
		display: inline-flex;
		justify-content: space-between;
		align-items: center;
		@include flex-gap(0, 20rpx); // 替换了 column-gap
		width: min-content;
		max-height: 92rpx;
		padding: 15rpx;
		background: #F0F0F0;
		border-radius: 12rpx;
		box-sizing: border-box;
		overflow: hidden;
		transition: max-height .3s ease-in-out, overflow .3s ease-in-out;

		.sku-left-box {
			height: 100%;
		}

		text {
			font-weight: 400;
			font-size: 27rpx;
			color: #8C8C8C;
		}

		&.expanded {
			max-height: fit-content !important;
			overflow: visible;
			/* 展开时允许内容溢出 */
		}
	}

	.header-options {
		position: fixed;
		left: 0;
		right: 0;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #F0F0F0;
		padding: 23rpx 31rpx 19rpx;
		z-index: 10;

		.checkAll {
			font-weight: bold;
			font-size: 31rpx;
			color: #262626;
		}

		.administrate {
			background: #FFFFFF;
			border-radius: 52rpx;
			font-weight: bold;
			font-size: 27rpx;
			color: #262626;
			padding: 0 26rpx;
			height: 54rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 13px;
			font-family: PingFang SC-Bold;
		}
	}

	.cart-goods-box {
		margin-top: 55px;
		padding-bottom: calc(108rpx + 94rpx + env(safe-area-inset-bottom, 20rpx));

		.checkbox-group-box {
			display: grid;
			@include flex-gap(31rpx, 0); // 替换了 row-gap
		}

		.cart-item {
			background: #fff;
			border-radius: 31rpx;
			padding: 31rpx;
			box-sizing: border-box;


			.cart-item-box {
				display: flex;
				justify-content: space-between;
				@include flex-gap(0, 8rpx); // 替换了 column-gap

				.cart-item-left {
					::v-deep .uni-checkbox-input {
						margin-right: 0;
						outline: none !important;
						border: 1px solid #d1d1d1 !important;
					}

					::v-deep .uni-checkbox-input-checked {
						border: 1px solid #FF5A1E !important;
					}



				}

				.cart-item-right {
					flex: 1;
					display: grid;
					@include flex-gap(31rpx, 0); // 替换了 row-gap

					.cart-main-box {
						display: flex;
						@include flex-gap(0, 31rpx); // 替换了 column-gap

						.cart-main-box-img {
							::v-deep .u-image {
								height: auto !important;

								.u-image__image {
									margin: auto;
								}
							}
						}

						.goods-info {
							display: grid;
							flex: 1;

							.goods-price {

								.minus,
								.plus {
									display: flex;
									justify-content: center;
									align-items: center;
									width: 34rpx;
									height: 34rpx;
									border-radius: 50%;
								}

								.input {
									font-weight: 400;
									font-size: 30rpx;
									color: #262626;
									margin: 0 14rpx;
								}
							}
						}
					}

					.assembly-sort-box {
						display: flex;
						@include flex-gap(0, 13rpx); // 替换了 column-gap

						.left-icon {
							margin-top: 14px;
						}

						.assembly-sort-right {
							flex: 1;
							display: grid;
							@include flex-gap(31rpx, 0); // 替换了 row-gap

							.child-item {
								display: flex;
								@include flex-gap(0, 21rpx); // 替换了 column-gap

								.goods-info {
									display: grid;
									@include flex-gap(12rpx, 0); // 替换了 row-gap
									flex: 1;

									.goods-price {

										.minus,
										.plus {
											display: flex;
											justify-content: center;
											align-items: center;
											width: 34rpx;
											height: 34rpx;
											border-radius: 50%;
											border: 1px solid #8C8C8C;
										}

										.input {
											font-weight: 400;
											font-size: 30rpx;
											color: #262626;
											margin: 0 14rpx;
										}
									}
								}
							}
						}
					}
				}
			}

			.free-gift {
				background: #EEEEEE;
				border-radius: 12rpx;
				padding: 10rpx 19rpx;
				color: #8C8C8C;
				display: inline-flex;
				align-items: center;
				margin-top: 31rpx;
				font-family: PingFang SC-Regular;

				.icon-gift {
					width: 40rpx;
					height: 40rpx;

				}

				text {
					font-weight: 400;
					font-size: 27rpx;
					margin-left: 4rpx;
				}
			}
		}
	}

	.footer-payment-box {
		position: fixed;
		z-index: 2;
		bottom: calc(var(--custom-window-bottom) + 2rpx);
		left: 0;
		right: 0;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #FFFFFF;
		border-radius: 31rpx 31rpx 0px 0px;
		padding: 17rpx 31rpx;
		box-sizing: border-box;
		height: 108rpx;

		.payment-left {
			flex: 1;

			.payment-left-wrap {
				display: flex;
				justify-content: space-between;
				padding-right: 20rpx;
			}

			.title {
				font-weight: bold;
				font-size: 31rpx;
				color: #262626;
			}

			.total-price {
				font-weight: bold;
				font-size: 35rpx;
				color: #FF5A1E;
				margin-left: 6rpx;
			}
		}

		.payment-right {
			.payment-btn {
				width: 221rpx;
				height: 73rpx;
				background: #FF5A1E;
				border-radius: 52rpx;
				font-weight: 600;
				font-size: 31rpx;
				color: #FFFFFF;
			}
		}
	}
}
</style>