<template>
	<view>
		<navbar autoBack>
			<template v-if="fromOrderDetail" slot="title">
				<text>Select Address</text>
			</template>
		</navbar>

		<mescroll-body :down="{ use: addressData.length > 0 }" @init="mescrollInit" @down="downCallback"
			@up="upCallback" style="min-height: 80vh;">
			<view class="address-book-box PingFang-SC-Bold">
				<!-- 重写的地址项布局 - 不使用复杂的cell结构 -->
				<view class="address-item-wrapper common-box-style" v-for="(item, index) in addressData" :key="index">
					
					<!-- 地址项内容 -->
					<view class="address-item-content" @click="handleAddressClick(item, $event)">
						<view class="address-item-check" v-if='fromOrderDetail'>
														<!-- 选中状态图标 -->
 
							<!-- <u-icon  name="checkmark-circle"
								color="#D9D9D9" size="38rpx" class="select-icon select-icon-default"></u-icon> -->
								<radio-group @change="(e) => radioChange(e, index)">
								<radio class="checked-radio" :value="index.toString()"
									:checked="item.AId == currentAId" backgroundColor="#F0F0F0"
									activeBackgroundColor="#FF5A1E"></radio>
							</radio-group>
						</view>
						<!-- 左侧图标区域 -->
						<view class="address-left-icons">
							<!-- 用户头像 -->
							<u-avatar class="user-avatar" :text="avatarName(item.FirstName)" size="75rpx"
								bg-color="#ffded2" color="#FF5A1E" font-size="28rpx"></u-avatar>
						</view>
						
						<!-- 中间内容区域 -->
						<view class="address-main-content">
							<!-- 姓名和电话 -->
							<view class="address-name-phone">
								{{ item.FirstName }}
								&nbsp;&nbsp;&nbsp;
								{{ item.PhoneNumber }}
							</view>
							
							<!-- 地址详情 - 第一行 -->
							<view class="address-detail-line">
								<text class="address-text">{{ addressDetail(item) }}</text>
							</view>
							
							<!-- 地址详情 - 第二行 -->
							<view class="address-detail-line">
								<text class="address-text">{{ formatAddressDetail(item) }}</text>
							</view>
						</view>
						
						<!-- 右侧操作区域 -->
						<view class="address-right-actions">
							<view class="divider-line">线条</view>
							<u-icon name="edit-pen" size="22px" color="#8C8C8C" 
								@click="editAddress(item, $event)"></u-icon>
						</view>
					</view>
					
					<!-- 分隔线 -->
					<u-line color="#F7F7F7" length="590rpx" margin="0 0 0 31rpx"></u-line>
					
					<!-- 默认地址设置和删除操作 -->
					<view class="address-actions-wrapper">
						<view class="default-setting">
							<radio-group @change="(e) => radioChange(e, index)">
								<radio class="checked-radio" :value="index.toString()"
									:checked="item.IsDefault == 1 ? true : false" backgroundColor="#F0F0F0"
									activeBackgroundColor="#FF5A1E">Defaults</radio>
							</radio-group>
						</view>
						<view class="delete-action">
							<text class="delete-text" @click="delAddress(index)">Delete</text>
						</view>
					</view>
				</view>
			</view>
		</mescroll-body>

		<SubmitButton :class="['confirm-btn', { 'hidden': isButtonHidden }]" text="Add Address"
			@handleConfirm="addAddress">
		</SubmitButton>

		<u-modal :show="show" title="Are you sure delete it?" confirmText="Sure" cancelText="Cancel" showCancelButton
			closeOnClickOverlay confirmColor="#FF5A1E" @confirm="handleDeleteAddress" @cancel="show = false"
			@close="show = false"></u-modal>
	</view>
</template>

<script>
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import {
	userAddress,
	addressDelete,
	setDefaultAddress
} from '@/api/address.js';

export default {
	mixins: [MescrollMixin],
	data() {
		return {
			show: false,
			deleteIndex: null,
			addressData: [],
			lastScrollTop: 0,
			isButtonHidden: false,
			// 来自订单详情页的参数
			fromOrderDetail: false,
			currentAId: '',
			isNavigating: false
		};
	},
	onLoad(options) {
		// 检查是否来自订单详情页
		if (options.fromOrderDetail) {
			this.fromOrderDetail = true;
			this.currentAId = options.currentAId || '';
		}
	},
	computed: {
		addressDetail() {
			return (item) => {
				return `${item.Country},${item.State},${item.City},${item.AddressLine1}`
			}
		},
		avatarName() {
			return (name) => {
				return name.substring(0, 1);
			}
		}
	},
	onShow() {
		uni.$once('handleAddRefresh', (isRefresh) => {
			console.log('isRefresh', isRefresh)
			if (isRefresh) {
				this.reloadList();
			}
		})
	},
	onPageScroll({
		scrollTop
	}) {
		if (scrollTop > this.lastScrollTop) {
			this.isButtonHidden = true;
		} else {
			this.isButtonHidden = false;
		}
		this.lastScrollTop = scrollTop;
	},
	methods: {
		reloadList() {
			this.mescroll.resetUpScroll();
		},
		upCallback(page) {
			userAddress({
				page: page.num,
				size: page.size,
				type: 0
			}).then(res => {
				if (page.num == 1) {
					this.addressData = [];
				}

				this.addressData = this.addressData.concat(res.result);
				this.mescroll.endByPage(res.result.length, res.total_pages);
			}).catch(() => {
				this.mescroll.endErr();
			})
		},
		radioChange(e, itemIndex) {
			// 如果传递了itemIndex参数，使用它；否则从事件中获取
			let index = itemIndex !== undefined ? itemIndex : parseInt(e.detail.value);
			let address = this.addressData[index];
			if (!address) return this.$toast({
				title: 'The default address you set does not exist!'
			});
			setDefaultAddress({
				AId: address.AId
			}).then(res => {
				for (let i = 0; i < this.addressData.length; i++) {
					if (index === i) {
						this.addressData[i].IsDefault = 1;
					} else {
						this.addressData[i].IsDefault = 0;
					}
				}
				this.$toast({
					title: 'Successful Setting'
				})
			}).catch(err => {
				this.$toast({
					title: 'Setting Failed',
					icon: 'error'
				})
			})
		},
		// 编辑地址
		editAddress(item, e) {
			this.isNavigating = true;
			// 只处理编辑逻辑，不用 stopPropagation
			console.log('编辑地址:', item);
			const {
				CountryData,
				...editData
			} = item;
			uni.navigateTo({
				url: `/pages/tabs/cart/addAddress/addAddress?editData=${JSON.stringify(editData)}`
			})
		},
		addAddress(e) {
			this.isNavigating = true;
			this.$tab.navigateTo(`/pages/tabs/cart/addAddress/addAddress`)
		},
		formatAddressDetail(item) {
			const state = item.StateAbbr || item.State || '';
			const zip = item.ZipCode || '';
			const address = item.AddressLine1 || '';
			return [state, zip].filter(Boolean).join(' ');
		},
		handleDeleteAddress() {
			const address = this.addressData[this.deleteIndex];

			addressDelete({
				AId: address.AId
			}).then(res => {
				this.$toast({
					title: 'Successfully deleted'
				});
				this.addressData.splice(this.deleteIndex, 1);
				this.show = false;

				this.reloadList();
			}).catch((err) => {
				this.$toast({
					title: 'Delete failed'
				});
				this.show = false;
			})
		},
		// 删除地址
		delAddress(index) {
			const address = this.addressData[index];
			if (!address) return this.$toast({
				title: 'The address you deleted does not exist!'
			});

			this.show = true;
			this.deleteIndex = index;
		},
		// 处理地址点击
		handleAddressClick(item, e) {
			// 判断事件来源，若是编辑按钮则不处理
			if (e && e.target && e.target.className && String(e.target.className).includes('edit-pen')) {
				return;
			}
			if (this.fromOrderDetail) {
				this.selectAddressForOrder(item);
			} else {
				// 普通地址管理，可以添加其他逻辑
				console.log('普通地址点击:', item);
			}
		},
		// 为订单选择地址
		async selectAddressForOrder(address) {
			if (this.isNavigating) {
				this.isNavigating = false;
				return;
			}
			console.log('🚀 选择地址:', address);
			this.currentAId = address.AId;
			
			// Store the address first
			uni.setStorageSync('selectedAddress', address);
			
			// Emit the event
			console.log('🚀 发送 addressSelected 事件');
			uni.$emit('addressSelected', address);
			
			// Wait a moment to ensure event propagation
			await new Promise(resolve => setTimeout(resolve, 100));
			
			// Navigate back
			console.log('🚀 准备返回上一页');
			this.$tab.navigateBack();
		}
	}
}
</script>

<style lang="scss" scoped>
.address-book-box {
	padding: 23rpx 31rpx;
	display: flex;
	flex-direction: column;

	// 地址项包装器
	.address-item-wrapper {
		background: #fff;
		border-radius: 20rpx;
		overflow: hidden;
		margin-bottom: 31rpx;
		
		.address-item-content {
			padding: 30rpx;
			display: flex;
			align-items: flex-start;
			.address-item-check{
				align-self: center;
				margin-right: 8rpx;
				.select-icon-default{
				}
			}
			// 左侧图标区域
			.address-left-icons {
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 75rpx; // 固定宽度，与头像宽度一致
				flex-shrink: 0;
				margin-right: 20rpx;
				
				.select-icon {
					margin-bottom: 10rpx;
				}
				
				.user-avatar {
					::v-deep .u-text__value {
						font-weight: bold !important;
					}
				}
			}
			
			// 中间内容区域 - 兼容低版本机型的宽度限制
			.address-main-content {
				flex: 1;
				min-width: 0; // 允许flex子项收缩
				width: 0; // 配合flex:1使用，让flex自动分配宽度
				overflow: hidden; // 防止内容溢出
				
				// 姓名和电话
				.address-name-phone {
					margin-bottom: 20rpx;
					font-family: 'PingFang-SC-Bold', sans-serif;
					display: flex;
					flex-wrap: wrap;
					word-break: break-all;
					color: #262626;
					font-size: 31rpx;
				}
				
				// 地址详情行
				.address-detail-line {
					margin-bottom: 10rpx;
					width: 100%;
					max-width: 100%;
					box-sizing: border-box;
					overflow: hidden; // 防止内容溢出
					
					// 最后一个地址详情行不要下边距
					&:last-child {
						margin-bottom: 0;
					}
					
					.address-text {
						font-weight: bold;
						font-size: 26rpx;
						color: #8C8C8C;
						line-height: 38rpx;
						font-family: 'PingFang-SC-Bold', sans-serif;
						
						// 兼容低版本的换行方案
						display: block;
						width: 100%;
						
						// 基础换行设置（兼容性好）
						word-break: break-all;
						word-wrap: break-word;
						white-space: normal;
						
						// 移动端webkit优化
						-webkit-word-break: break-all;
					}
				}
			}
			
			// 右侧操作区域
			.address-right-actions {
				display: flex;
				align-items: center;
				width: 60rpx; // 固定宽度，容纳分隔线和编辑图标
				flex-shrink: 0;
				margin-left: 20rpx;
				align-self: center;
				.divider-line {
					width: 2rpx;
					height: 48rpx;
					background: #D9D9D9;
					color:transparent;
					margin-right: 20rpx;
				}
			}
		}
	}
	
	// 地址操作区域（默认设置和删除）
	.address-actions-wrapper {
		padding: 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #fff;
		border-radius: 0 0 20rpx 20rpx;
		
		.default-setting {
			flex: 1;
		}
		
		.delete-action {
			.delete-text {
				font-weight: bold;
				font-size: 27rpx;
				color: rgba(0, 0, 0, 0.8);
				cursor: pointer;
			}
		}
	}

	// 保留原有的radio样式
	::v-deep uni-radio .uni-radio-input {
		border: none;
	}

	.checked-radio {
		font-weight: bold;
		font-size: 27rpx;
		color: rgba(0, 0, 0, 0.8);
		line-height: 1;

		::v-deep .uni-radio-input {
			width: 30rpx !important;
			height: 30rpx !important;
			margin-right: 20rpx;
		}
	}
}

.confirm-btn {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 96rpx;
	transition: all 0.3s ease;

	&.hidden {
		opacity: 0;
		transform: translateY(96rpx);
	}
}
</style>