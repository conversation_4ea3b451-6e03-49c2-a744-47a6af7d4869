<template>
	<view class="sort-container">
		<navbar :isLeftShow="false"></navbar>
		<view class="search-fixed-box" @click="handleSearchClick">
			<u-search v-model="searchTxt" placeholder="Search for bicycles" height="90rpx" bgColor="#fff"
				placeholderColor="#595959" :showAction="true" actionText="Search" :actionStyle="actionStyle"
				:disabled="true" :animation="true" searchIcon="/static/assets/home/<USER>" @search="onSearchGoods"
				@click="handleSearchClick"></u-search>
		</view>

		<view class="fixed-bar"></view>
		<uni-section class="goods-box" :title="item.Name" titleFontSize="19" titleColor="#262626"
			v-for="(item, index) in bikeList" :key="index">
			<block v-if="item.Child.length">
				<view class="goods-container">
					<view class="bike-each-box" v-for="cItem in item.Child" :key="cItem.CateId"
						@click="onBikeToGoods(cItem)">
						<view class="goods-img-box">
							<u--image class="goodsImg" :src="cItem.PicPath_app" width="100%" height="auto"
								mode="widthFix" bgColor="#fff"></u--image>
						</view>
						<text class="bikeName" size="14px" color="#8C8C8C">{{ cItem.Category_en }}</text>
					</view>

				</view>
			</block>
			<block v-else>
				<u-empty mode="data" text="No Data" iconSize="40"></u-empty>
			</block>
		</uni-section>
	</view>
</template>

<script>
import {
	collectionsList
} from "@/api/home.js";
import tabBarResetScroll from "@/mixins/tabBarResetScroll.js";

export default {
	mixins: [tabBarResetScroll],
	data() {
		return {
			searchTxt: "TRIKE",
			bikeList: [],
			actionStyle: {
				fontSize: '12px'
			}
		};
	},
	onLoad() {
		this.getCollectionsList();
	},
	onShow() {
		this.$store.commit('SET_CART_TABBAR_COUNT');
	},
	methods: {
		getCollectionsList() {
			collectionsList().then(res => {
				this.bikeList = res;
			})
		},

		onBikeToGoods(data) {
			this.$tab.navigateTo(
				`/pages/sortGoods/sortGoods?Keyword=${data.Category_en}&CateId=${data.CateId}`)
		},
		handleSearchClick() {
			this.$tab.navigateTo(`/pages/sortGoods/sortGoods`)
		}
	}
}
</script>
<style lang="scss" scoped>
.sort-container {
	padding: 0 31rpx 23rpx;
	box-sizing: border-box;
	padding-bottom: var(--window-bottom);

	.search-fixed-box {
		position: fixed;
		left: 31rpx;
		right: 31rpx;
		z-index: 10;
		padding-top: 23rpx;
		padding-bottom: 23rpx;

		&::after {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			width: 100%;
			height: calc(100% - 23rpx);
			content: '';
			background-color: transparent;
		}

		.search-box {
			padding: 31rpx !important;
			background: #FFFFFF;
			border-radius: 31rpx;

			::v-deep .u-input__content__prefix-icon {
				margin-right: 23rpx;

				.search_icon {
					width: 24px;
					height: 24px;
				}
			}

			::v-deep .u-input__content__field-wrapper__field {
				font-weight: 400;
				font-size: 27rpx !important;
				color: #595959 !important;
			}
		}
	}

	.fixed-bar {
		height: 108rpx;
		padding-top: 13px;
	}

	.goods-box {
		background-color: transparent;

		::v-deep .uni-section-header {
			padding: 0 auto 31rpx !important;
			font-weight: bold;
		}

		.goods-container {
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			@include flex-gap(23rpx, 25rpx); // 替换了 gap 双值
			font-family: PingFang SC-Bold;

			.bike-each-box {
				text-align: center;

				.goods-img-box {

					width: 212rpx;
					height: 212rpx;
					border-radius: 31rpx;
					overflow: hidden;
					background-color: #fff;
					box-sizing: border-box;

					.goodsImg {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 100%;
						height: 100%;
						// transform: scale(.7);
					}
				}

				.bikeName {
					font-weight: bold;
					font-size: 29rpx;
					color: #8C8C8C;
					margin-top: 6rpx;
					display: inline-block;
				}
			}

		}
	}
}
</style>