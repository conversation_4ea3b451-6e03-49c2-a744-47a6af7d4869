<template>
	<view class="model-list-box">
		<scroll-view class="scroll-view_H" scroll-x>
			<view :class="['model-item modelActive']" v-for="(item, index) in modleList" :key="index"
				@tap="onChooseModel(item)">
				<u--image :src="item.cate_pic" mode="aspectFit" width="104rpx" height="70rpx" radius="10rpx" />
				<u-transition :show="true" mode="slide-right">
					<text class="modle_name" v-show="true">{{ item.cate_name }}</text>
				</u-transition>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		props: {
			modleList: {
				type: Array,
				default: () => []
			}
		},
		data() {
			return {
				active: 0
			};
		},
		methods: {
			onChooseModel(item) {
				this.$tab.navigateTo(`/pages/sortGoods/sortGoods?Keyword=${item.cate_name}&CateId=${item.cate_id}`)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.model-list-box {
		margin-top: 81rpx;
		margin-bottom: 30rpx;
		margin-left: 31rpx;

		.scroll-view_H {
			white-space: nowrap;
			width: 100%;

			.model-item {
				box-sizing: border-box;
				background: #FFFFFF;
				border-radius: 30rpx;
				display: inline-flex;
				align-items: center;
				justify-content: center;
				margin-right: 23rpx;
				overflow: hidden;
				width: 64px;
				height: 64px;
				padding: 0 15rpx 0 13rpx;
				transition: width .8s ease-in-out;
				
				&.modelActive {
					width: auto;
				}

				.modle_name {
					font-weight: 800;
					font-size: 27rpx;
					color: #262626;
					font-family: PingFang SC-Heavy, PingFang, "PingFang SC" !important;
					margin-left: 13rpx;
					white-space: nowrap;
				}
			}
		}
	}
</style>