<template>
	<view>
		<view class="location-container" @tap="showLocationSelect">
			<u-icon name="map-fill" color="#FF5A1E" size="18"></u-icon>
			<text class="location-text">{{ selectedCountryName }}</text>
			<!-- <u-icon name="arrow-down" color="#101111" size="10"></u-icon> -->
			<img src="@/static/assets/common/down-loc.png" alt="">
		</view>

		<u-picker :closeOnClickOverlay="true"  :show="showPopup" :columns="columns"
			:defaultIndex="defaultIndex" @confirm="onPickerConfirm" @cancel="cancelSelect" @close="closePopup"
			title="Country" cancelText="Cancel" confirmText="Confirm" confirmColor="#FF5A1E"
			class="custom-picker-lang"></u-picker>
	</view>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import { changeOverseas } from '@/api/common.js';

export default {
	data() {
		return {
			showPopup: false,
			selectedCountryId: '',
			tempCountryId: '',
			columns: [[]],
			defaultIndex: [0]
		};
	},
	computed: {
		...mapState({
			countryList: state => state.app.lang.list,
			currentOverseasId: state => state.app.lang.id
		}),
		selectedCountryName() {
			if (!this.countryList || !this.countryList.length) return '';

			const country = this.countryList.find(item => item.OvId === this.selectedCountryId);
			return country ? country.Name_en : this.countryList[0].Name_en;
		}
	},
	methods: {
		...mapActions(['getPriceSymbol']),
		showLocationSelect() {
			// 构建picker所需的数据格式
			if (this.countryList && this.countryList.length) {
				const countryNames = this.countryList.map(item => ({
					text: item.Name_en,
					value: item.OvId
				}));
				this.columns = [countryNames];

				// 设置默认选中项
				if (this.selectedCountryId) {
					const selectedIndex = this.countryList.findIndex(item => item.OvId === this.selectedCountryId);
					if (selectedIndex !== -1) {
						this.defaultIndex = [selectedIndex];
					}
				}
			}
			this.showPopup = true;
			this.tempCountryId = this.selectedCountryId;
		},
		closePopup() {
			this.showPopup = false;
		},
		cancelSelect() {
			this.showPopup = false;
		},
		selectCountry(country) {
			this.tempCountryId = country.OvId;
		},
		async onPickerConfirm(e) {
			// 输出完整的事件对象查看结构
			// 获取选项索引 - u-picker选中的index
			if (!e.indexs || !Array.isArray(e.indexs) || e.indexs.length === 0) {
				console.log('无效的选择索引');
				this.showPopup = false;
				return;
			}

			// 获取选中的索引
			const selectedIndex = e.indexs[0];

			// 根据索引从国家列表中获取对应的国家
			if (!this.countryList || !this.countryList[selectedIndex]) {
				this.showPopup = false;
				return;
			}

			const country = this.countryList[selectedIndex];
			const selectedValue = country.OvId;
			// 如果没有变化，直接关闭选择器
			if (selectedValue === this.selectedCountryId) {
				this.showPopup = false;
				return;
			}

			// 更新选中的国家ID
			this.selectedCountryId = selectedValue;
			this.showPopup = false;
			if (country) {
				uni.showLoading({
					title: 'Changing location...'
				});

				const data = await changeOverseas({ OvId: country.OvId });
				if (data) {
					this.getPriceSymbol();
					uni.setStorageSync('selectedCountryId', this.selectedCountryId);
					uni.startPullDownRefresh();
					uni.hideLoading();
					uni.showToast({
						title: 'successfully',
						icon: 'success',
						duration: 2000
					});
					this.$emit('changeCountry', country);

				} else {
					uni.hideLoading();
					uni.showToast({
						title: 'Failed to change location',
						icon: 'error',
						duration: 2000
					});
				}
			}
		}
	},
	created() {
		this.$watch('countryList', (newVal) => {
			if (newVal && newVal.length) {
				const savedCountryId = uni.getStorageSync('selectedCountryId');

				if (savedCountryId && newVal.some(country => country.OvId === savedCountryId)) {
					this.selectedCountryId = savedCountryId;
				} else {
					this.selectedCountryId = this.currentOverseasId || newVal[0].OvId;
				}
			}
		}, { immediate: true });
	}
}
</script>

<style lang="scss" scoped>
.location-container {
	display: flex;
	align-items: center;
	padding: 0rpx 13rpx;
	background-color: rgba(255, 255, 255, 0.8);
	border-radius: 17rpx;
	height: 62rpx;
	font-size: 25rpx;

	img {
		width: 19rpx;
		vertical-align: middle;
		margin-left: 5rpx;
		position: relative;
		top: 3rpx;
	}
}

.location-text {
	margin: 0 6rpx;
	font-size: 24rpx;
	color: #333;
}

/* 自定义picker样式 */
.custom-picker-lang {

	::v-deep .uni-picker-view-indicator {
		color: #FF5A1E;
		// background-color: #FFEFE9;
		border: 0;
	}

	::v-deep .uni-picker-view-content {
		z-index: 20;
	}

	::v-deep .u-picker__view__column__item {
		color: #8C8C8C;
		// color: #FF5A1E;
		transition: all 0.2s ease-in-out;
	}

	::v-deep .u-picker__view__column__item--active {
		color: #FF5A1E;
		color: #FF5A1E;
	}
}

.popup-container {
	background-color: #fff;
	border-radius: 20rpx;
	padding-bottom: env(safe-area-inset-bottom);
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1px solid #eee;
}

.popup-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.cancel-btn {
	color: #666;
	font-size: 28rpx;
}

.confirm-btn {
	color: #FF5A1E;
	font-size: 28rpx;
	font-weight: bold;
}

.country-list {
	max-height: 40vh;
	overflow-y: auto;
}

.country-item {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1px solid #f5f5f5;
	color: #8C8C8C;

	&.selected {
		background-color: #FFEFE9;
		color: #FF5A1E;
		font-weight: 700;
	}
}
</style>