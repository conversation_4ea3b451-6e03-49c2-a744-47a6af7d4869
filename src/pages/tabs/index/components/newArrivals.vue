<template>
	<view class="new-arrivals-box">
		<view class="newArrivals-title">
			<text class="main-title">New Arrivals</text>
			<text class="view-all-btn" @click="handleToPage">View All</text>
		</view>
		<swiper class="swiper-box" :current="currentIndex" @change="onSwiperChange" :indicator-dots="false"
			:autoplay="false" :circular="false" :display-multiple-items="1" previous-margin="31rpx" next-margin="52rpx">
			<swiper-item v-for="(item, index) in newArrivalList" :key="index" class="swiper-item">
				<view class="model-item" @click.stop="onItemClick($event, item, index)">
					<view class="left-intro">
						<u--text :text="item.news_title" color="#FEFEFE" size="38rpx" bold
							style="font-family: PingFang SC-Semibold!important;line-height: 2;"
							margin="0 0 10rpx 0"></u--text>
						<u--text :text="item.news_desc" color="#FEFEFE" size="25rpx" :lines="2"
							style="white-space: normal; font-family:PingFang SC-Medium!important;line-height: 1.7;"></u--text>
						<!-- 按钮外层加个容器阻止事件冒泡 -->
						<view class="button-container" @click.stop.prevent="">
							<view class="go_btn" shape="circle" @click.stop.prevent="onGoButtonClick(item, index)">Lets
								Go</view>
						</view>
					</view>

					<image class="bike-img-box" :src="item.news_pic" style="width: 431rpx; height: 294rpx;"
						mode="aspectFit" radius="31rpx" duration="0"></image>
				</view>
			</swiper-item>
		</swiper>
	</view>
</template>

<script>
export default {
	props: {
		newArrivalList: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			currentIndex: 0
		};
	},
	methods: {
		// swiper 切换事件
		onSwiperChange(event) {
			this.currentIndex = event.detail.current;
		},

		// 点击卡片事件
		onItemClick(event, item, index) {
			console.log('点击了卡片:', event, item, index);
			// 可以根据 item 的数据跳转到对应页面
			// this.$tab.navigateTo(`/pages/goodsDetail/goodsDetail?id=${item.id}`);
		},

		// 点击 "Lets Go" 按钮事件
		onGoButtonClick(item, index) {
			// 确保阻止事件冒泡
			console.log('点击了按钮:', item, index);
			if (item.news_url) {
				this.$tab.navigateTo(item.news_url)
			}
		},

		handleToPage() {
			this.$tab.navigateTo('/pages/sortGoods/sortGoods')
		},
	}
}
</script>

<style lang="scss" scoped>
.new-arrivals-box {
	overflow: hidden;

	.newArrivals-title {
		padding-left: 29rpx;
		padding-right: 33rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 31rpx;
		box-sizing: border-box;

		.main-title {
			font-family: PingFang SC-Semibold, PingFang, "PingFang SC" !important;
			font-size: 35rpx;
			color: #262626;
		}

		.view-all-btn {
			font-family: PingFang SC-Regular, PingFang, "PingFang SC" !important;
			font-weight: 400;
			font-size: 21rpx;
			color: #595959;
		}
	}

	.swiper-box {
		height: 346rpx;

		.swiper-item {
			display: flex;
			align-items: center;
			box-sizing: border-box;
		}

		.model-item {
			position: relative;
			width: 633rpx;
			height: 346rpx;
			padding: 27rpx 20rpx 27rpx 31rpx;
			background: #ccc;
			border-radius: 31rpx;
			display: flex;
			background-size: 100% 100%;
			// background: url(@/static/assets/home/<USER>
			background-color: #FF7739;
			// background-color: #111;
			box-sizing: border-box;
			justify-content: space-between;
			position: relative;
			transition: transform 0.2s ease-in-out;
			cursor: pointer;

			&::before {
				content: "";
				background: url(@/static/assets/home/<USER>
				background-size: 100% 100%;
				width: 100%;
				height: 100%;
				position: absolute;
				top: 0;
				left: 0;
				z-index: 10;
				pointer-events: none
			}

			&::after {
				content: "";
				background: url(@/static/assets/home/<USER>
				background-size: auto 100%;
				width: 182rpx;
				height: 206rpx;
				position: absolute;
				bottom: 0;
				right: 0;
				z-index: 21;
				pointer-events: none
			}

			.left-intro {
				width: 238rpx;

				.go_btn {
					position: absolute;
					left: 31rpx;
					bottom: 85rpx;
					width: 148rpx;
					height: 54rpx;
					background: #FEFEFE;
					border-radius: 31rpx;
					font-weight: 500;
					font-size: 23rpx;
					color: #262626;
					z-index: 100;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}

			.bike-img-box {
				position: absolute;
				right: 16rpx;
				top: 28rpx;
			}
		}
	}
}
</style>