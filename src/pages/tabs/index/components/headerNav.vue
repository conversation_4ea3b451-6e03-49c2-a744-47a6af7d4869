<template>
	<view class="home_header">
<!-- 		<u-navbar title="Shop" :bgColor="bgColor" :titleStyle="titleStyle" @leftClick="handleLeftClick" /> -->

		<view class="search-box" @click.native.prevent="$tab.navigateTo('/pages/Search/Search')">
			<image class="search_icon" src="@/static/assets/home/<USER>" />
			<text class="search-text">Search for bicycles</text>
			<image class="search_sort_icon" src="@/static/assets/home/<USER>" />
 
		</view>
	</view>
</template>

<script>
	import {
		mapGetters
	} from 'vuex';

	export default {
		props: {
			scrollTop: {
				type: Number,
				default: 0
			},
			threshold: {
				type: Number,
				default: 150
			}
		},
		data() {
			return {
				titleStyle: {
					fontWeight: 600,
					fontSize: '35rpx',
					color: "#000"
				}
			}
		},
		computed: {
			...mapGetters(['platForm']),
			bgColor() {
				if (this.scrollTop <= this.threshold) {
					const opacity = this.scrollTop / this.threshold;
					return `rgba(255, 255, 255, ${opacity})`;
				} else {
					return '#F0F0F0';
				}
			}
		},
		methods: {
			handleLeftClick() {
				if (this.platForm === 'android') {
					window.zlbridge.goPreviousPage();
				} else if (this.platForm === 'ios') {
					window.webkit.messageHandlers.exitStore.postMessage(null)
				} else {
					uni.navigateBack();
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.home_header {
		position: relative;

		.status-bar {
			position: fixed;
			top: 0;
			width: 100%;
			z-index: 999;
		}

		.search-box {
			position: absolute;
			bottom: -54rpx;
			left: 50%;
			right: 50%;
			transform: translateX(-50%);
			width: calc(100% - 62rpx);
			height: 108rpx;
			background: #FFFFFF;
			border-radius: 31rpx;
			box-sizing: border-box;
			box-shadow: 0 3px 5px #c6c6c6;
			padding: 0 31rpx 0 37rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.search-input{
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				opacity: 0;
				z-index: 100;
				background: red;
			}
			.search_icon,
			.search_sort_icon {
				width: 46rpx;
				height: 46rpx
			}

			.search-text {
				font-weight: 300;
				font-size: 27rpx;
				color: #595959 ;
				font-family:'PingFang SC-Regular'!important;;
				flex: 1;
				margin-left: 23rpx;
			}
		}
	}
</style>