<template>
	<!-- 
	swiper中的transfrom会使fixed失效,此时用height固定高度; 
	swiper中无法触发mescroll-mixins.js的onPageScroll和onReachBottom方法,只能用mescroll-uni,不能用mescroll-body
	-->
	<!-- top的高度等于悬浮菜单tabs的高度 -->
	<mescroll-uni i18n="en" @init="mescrollInit" :bottombar="false" :height="height" :disable-scroll="disableScroll"
		:down="downOption" :up="upOption" @up="upCallback" @emptyclick="emptyClick">
		<water-fall :dataList="goodsList" style="padding-bottom: 20px;">
			<template v-slot:itemLeft="slotProps">
				<productCover :product="slotProps.item" imgName="AppPicPath"
					@click.native="handleToGoodsDetail(slotProps.item)" @addFavoriteFn="onAddFavoriteFn"
					@onAddCart="handleAddToCart"></productCover>
			</template>

			<template v-slot:itemRight="slotProps">
				<productCover :product="slotProps.item" imgName="AppPicPath"
					@click.native="handleToGoodsDetail(slotProps.item)" @addFavoriteFn="onAddFavoriteFn"
					@onAddCart="handleAddToCart"></productCover>
			</template>
		</water-fall>
	</mescroll-uni>
</template>

<script>
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import MescrollMoreItemMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mixins/mescroll-more-item.js";

export default {
	mixins: [MescrollMixin, MescrollMoreItemMixin], // 注意此处还需使用MescrollMoreItemMixin (必须写在MescrollMixin后面)
	data() {
		return {
			downOption: {
				use: false // 禁用
			},
			upOption: {
				use: false,
				auto: false, // 不自动加载
				noMoreSize: 4, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
				empty: {
					tip: '~ 空空如也 ~', // 提示
					btnText: '去看看'
				}
			},
			goods: [] //列表数据
		}
	},
	props: {
		i: Number, // 每个tab页的专属下标 (除了支付宝小程序必须在这里定义, 其他平台都可不用写, 因为已在MescrollMoreItemMixin定义)
		index: { // 当前tab的下标 (除了支付宝小程序必须在这里定义, 其他平台都可不用写, 因为已在MescrollMoreItemMixin定义)
			type: Number,
			default() {
				return 0
			}
		},
		tabs: { // 为了请求数据,演示用,可根据自己的项目判断是否要传
			type: Array,
			default() {
				return []
			}
		},
		goodsList: {
			type: Array,
			default() {
				return []
			}
		},
		height: [Number, String], // mescroll的高度
		disableScroll: Boolean // 是否禁止滚动, 默认false
	},
	methods: {
		/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */
		upCallback(page) {
			//联网加载数据
			let keyword = this.tabs[this.i].name
			apiGoods(page.num, page.size, keyword).then(res => {
				//联网成功的回调,隐藏下拉刷新和上拉加载的状态;
				this.mescroll.endSuccess(res.list.length);
				//设置列表数据
				if (page.num == 1) this.goods = []; //如果是第一页需手动制空列表
				this.goods = this.goods.concat(res.list); //追加新数据
			}).catch(() => {
				//联网失败, 结束加载
				this.mescroll.endErr();
			})
		},
		//点击空布局按钮的回调
		emptyClick() {
			uni.showToast({
				title: '点击了按钮,具体逻辑自行实现'
			})
		},
		handleAddToCart(e) {
			this.$emit('handleAddToCart', e)
		},
		onAddFavoriteFn(e) {
			this.$emit('handleAddFavorite', e)
		},
		handleToGoodsDetail(item) {
			this.$tab.navigateTo(`/pages/goodsDetail/goodsDetail?ProId=${item.ProId}`)
		}
	}
}
</script>