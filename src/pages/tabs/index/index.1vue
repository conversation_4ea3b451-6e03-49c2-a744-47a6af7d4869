<template>
  <view class="container">
    <!-- 头部内容 -->
    <view class="header" :style="{height: headerHeight + 'px'}">
      <view class="header-content">这里是头部内容</view>
    </view>
    
    <!-- 吸顶Tab -->
    <view class="sticky-tab" :class="{fixed: isTabFixed}" 
          :style="{top: headerHeight + 'px', width: windowWidth + 'px'}">
      <scroll-view class="tab-scroll" scroll-x :scroll-left="scrollLeft" scroll-with-animation>
        <view class="tab-item" v-for="(item, index) in tabs" :key="index" 
              :class="{active: currentTab === index}" @click="switchTab(index)">
          {{item}}
        </view>
      </scroll-view>
    </view>
    
    <!-- 占位元素，用于固定Tab时保持布局不变 -->
    <view class="tab-placeholder" v-if="isTabFixed" 
          :style="{height: tabHeight + 'px'}"></view>
    
    <!-- 内容区域 -->
    <swiper class="swiper" :current="currentTab" @change="swiperChange" 
            :style="{height: swiperHeight + 'px'}">
      <swiper-item v-for="(item, index) in tabs" :key="index">
        <scroll-view class="swiper-scroll" scroll-y 
                     :scroll-top="scrollTops[index]" @scroll="scroll">
          <view class="content">
            <view class="item" v-for="i in 30" :key="i">
              内容{{index + 1}} - 项目{{i}}
            </view>
          </view>
        </scroll-view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
export default {
  data() {
    return {
      tabs: ['Tab1', 'Tab2', 'Tab3', 'Tab4'],
      currentTab: 0,
      isTabFixed: false,
      scrollTops: [0, 0, 0, 0], // 记录每个tab的滚动位置
      tabOffsetTop: 0, // tab距离顶部的距离
      tabHeight: 44, // tab高度
      headerHeight: 150, // 头部高度
      windowWidth: 375, // 窗口宽度
      swiperHeight: 0, // swiper高度
      scrollLeft: 0 // tab滚动位置
    }
  },
  onLoad() {
    // 获取系统信息
    uni.getSystemInfo({
      success: (res) => {
        this.windowWidth = res.windowWidth
        // 计算swiper高度 = 窗口高度 - 头部高度 - tab高度
        this.swiperHeight = res.windowHeight - this.headerHeight - this.tabHeight
      }
    })
  },
  onReady() {
    // 获取tab距离顶部的距离
    const query = uni.createSelectorQuery().in(this)
    query.select('.sticky-tab').boundingClientRect(data => {
      this.tabOffsetTop = data.top
    }).exec()
  },
  methods: {
    // 切换tab
    switchTab(index) {
      if (this.currentTab === index) return
      
      // 切换到新tab时重置滚动位置为0
      this.scrollTops[this.currentTab] = 0
      this.currentTab = index
      
      // 计算tab滚动位置，使当前tab居中
      this.calculateScrollLeft(index)
    },
    
    // swiper切换
    swiperChange(e) {
      const index = e.detail.current
      this.currentTab = index
      
      // 计算tab滚动位置，使当前tab居中
      this.calculateScrollLeft(index)
    },
    
    // 计算tab滚动位置
    calculateScrollLeft(index) {
      // 每个tab的宽度
      const tabWidth = this.windowWidth / 4
      // 计算需要滚动的距离
      this.scrollLeft = index * tabWidth - (this.windowWidth / 2 - tabWidth / 2)
      
      // 边界检查
      if (this.scrollLeft < 0) this.scrollLeft = 0
      const maxScroll = (this.tabs.length - 1) * tabWidth
      if (this.scrollLeft > maxScroll) this.scrollLeft = maxScroll
    },
    
    // 滚动事件
    scroll(e) {
      const scrollTop = e.detail.scrollTop
      // 记录当前tab的滚动位置
      this.scrollTops[this.currentTab] = scrollTop
      
      // 判断是否需要固定tab
      this.isTabFixed = scrollTop >= this.tabOffsetTop
    }
  }
}
</script>

<style>
.container {
  position: relative;
}

.header {
  background-color: #4a90e2;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sticky-tab {
  height: 44px;
  background-color: #f8f8f8;
  z-index: 10;
}

.sticky-tab.fixed {
  position: fixed;
  left: 0;
}

.tab-scroll {
  white-space: nowrap;
  height: 100%;
}

.tab-item {
  display: inline-block;
  width: 25%;
  height: 44px;
  line-height: 44px;
  text-align: center;
  font-size: 14px;
}

.tab-item.active {
  color: #4a90e2;
  font-weight: bold;
  border-bottom: 2px solid #4a90e2;
}

.swiper {
  width: 100%;
}

.swiper-scroll {
  height: 100%;
}

.content {
  padding: 10px;
}

.item {
  padding: 15px 0;
  border-bottom: 1px solid #eee;
}
</style>