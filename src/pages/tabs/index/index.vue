<!-- 吸顶轮播菜单导航 -->
<template>
	<AppInitWrapper @show-complete="onShowComplete" @init-complete="onInitComplete" @init-timeout="onInitTimeout">
		<view class="home-container">
			<!-- 国家切换 -->
			<navbar isImmersion :scrollTop="scrollTop" @leftClick="leftClick" :isRightShow="true">
				<template #right>
					<Location  @changeCountry="handleChangeCountry"/>
				</template>
			</navbar>
			<u-loading-page :loading="isPageLoading" bg-color="#F0F0F0" iconSize="30"
				loadingText="Loading"></u-loading-page>
			<transition name="fade">
				<!-- v-if='isPageLoading' -->
				<view class="home-container">
					<pageSkeleton v-if='isPageLoading' />
				</view>
			</transition>
			<transition name="fade">
				<view v-if="!isPageLoading">
					<view class="home-data">
						<!-- 顶部内容 -->
						<goods-swiper :swiperList="swiperList" keyName="banner_pic" height="685rpx"
							circular @click="handleToRouter"></goods-swiper>
						<!-- 头部 -->
						<HeaderNav :scrollTop="scrollTop" />
						<!-- 车型列表 -->
						<ModleList :modleList="modleList" />
						<!-- New Arrivals  -->
						<NewArrivals :newArrivalList="newArrivalList" />
					</view>

					<view class="sticky-tabs" :style="{ top: isStatusBarAndNavHeight }">
						<u-tabs :current="tabIndex" class="u-tabs-box" :list="tabs" :lineWidth="0" :lineHeight="0"
							lineColor="" :activeStyle="activeStyle" :inactiveStyle="inactiveStyle"
							:itemStyle="itemStyle" @change="tabChange"></u-tabs>
					</view>
					<!-- 数据列表 -->
					<water-fall :dataList="list" style="padding-bottom: 116px;">
						<template v-slot:itemLeft="slotProps">
							<productCover :product="slotProps.item" :scrollTop="scrollTop" imgName="AppPicPath"
								@click.native="handleToGoodsDetail(slotProps.item)" @addFavoriteFn="onAddFavoriteFn"
								@onAddCart="onAddCart"></productCover>
						</template>

						<template v-slot:itemRight="slotProps">
							<productCover :product="slotProps.item" :scrollTop="scrollTop" imgName="AppPicPath"
								@click.native="handleToGoodsDetail(slotProps.item)" @addFavoriteFn="onAddFavoriteFn"
								@onAddCart="onAddCart"></productCover>
						</template>
					</water-fall>
					<addPopup ref="addPopupRef"></addPopup>
				</view>
			</transition>
		</view>
	</AppInitWrapper>
</template>

<script>
import {
	mapGetters,
	mapActions
} from "vuex";
import HeaderNav from './components/headerNav';
import ModleList from './components/modleList';
import NewArrivals from './components/newArrivals';
import GoodsSwiper from './components/goodsSwiper';
import mescrollItem from './components/mescroll-item.vue';
import Location from './components/Location.vue';
import pageSkeleton from '@/uni_modules/skeleton-ui/pages/template/home-page-familiar.vue';
import {
	getHome as getHomeApi,
	productDetail,
	productFavorite
} from '@/api/home.js';
import AppInitWrapper from "@/components/AppInitWrapper/AppInitWrapper.vue";

export default {
	components: {
		HeaderNav,
		ModleList,
		NewArrivals,
		GoodsSwiper,
		mescrollItem,
		Location,
		pageSkeleton,
		AppInitWrapper
	},
	data() {
		return {
			isPageLoading: true,
			swiperList: [],
			modleList: [],
			newArrivalList: [],
			goodsList: [],
			scrollTop: 0,
			lastScrollTop: 0,
			isShowButtons: false,
			isDisableScroll: false,
			upOption: {
				use: false
			},
			downOption: {
				native: true
			},
			topHeight: 0,
			swiperHeight: "",
			tabs: [],
			tabIndex: 0,
			disableScroll: true,
			itemStyle: {
				padding: 0
			},
			activeStyle: {
				color: '#fff',
				backgroundColor: '#FF5A1E',
				borderRadius: '31rpx',
				height: '62rpx',
				lineHeight: '62rpx',
				paddingLeft: '46rpx',
				paddingRight: '46rpx',
				fontSize: '27rpx',
				fontWeight: 'bold',
				transition: 'background .3s ease'
			},
			inactiveStyle: {
				color: '#595959',
				height: '62rpx',
				lineHeight: '62rpx',
				paddingLeft: '46rpx',
				paddingRight: '46rpx',
				fontSize: '27rpx',
				fontWeight: 'bold',
			},
			threshold: 150,
			titleStyle: {
				fontWeight: 600,
				fontSize: '35rpx',
				color: "#000"
			},
			scrollRestoreTimer: null
		}
	},
	computed: {
		...mapGetters(['platForm', 'statusBarAndNavHeight', 'cartNum']),
		isStatusBarAndNavHeight() {
			return `${this.statusBarAndNavHeight}px`;
		},
		list() {
			const currentTab = this.goodsList?.[this.tabIndex];
			if (!currentTab) return [];
			// 原始商品数据（深拷贝避免污染原数据）
			const productData = JSON.parse(JSON.stringify(currentTab.product_data || []));
			const adImages = currentTab.product_pic_data || [];

			// 广告数据处理（标记并添加位置信息）
			let processedAds = adImages.map((ad, index) => ({
				...ad,
				isAd: true,
				AppPicPath: ad.product_pic,
				product_url: ad.product_url || ''
			}));
			if (processedAds.length === 0) {
				return productData;
			}
			let currentPos = 1; // 第一个广告位置
			for (let i = 0; i < processedAds.length; i++) {
				productData.splice(currentPos, 0, processedAds[i]);
				if (i % 2 == 0) {
					currentPos += 5;
				} else {
					currentPos += 7;
				}
			}
			return productData;
		}
	},
	watch: {
		tabIndex(i) {
			// 当列表禁止滚动时,需把列表滚动条置顶 (解决问题: "全部"tab翻到第二页,切换到其他tab,滚动到顶部,再切回"全部"tab,此时的列表数据应该重头开始)
			if (this.disableScroll) {
				this.disableScroll = false // 当disableScroll=true时,scroll-view的scrollTo会失效,需先开启,再置顶
				this.$nextTick(() => {
					let mescroll = this.getMescroll(i)
					mescroll && mescroll.scrollTo(0, 0)
					setTimeout(() => { // 经测试android真机需延时300ms才能恢复禁止滚动,否则scrollTo有可能无效
						this.disableScroll = true
					}, 300)
				})
			}
		}
	},
	onHide() {
		// 保存当前滚动位置，用于页面恢复时使用
		this.lastScrollTop = this.scrollTop;
		console.log('首页隐藏，保存滚动位置:', this.lastScrollTop);
		
		// 在离开页面时，清除可能的滚动恢复定时器
		if (this.scrollRestoreTimer) {
			clearTimeout(this.scrollRestoreTimer);
			this.scrollRestoreTimer = null;
		}
	},
	onLoad() {
	},
	onPullDownRefresh() {
		this.getHome();
	},
	onShow() {
		// 首页显示时，确保滚动状态同步
		this.$nextTick(() => {
			// 如果从其他页面返回且有保存的滚动位置，恢复它
			if (this.lastScrollTop > 0) {
				// 立即设置 scrollTop 以确保导航条透明度正确
				this.scrollTop = this.lastScrollTop;
				
				this.scrollRestoreTimer = setTimeout(() => {
					uni.pageScrollTo({
						scrollTop: this.lastScrollTop,
						duration: 100,
						success: () => {
							// 确保滚动位置和状态完全同步
							console.log('首页滚动位置已恢复到:', this.lastScrollTop);
							this.scrollRestoreTimer = null;
						}
					});
				}, 150); // 增加延时，确保页面完全渲染后再滚动
			} else {
				// 如果没有保存的位置，确保从顶部开始
				this.scrollTop = 0;
			}
		});
	},
	mounted() {

		this.modifyStatusBarStyle();

		// console.log("uni.getSystemInfoSync().windowHeight", uni.getSystemInfoSync().windowHeight);
		// this.swiperHeight = uni.getSystemInfoSync().windowHeight - uni.upx2px(106) - 44 + 'px';
		// console.log('this.swiperHeight', this.swiperHeight);

		// #ifdef H5
		uni.pageScrollTo({
			scrollTop: 0,
			duration: 0
		}) // 刷新浏览器,重置滚动条
		// #endif
	},
	onPageScroll(e) {
		// 边界检查，确保scrollTop有效
		if (!e || typeof e.scrollTop !== 'number' || e.scrollTop < 0) {
			return;
		}

		const systemInfo = uni.getSystemInfoSync();
		if (systemInfo.statusBarHeight) {
			this.$store.commit('SET_STATUSBARHEIGHT', systemInfo.statusBarHeight);
		}

		this.scrollTop = e.scrollTop;
		this.isDisableScroll = Math.ceil(e.scrollTop) > (this.topHeight - uni.upx2px(106) - 40);
	},
	methods: {
		...mapActions(['getCartNum']),
		onShowComplete() {
			console.log("🚀 ~ file: index.vue:231 ~ onShowComplete ~ 页面显示完成")
			this.getCartNum().then(() => {
				this.$store.commit('SET_CART_TABBAR_COUNT');
			})
			this.getHome();
			
			// 不在这里直接调用getHome，等待初始化完成后再调用
			const systemInfo = uni.getSystemInfoSync();
			console.log('systemInfo.statusBarHeight', systemInfo.statusBarHeight)
		},
		leftClick() {
			if (this.platForm === 'android') {
				window.zlbridge.exitStore();
			} else if (this.platForm === 'ios') {
				window.webkit.messageHandlers.exitStore.postMessage(null)
			} else {
				uni.navigateBack();
			}
		},
		modifyStatusBarStyle() {
			// 获取所有 u-status-bar 元素
			const statusBars = document.getElementsByClassName('u-status-bar');
			if (statusBars.length > 0) {
				// 遍历所有 u-status-bar 元素并修改样式
				for (let i = 0; i < statusBars.length; i++) {
					const statusBar = statusBars[i];
					// 修改背景颜色
					statusBar.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
					// 修改高度（例如动态设置为 30px）
					statusBar.style.height = '30px !important';
					// 其他样式修改
					statusBar.style.opacity = '0.8';
				}
			}
		},
		// 轮播菜单
		swiperChange(e) {
			this.tabIndex = e.detail.current
		},
		handleChangeCountry(country) {
			this.tabIndex = 0; // 或者根据需要重置/更新
		},
		tabChange(e) {
			this.tabIndex = e.index;
		},
		// 获取指定下标的mescroll对象
		getMescroll(i) {
			let mescrollItems = this.$refs.mescrollItem;
			if (mescrollItems) {
				let item = mescrollItems[i]
				if (item) return item.mescroll
			}
			return null
		},
		handleScroll() {
			const backButton = document.querySelector('.uni-page-head-hd');
			if (this.scrollTop > 0) {
				backButton.classList.add('show')
			} else {
				backButton.classList.remove('show')
			}
		},
		getHome() {
			getHomeApi().then(res => {
				this.isPageLoading = false;
				this.swiperList = res?.[0]?.Data;
				this.modleList = res?.[1]?.Data;
				this.newArrivalList = res?.[2]?.Data;
				this.goodsList = res?.[3]?.Data;
				this.tabs = res[3]?.Data?.map(item => {
					return {
						name: item.product_title
					}
				})

				this.$nextTick(() => {
					const homeData = uni.createSelectorQuery().in(this).select('.home-data');
					console.log('homeData', homeData)
					homeData.boundingClientRect(data => {
						console.log('data', data);
						this.topHeight = data?.height;
					}).exec();
				})
			}).finally(() => {
				uni.stopPullDownRefresh();
			})
		},
		onAddFavoriteFn({
			ProId
		}) {
			productFavorite({
				ProId
			}).then(res => {
				if (res.Status === 1) {
					this.$toast({
						title: 'Successful collection',
						image: "/static/assets/common/add_successfully.png"
					})
				}

				this.getHome();
			}).catch(() => {
				this.$toast({
					title: 'Collection failure',
					icon: 'error'
				})
			})
		},
		// 加入购物车
		onAddCart(params) {
			uni.showLoading()
			productDetail({
				ProId: params.ProId
			}).then(res => {
				this.$refs.addPopupRef.isAddCartShow = true;
				this.$refs.addPopupRef.productImg = params.AppPicPath;
				this.$refs.addPopupRef.productDetailData = res;

				// this.$store.commit('SET_CARTNUM', +this.cartNum + 1);
				// this.$store.commit('SET_CART_TABBAR_COUNT');
			}).finally(() => {
				uni.hideLoading()
			})
		},
		handleToGoodsDetail(item) {
			if (item.isAd) {
				this.$store.commit('user/TO_APP_PAGE', item.product_url)
				return
			}
			this.$tab.navigateTo(`/pages/goodsDetail/goodsDetail?ProId=${item.ProId}`)
		},

		// 初始化完成回调
		onInitComplete() {
			console.log('页面初始化完成，开始加载数据');
			this.getHome();
		},

		// 初始化超时回调
		onInitTimeout() {
			console.warn('页面初始化超时，继续加载数据');
			this.getHome();
		},
		handleToRouter(index) {
			console.log('index', index);
			if (this.swiperList[index] && this.swiperList[index].banner_url) {
				this.$tab.navigateTo(this.swiperList[index].banner_url)
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.u-tabs-box {
	margin-left: 29rpx;
	padding: 23rpx 0;
}

.sticky-tabs {
	position: sticky;
	z-index: 777;
	background-color: #F0F0F0;

	::v-deep .u-tabs {
		padding: 30rpx 0;
	}
}

.uni-page-head-hd {
	display: none !important;

	&.show {
		display: block;
	}
}

.fade-enter-active,
.fade-leave-active {
	transition: opacity 0.5s ease;
}

.fade-enter,
.fade-leave-to {
	opacity: 0;
}
</style>