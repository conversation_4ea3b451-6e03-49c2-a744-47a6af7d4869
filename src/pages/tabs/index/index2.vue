<!-- 吸顶轮播菜单导航 -->
<template>
	<view class="home-container">
		<u-loading-page :loading="isPageLoading" bg-color="#F0F0F0" iconSize="30"
			loadingText="Loading"></u-loading-page>

		<template v-if="!isPageLoading">
			<mescroll-body :sticky="true" :bottombar="false" @init="mescrollInit" @down="downCallback" :up="upOption"
				:down="downOption">
				<view class="home-data">
					<!-- 顶部内容 -->
					<goods-swiper :swiperList="swiperList" keyName="banner_pic" height="685rpx" circular></goods-swiper>
					<!-- 头部 -->
					<HeaderNav :scrollTop="scrollTop" />
					<!-- 车型列表 -->
					<ModleList :modleList="modleList" />
					<!-- New Arrivals  -->
					<NewArrivals :newArrivalList="newArrivalList" />
				</view>

				<view class="sticky-tabs" :style="{ top: isStatusBarAndNavHeight }">
					<u-tabs :current="tabIndex" class="u-tabs-box" :list="tabs" :lineWidth="0" :lineHeight="0"
						lineColor="" :activeStyle="activeStyle" :inactiveStyle="inactiveStyle" :itemStyle="itemStyle"
						@change="tabChange"></u-tabs>
				</view>

				<!-- 数据列表 -->
				<swiper :style="{height: swiperHeight}" :current="tabIndex" @change="swiperChange">
					<swiper-item v-for="(tab,i) in tabs" :key="i">
						<mescroll-item ref="mescrollItem" :i="i" :index="tabIndex" :tabs="tabs" :height="swiperHeight"
							:disable-scroll="disableScroll" :goodsList="list"
							@handleAddToCart="handleAddToCart" @handleAddFavorite="handleAddFavorite"></mescroll-item>
					</swiper-item>
				</swiper>
			</mescroll-body>

			<addPopup ref="addPopupRef"></addPopup>
		</template>
	</view>
</template>

<script>
	import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
	import {
		mapGetters
	} from "vuex";
	import HeaderNav from './components/headerNav';
	import ModleList from './components/modleList';
	import NewArrivals from './components/newArrivals';
	import GoodsSwiper from './components/goodsSwiper';
	import mescrollItem from './components/mescroll-item.vue';
	import {
		getHome,
		productDetail,
		productFavorite
	} from '@/api/home.js';

	export default {
		mixins: [MescrollMixin], // 使用mixin
		components: {
			HeaderNav,
			ModleList,
			NewArrivals,
			GoodsSwiper,
			mescrollItem
		},
		data() {
			return {
				isPageLoading: true,
				swiperList: [],
				modleList: [],
				newArrivalList: [],
				goodsList: [],
				scrollTop: 0,
				isShowButtons: false,
				upOption: {
					use: false
				},
				downOption: {
					native: true
				},
				topHeight: uni.upx2px(619),
				swiperHeight: "",
				tabs: [],
				tabIndex: 0,
				disableScroll: true,
				itemStyle: {
					padding: 0
				},
				activeStyle: {
					color: '#fff',
					backgroundColor: '#FF5A1E',
					borderRadius: '31rpx',
					height: '62rpx',
					lineHeight: '62rpx',
					paddingLeft: '46rpx',
					paddingRight: '46rpx',
					fontSize: '27rpx',
					fontWeight: 'bold',
					transition: 'background .3s ease'
				},
				inactiveStyle: {
					color: '#595959',
					height: '62rpx',
					lineHeight: '62rpx',
					paddingLeft: '46rpx',
					paddingRight: '46rpx',
					fontSize: '27rpx',
					fontWeight: 'bold',
				}
			}
		},
		computed: {
			...mapGetters(['platForm', 'statusBarAndNavHeight', 'cartNum']),
			isStatusBarAndNavHeight() {
				return `${this.statusBarAndNavHeight}px`;
			},
			list() {
				const list = this.goodsList[this.tabIndex]?.product_data;
				return list;
				
				// if (this.tabIndex == 0) {
				// 	return [...list, ...list, ...list, ...list, ...list]
				// }
				// return this.goodsList[this.tabIndex]?.product_data;
			}
		},
		watch: {
			tabIndex(i) {
				// 当列表禁止滚动时,需把列表滚动条置顶 (解决问题: "全部"tab翻到第二页,切换到其他tab,滚动到顶部,再切回"全部"tab,此时的列表数据应该重头开始)
				if (this.disableScroll) {
					this.disableScroll = false // 当disableScroll=true时,scroll-view的scrollTo会失效,需先开启,再置顶
					this.$nextTick(() => {
						let mescroll = this.getMescroll(i)
						mescroll && mescroll.scrollTo(0, 0)
						setTimeout(() => { // 经测试android真机需延时300ms才能恢复禁止滚动,否则scrollTo有可能无效
							this.disableScroll = true
						}, 300)
					})
				}
			},
			cartNum(value) {
				console.log('index', value)
				if (value) {
					this.$store.commit('SET_CART_TABBAR_COUNT');
				}
			}
		},
		onLoad() {
			this.getHome();
		},
		mounted() {
			this.swiperHeight = uni.getSystemInfoSync().windowHeight - uni.upx2px(106) - 44 + 'px';

			// #ifdef H5
			uni.pageScrollTo({
				scrollTop: 0,
				duration: 0
			}) // 刷新浏览器,重置滚动条
			// #endif
		},
		onNavigationBarButtonTap(e) {
			if (this.platForm === 'android') {
				window.zlbridge.exitStore();
			} else if (this.platForm === 'ios') {
				window.webkit?.messageHandlers.exitStore.postMessage(null)
			} else {
				uni.navigateBack();
			}
		},
		methods: {
			// downCallback() {
			// 	this.getHome();
			// },
			// 轮播菜单
			swiperChange(e) {
				this.tabIndex = e.detail.current
			},
			tabChange(e) {
				this.tabIndex = e.index;
			},
			// 获取指定下标的mescroll对象
			getMescroll(i) {
				let mescrollItems = this.$refs.mescrollItem;
				if (mescrollItems) {
					let item = mescrollItems[i]
					if (item) return item.mescroll
				}
				return null
			},
			// 页面的滚动事件
			onPageScroll(e) {
				this.scrollTop = e.scrollTop;
				console.log('this.scrollTop', this.scrollTop, this.topHeight, this.topHeight - uni.upx2px(106) - 40)
				// this.disableScroll = Math.ceil(e.scrollTop) < this.topHeight;
				this.disableScroll = Math.ceil(e.scrollTop) < (this.topHeight - uni.upx2px(106) - 40)
			},
			handleScroll() {
				const backButton = document.querySelector('.uni-page-head-hd');
				if (this.scrollTop > 0) {
					backButton.classList.add('show')
				} else {
					backButton.classList.remove('show')
				}
			},
			// 加入购物车
			handleAddToCart(params) {
				console.log('params', params)
				uni.showLoading()
				productDetail({
					ProId: params.ProId
				}).then(res => {
					this.$refs.addPopupRef.isAddCartShow = true;
					this.$refs.addPopupRef.productImg = params.AppPicPath;
					this.$refs.addPopupRef.productDetailData = res;
				}).finally(() => {
					uni.hideLoading()
				})
			},
			handleAddFavorite({
				ProId
			}) {
				productFavorite({
					ProId
				}).then(res => {
					console.log(res);
					if (res.Status === 1) {
						this.$toast({
							title: 'Successful collection',
							image: "/static/assets/common/add_successfully.png"
						})
					}
			
					this.getHome();
				}).catch(() => {
					this.$toast({
						title: 'Collection failure',
						icon: 'error'
					})
				})
			},
			getHome() {
				getHome().then(res => {
					this.isPageLoading = false;
					this.swiperList = res[0].Data;
					this.modleList = res[1].Data;
					this.newArrivalList = res[2].Data;
					this.goodsList = res[3]?.Data;
					this.tabs = res[3]?.Data.map(item => {
						return {
							name: item.product_title
						}
					})


					this.$nextTick(() => {
						const homeData = uni.createSelectorQuery().in(this).select('.home-data');
						console.log('homeData', homeData)
						homeData.boundingClientRect(data => {
							console.log('data', data);
							this.topHeight = data?.height;
						}).exec();
					})
				}).finally(() => {
					this.mescroll.endDownScroll();
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.u-tabs-box {
		margin-left: 29rpx;
		padding: 23rpx 0;
	}

	.sticky-tabs {
		position: sticky;
		z-index: 10;
		background-color: #F0F0F0;
	}

	.uni-page-head-hd {
		display: none !important;

		&.show {
			display: block;
		}
	}
</style>