<template>
	<mescroll-uni @init="mescrollInit" :height="height" :down="downOption" @down="downCallback"
		:up="upOption" @up="upCallback">
		<goodsList :list="list" @handleToOrderStatusPage="handleToOrderStatusPage"></goodsList>
	</mescroll-uni>
</template>

<script>
	import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
	import MescrollMoreItemMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mixins/mescroll-more-item.js";
	import {
		getMyOrderList
	} from "@/api/home.js";

	export default {
		mixins: [MescrollMixin, MescrollMoreItemMixin], // 注意此处还需使用MescrollMoreItemMixin (必须写在MescrollMixin后面)
		components: {
			goodsList: () => import('./goods-list.vue')
		},
		data() {
			return {
				downOption: {
					auto: false 
				},
				upOption: {
					auto:false
				},
				list: []
			}
		},
		props: {
			i: Number, 
			index: { 
				type: Number,
				default () {
					return 0
				}
			},
			tabs: { 
				type: Array,
				default () {
					return []
				}
			},
			height: [Number, String] // mescroll的高度
		},
		methods: {
			downCallback() {
				this.mescroll.resetUpScroll()
			},
			upCallback(page) {
				getMyOrderList({
					page: page.num,
					OrderStatus: this.tabs[this.index].OrderStatus
				}).then(res => {
					if (page.num == 1) this.list = [];
					this.list = this.list.concat(res.result);
					this.mescroll.endByPage(res.result.length, res.total_pages);
				}).catch(() => {
					this.mescroll.endErr();
				})
			},
			handleToOrderStatusPage(OrderId) {
				this.$tab.navigateTo(`/pages/tabs/mine/OrderStatus/OrderStatus?OrderId=${OrderId}`)
			}
		}
	}
</script>
<style lang="scss" scoped>

</style>