<template>
	<view class="myOrder-box">
		<navbar autoBack />
		
		<view class="subsection-header-container" v-if="isTabShow" :style="{top: statusBarAndNavHeight + 'px'}">
			<u-tabs class="u-tabs-box" :list="tabs" :current="tabIndex" :lineWidth="0" :lineHeight="0"
				:activeStyle="activeStyle" :inactiveStyle="inactiveStyle" @change="sectionChange"></u-tabs>
		</view>
		<view style="height: 130rpx;"></view>
		<swiper :duration='0' :style="{height: height}" :current="tabIndex" @change="swiperChange">
			<swiper-item v-for="(tab,i) in tabs" :key="i">
				<mescroll-item ref="mescrollItem" :i="i" :index="tabIndex" :tabs="tabs"
					:height="height" style="min-height: 80vh;"></mescroll-item>
			</swiper-item>
		</swiper>

		<!-- <swiper-item v-for="(tab,i) in tabs" :key="i">
				<mescroll-item ref="mescrollItem" :i="i" :index="tabIndex" :tabs="tabs"
					:height="height"></mescroll-item>
			</swiper-item> -->
	</view>
</template>

<script>
	import MescrollItem from './mescroll-swiper-item.vue';
	import {
		mapGetters
	} from 'vuex';

	export default {
		data() {
			return {
				isTabShow: false,
				tabIndex: 0,
				height: "",
				tabs: [{
						name: 'All',
						OrderStatus: '',
					},
					{
						name: 'Unpaid',
						OrderStatus: '1',
					},
					{
						name: 'On Hold',
						OrderStatus: '4',
					},
					{
						name: 'Delivery',
						OrderStatus: '5',
					},
					{
						name: 'Archived',
						OrderStatus: '6',
					}
				],
				activeStyle: {
					color: '#fff',
					backgroundColor: '#FF5A1E',
					borderRadius: '29rpx',
					width: '100%',
					height: '85rpx',
					lineHeight: '85rpx',
					fontSize: '28rpx',
					fontWeight: 'bold'
				},
				inactiveStyle: {
					color: '#8C8C8C',
					width: '100%',
					height: '85rpx',
					lineHeight: '85rpx',
					fontSize: '25rpx',
					fontWeight: 'bold',
				}
			};
		},
		components: {
			MescrollItem
		},
		computed: {
			...mapGetters(['platForm','statusBarHeight','statusBarAndNavHeight']),
		},
		onLoad(e) {
			if (e?.tabIndex) {
				this.tabIndex = +e?.tabIndex;
			} else {
				this.tabIndex = 0;
			}

			this.isTabShow = true;
			setTimeout(() => {
				this.tabIndex = this.tabIndex;
			}, 500)

			this.height = uni.getSystemInfoSync().windowHeight - this.statusBarAndNavHeight - uni.upx2px(130 ) + 'px';
		},
		methods: {
			sectionChange(item) {
				this.tabIndex = item.index;
			},
			swiperChange(e) {
				this.tabIndex = e.detail.current
			},
		}
	}
</script>

<style lang="scss" scoped>
	.myOrder-box {
		height: 100vh;

		.subsection-header-container {
			z-index: 200;
			position: fixed;
			top: --window-top;
			left: 31rpx;
			right: 31rpx;
			background-color: #fff;
			border-radius: 31rpx;
			overflow: hidden;
			z-index: 1000;
			&::before{
				content: '';
				height:1rpx;


			}
			.u-tabs-box {
				height: 92rpx;
				margin-left: 4rpx;
				margin-right: 4rpx;

				::v-deep .u-tabs__wrapper__nav {
					padding: 0;

					.u-tabs__wrapper__nav__item {
						flex: 1;
						padding: 0;
						text-align: center;
						
						&:first-child {
							flex: none;
							
							.u-tabs__wrapper__nav__item__text {
								width: 130rpx !important;
							}
						}
					}
				}

				::v-deep .u-tabs__wrapper {
					height: 100%;
				}
			}
		}
	}
</style>