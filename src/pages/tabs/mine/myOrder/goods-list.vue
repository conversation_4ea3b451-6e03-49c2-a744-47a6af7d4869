<template>
	<view class="order-list-box">
		<view class="order-item" v-for="(item, index) in list" :key="index">
			<view class="order-item-header" @click.stop="handleClick(item)">
				<u--text :text="`Order Number: ${item.OId}`" color="#262626" size="27rpx" bold :lines="1"></u--text>
				<view style="width: 137rpx;">
					<u--text :text="item.OrderStatusText" color="#FF5A1E" size="30rpx" bold align="right"
						:lines="1"></u--text>
				</view>
			</view>


			<expand-collapse :isTagShow="item.orders_products_list.length > 2"
				:showHeight="isShowMoreHeight(item.orders_products_list)">
				<group-goods-list :ordersProductsList="item.orders_products_list" @jump="handleClick(item)"
					:curItem="item"></group-goods-list>
			</expand-collapse>
		</view>
	</view>
</template>

<script>
import SkuMixins from '@/mixins/sku.js';
import {
	mapGetters
} from "vuex";

export default {
	name: 'GoodsList',
	mixins: [SkuMixins],
	props: {
		list: {
			type: Array,
			default: () => []
		}
	},
	computed: {
		...mapGetters(['priceSymbol']),
		isShowMoreHeight() {
			return (list) => {
				if (list.length >= 1) {
					return '130px'
				} else {
					return 'auto'
				}
			}
		},

	},
	methods: {
		handleClick(item) {
			console.log("🚀 ~ jump ~ item:", item)
			this.$emit('handleToOrderStatusPage', item.OrderId)
		}
	}
}
</script>

<style lang="scss" scoped>
.order-list-box {
	display: grid;
	@include flex-gap(31rpx, 0); // 替换了 row-gap
	margin: 0 31rpx;

	.order-item {
		padding: 23rpx 31rpx;
		background: #fff;
		border-radius: 31rpx;

		.order-item-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
	}
}
</style>