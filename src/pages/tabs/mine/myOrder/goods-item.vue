<template>
	<view class="goods-list-box">
		<view v-for="cItem in orderGoodsData" :key="cItem.ProId" class="goods-items">
			<view>
				<view class="main-center">
					<view class="goods-img-box">
						<u--image :src="cItem.PicPath" width="180rpx" height="104rpx" radius="18rpx"
							mode="aspectFit"></u--image>
						<view class="gift-title" v-if="cItem.IsGift == 1 && cItem.GiftText != ''">
							<u--text :text="cItem.GiftText" color="#fff" bold size="10px"></u--text>
						</view>
					</view>
					<view class="goods-info-box">
						<u--text :text="cItem.Name" lines="1" color="#262626" bold></u--text>

						<view class="main-info">
							<view class="main-info-left">
								<!-- 普通商品Sku -->
								<block v-if="Object.keys(cItem.Property)">
									<CollapsibleComponent>
										<template #content>
											<view v-for="(value, key) in cItem.Property" :key="key">
												<u--text :text="`${ key }: ${ value }`" color="#8C8C8C"
													size="27rpx"></u--text>
											</view>
										</template>
									</CollapsibleComponent>
								</block>
							</view>
							<view class="main-info-right">
								<u--text :text="`x${cItem.Qty}`" color="#8C8C8C" size="27rpx"></u--text>
							</view>
						</view>
					</view>
				</view>
				<!-- 价格 -->
				<u--text :text="cItem.Price" size="38rpx" bold color="#262626" align="right"></u--text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "GoodsItem",
		props: {
			orderGoodsData: {
				type: Array,
				default: () => []
			}
		}
	}
</script>

<style lang="scss" scoped>
	.goods-list-box {
		overflow: hidden;

		.goods-items {
			padding: 30rpx 0;

			.main-center {
				display: flex;

				.goods-img-box {
					position: relative;

					.gift-title {
						background-color: #FF5A1E;
						width: fit-content;
						padding: 2px;
						border-radius: 4px;
						position: absolute;
						right: 0;
						top: -8px;
					}
				}

				.goods-info-box {
					margin-left: 21rpx;
					flex: 1;

					.main-info {
						display: inline-flex;
						justify-content: space-between;
						width: 100%;
						margin-top: 19rpx;
					}
				}
			}
		}
	}
</style>