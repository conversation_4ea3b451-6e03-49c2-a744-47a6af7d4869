<template>
	<view class="logistics-detail-box">
		<navbar autoBack />
		
		<u-cell-group class="common-box-style" :border="false">
			<u-cell :border="false" center>
				<u--image slot="icon" width="42rpx" height="42rpx" radius="50%"
					src="/static/assets/mine/orderAddress.png"></u--image>
				<template slot="title">
					<view class="flow-box">
						<u--text text="ExpressType" lines="1" color="#262626" size="29rpx" bold
							style="flex: none; width: fit-content;"></u--text>
						<u-line direction="col" color="#D9D9D9" length="25rpx" margin="0 21rpx 0"></u-line>
						<u--text text="TrackingNumber" lines="1" color="#262626" size="29rpx" bold></u--text>
					</view>
				</template>
			</u-cell>

			<logistics :wlInfo="wlInfo"></logistics>
		</u-cell-group>
		<!-- 地址信息 -->
		<u-cell-group class="common-box-style" :border="false">
			<u-cell :border="false" center>
				<u-icon slot="icon" size="52rpx" name="/static/assets/mine/orderAddress.png" style="margin-right: 5px;"></u-icon>
				<view slot="title">
					<u--text text="Marry" size="29rpx" color="#262626" bold margin="0 0 4rpx 0"></u--text>
				</view>
				<view slot="label">
					<u--text text="Richmond Terrace,Staten Island, NY 10301 3" size="29rpx" color="#262626"
						bold></u--text>
				</view>
			</u-cell>
		</u-cell-group>
	</view>
</template>

<script>
	import { getOrderTrackDetail } from "@/api/orderDetails.js";

	export default {
		data() {
			return {
				OrderId: '',
				wlInfo: {
					delivery_status: 1, //快递状态 1已签收 2配送中
					list: [{
							"time": "2020-04-12 13:00:57",
							"timeArr": ['2020-04-12', '13:00:57'],
							"delivery_statusTxt": "Signed",
							"context": "Signed by HarkaDeng",
							"location": ""
						},
						{
							"time": "2020-04-12 12:58:53",
							"timeArr": ['2020-04-12', '12:58:53'],
							"delivery_statusTxt": "Delivering",
							"context": "Signed by HarkaDeng",
							"location": ""
						},
						{
							"time": "2020-04-11 15:45:44",
							"timeArr": ['2020-04-11', '15:45:44'],
							"delivery_statusTxt": "Arrival Scan",
							"context": "Signed by HarkaDeng",
							"location": ""
						},
						{
							"time": "2020-04-11 15:45:44",
							"timeArr": ['2020-04-11', '15:45:44'],
							"delivery_statusTxt": "Origin Scan",
							"context": "Signed by HarkaDeng",
							"location": ""
						},
						{
							"time": "2020-04-11 15:45:44",
							"timeArr": ['2020-04-11', '15:45:44'],
							"delivery_statusTxt": "Order Processed",
							"context": "Signed by HarkaDeng",
							"location": ""
						}
					]
				}
			};
		},
		onLoad(e) {
			this.OrderId = e.OrderId;
			this.getOrderTrackDetail();
		},
		onPullDownRefresh() {
			this.getOrderTrackDetail();
		},
		methods: {
			getOrderTrackDetail() {
				getOrderTrackDetail({
					OrderId: this.OrderId
				}).then(res => {
					console.log(res);
				}).finally(() => {
					uni.stopPullDownRefresh();
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.logistics-detail-box {
		padding: 23rpx 31rpx;
		display: grid;
  @include flex-gap(31rpx, 0); // 替换了 row-gap

		.flow-box {
			display: flex;
			align-items: center;
			font-weight: bold;
			font-size: 29rpx;
			color: #262626;
			margin-left: 30rpx;
		}
	}
</style>