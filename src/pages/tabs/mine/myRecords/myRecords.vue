<template>
	<view>
		<navbar autoBack />
		<mescroll-body :up="upOption" @init="mescrollInit" @down="downCallback" @up="upCallback">
			<view class="myRecords-box">
				<water-fall :dataList="list">
					<template v-slot:itemLeft="slotProps">
						<productCover :product="slotProps.item" :scrollTop="scrollTop"
							@click.native="handleToGoodsDetail(slotProps.item)" @addFavoriteFn="onAddFavoriteFn"
							@onAddCart="onAddCart"></productCover>
					</template>

					<template v-slot:itemRight="slotProps">
						<productCover :product="slotProps.item" :scrollTop="scrollTop"
							@click.native="handleToGoodsDetail(slotProps.item)" @addFavoriteFn="onAddFavoriteFn"
							@onAddCart="onAddCart"></productCover>
					</template>
				</water-fall>
			</view>

			<addPopup ref="addPopupRef"></addPopup>
		</mescroll-body>
	</view>
</template>

<script>
	import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
	import {
		getCartGetHistory
	} from '@/api/products.js';
	import {
		productDetail,
		productFavorite
	} from "@/api/home.js";

	export default {
		mixins: [MescrollMixin],
		data() {
			return {
				scrollTop: 0,
				list: [],
				upOption: {
					toTop: {
						duration: 0
					}
				}
			};
		},
		onPageScroll({
			scrollTop
		}) {
			this.scrollTop = scrollTop;
		},
		methods: {
			upCallback(page) {
				getCartGetHistory({
					page: page.num
				}).then(res => {
					if (page.num === 1) {
						this.list = []
					}
					this.list = this.list.concat(res.result);
					this.mescroll.endByPage(res.result.length, res.total_pages);
				}).catch(err => {
					this.mescroll.endErr();
				})
			},
			onAddFavoriteFn({
				ProId
			}) {
				productFavorite({
					ProId
				}).then(res => {
					if (res.Status === 1) {
						this.$toast({
							title: 'Successful collection',
							image: "/static/assets/common/add_successfully.png"
						})
					}

					this.mescroll.resetUpScroll();
				}).catch(() => {
					this.$toast({
						title: 'Collection failure',
						icon: 'error'
					})
				})
			},
			// 加入购物车
			onAddCart(params) {
				uni.showLoading()
				productDetail({
					ProId: params.ProId
				}).then(res => {
					this.$refs.addPopupRef.isAddCartShow = true;
					this.$refs.addPopupRef.productImg = params.PicPath_0;
					this.$refs.addPopupRef.productDetailData = res;
				}).finally(() => {
					uni.hideLoading()
				})
			},
			handleToGoodsDetail(item) {
				this.$tab.navigateTo(`/pages/goodsDetail/goodsDetail?ProId=${item.ProId}`)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.myRecords-box {
		padding: 23rpx 0;
	}
</style>