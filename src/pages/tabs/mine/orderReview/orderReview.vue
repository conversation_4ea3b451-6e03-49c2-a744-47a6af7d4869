<template>
	<view class="order-review-page">
		<navbar autoBack />

		<view class="order-review-box">
			<view class="review-box">
				<view class="like-button-box">
					<view class="like-item" v-for="(item, index) in 5" :key="index" @click="handleSetLike(index + 1)">
						<img :src="index < likeCount ? startActive : startInactive" class="star-img"
							:alt="index < likeCount ? 'active star' : 'inactive star'" />
					</view>
				</view>
				<u--textarea class="review-txt" v-model="reviewTxt" placeholder="Write..." border="none" count
					:maxlength="-1" :count="false" height="250rpx" :adjustPosition="false"></u--textarea>
				<view>
					<uni-file-picker v-model="fileList" fileMediatype="image" mode="grid" :auto-upload="false"
						:image-styles="imageStyles" @select="handleSelect">
						<u-icon name="plus" color="#D7D7D7" size="46rpx"></u-icon>
					</uni-file-picker>
				</view>
			</view>
			<u-button class="post-btn" text="Post" shape="circle" @click="handlePost"></u-button>
		</view>
	</view>
</template>

<script>
import {
	addComment
} from "@/api/orderDetails.js";
import {
	getToken
} from '@/utils/security/auth'
import startActive from '../../../../static/assets/common/star-active.png'
import startInactive from '../../../../static/assets/common/star-no.png'
export default {
	data() {
		return {
			startActive,
			startInactive,
			ProId: "",
			likeCount: 0,
			reviewTxt: "",
			fileList: [],
			imageStyles: {
				"border": {
					"color": "#D7D7D7", // 边框颜色
					"width": "1px", // 边框宽度
					"style": "dashed", // 边框样式
					"radius": "7px" // 边框圆角，不支持百分比
				}
			},

		};
	},
	onLoad(e) {
		this.ProId = e.ProId;
	},
	methods: {
		handleDeletePic(event) {
			console.log(event)
			this[`fileList${event.name}`].splice(event.index, 1);
		},
		handleSetLike(starIndex) {
			if (this.likeCount !== starIndex) {
				this.likeCount = starIndex;
			}
		},
		handleSelect(e) {
			console.log(e)
			this.fileList = this.fileList.concat(e.tempFiles)
		},
		handlePost() {
			if (!this.likeCount) {
				return this.$toast({
					title: 'The rating cannot be empty'
				})
			}

			if (this.reviewTxt == '') {
				return this.$toast({
					title: 'Comments cannot be empty'
				})
			}

			if (!this.fileList.length) {
				return this.$toast({
					title: 'Please upload the picture'
				})
			}

			const fileList = this.fileList.map(item => item.file);
			const formData = new FormData();
			formData.append('ProId', this.ProId);
			formData.append('Rating', this.likeCount);
			formData.append('Content', this.reviewTxt);

			fileList.forEach((fileData, index) => {
				formData.append(`PicPath_${index}`, fileData);
			})

			addComment(formData).then(res => {
				console.log(res)
				this.$toast({
					title: 'Post Success!'
				})

				setTimeout(() => {
					uni.navigateBack();
					uni.$emit('handleReviewRefresh', true);
				}, 500)
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.order-review-box {
	padding: 23rpx 31rpx;
	box-sizing: border-box;

	.review-box {
		background: #FFFFFF;
		border-radius: 31rpx;
		box-sizing: border-box;
		padding: 31rpx;

		.like-button-box {
			margin-bottom: 113rpx;
			display: flex;
			align-items: center;
			@include flex-gap(0, 38rpx); // 替换了 column-gap

			.like-item {
				width: 58rpx;
				height: 58rpx;
				border-radius: 12rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				.star-img {
					width: 40rpx;
					height: 40rpx;
					display: block;
				}
			}
		}

		.review-txt {
			padding: 0;
			margin-bottom: 27rpx;
		}
	}

	.post-btn {
		bottom: 96rpx;
		width: 688rpx;
		height: 112rpx;
		background: #FF5A1E;
		border-radius: 19rpx;
		font-weight: bold;
		color: #FFFFFF;
		margin-top: 488rpx;

		::v-deep .u-button__text {
			font-size: 38rpx !important;
		}
	}
}
</style>