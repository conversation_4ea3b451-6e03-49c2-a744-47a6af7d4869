<template>
	<view>
		<navbar autoBack />
		<!-- <PaypalButton :style="{ opacity: btnOpacity }" class="web-paypal-page" ref="paypalButton"
			@cancel="onOrderDetailPaymentCancel" @error="onPaymentError" @success="onPaymentSuccess"
			@open-view="onOpenView" :payFetch="handlePayment" v-show='showPayPalIframe' />
		<view class="stripe-payment">
			<StripeButton :clientSecret="stripeOrderInfo.client_secret" :orderInfo="stripeOrderInfo" />
		</view> -->

		<u-loading-page :loading="isPageLoading" bg-color="#F0F0F0" iconSize="30"
			loadingText="Loading"></u-loading-page>

		<view v-if="!isPageLoading" class="order-status-box">
			<!-- 订单状态 -->
			<OrderStatusInfo class="order-status-info-box" :orderStatusText="OrderStatusDetail.OrderStatusText"
				:orderStatusIntro="OrderStatusDetail.orderStatusDesc" />

			<!-- 地址信息 -->
			<view class="address-box">
				<view class="shopping-box"
					v-if="OrderStatusDetail.OrderStatus == 5 || OrderStatusDetail.OrderStatus == 6">
					<u--image class="shippingImg" radius="50%" width="42rpx" height="42rpx"
						:src="shippingTypeImg"></u--image>
					<u--text :text="ExpressType" lines="1" color="#262626" size="29rpx" bold
						style="flex: none; width: fit-content;"></u--text>
					<u-line direction="col" color="#D9D9D9" length="25rpx" margin="0 21rpx 0"
						v-if="TrackingNumber"></u-line>
					<u--text :text="TrackingNumber" lines="1" color="#262626" size="29rpx" bold></u--text>
				</view>

				<!-- @click="handleSelectAddress" -->
				<view class="address-info">
					<u-icon class="address-img" size="52rpx" name="/static/assets/mine/orderAddress.png"></u-icon>
					<view>
						<view class="flex align-center" style="color: #262626;font-weight: bold;">
							{{ shipAddName }} &nbsp;&nbsp;{{ PhoneNumber }}
						</view>
						<u--text :text="shipAddress" size="29rpx" color="#262626" margin="4rpx 0 0 0" bold
							style="word-break: break-all;"></u--text>
					</view>
				</view>
			</view>

			<!-- 订单信息 -->
			<u-cell-group class="common-box-style" :border="false"
				:custom-style="{ 'paddingBottom': '10rpx', overflow: 'hidden' }">
				<view v-for="(item, index) in OrderStatusDetail.orderProduct" :key="index"
					@click="handleClickOrderItem(item)">
					<OrderItem :PicPath="item.PicPath" :Name_en="item.Name" :goodsPrice="item.Price" :Qty="item.Qty"
						:customPriceSymbol="priceSymbol" :isGift="item.BuyType == 0 && item.IsGift == '1'">
						<template slot="skuInfo">
							<!-- 普通商品Sku -->
							<block v-if="item.BuyType == 0 && Object.keys(item.Property).length">
								<view v-for="(value, key) in item.Property" :key="key">
									<u--text :text="`${key}: ${value}`" color="#8C8C8C" size="27rpx"></u--text>
								</view>
							</block>

							<!-- 组合促销 主Sku -->
							<block
								v-if="item.BuyType == '4' && mainSku(item.ProId, item.Property) && Object.keys(item.Property).length">
								<view v-for="(value, key) in mainSku(item.ProId, item.Property)" :key="key">
									<u--text :text="`${key}:${value}`" color="#8C8C8C" size="27rpx"></u--text>
								</view>
							</block>
							<block v-if="item.BuyType == '5' && Object.keys(item.Property).length">
								<view v-for="(value, key) in item.Property" :key="key">
									<u--text :text="`${key}:${value}`" color="#8C8C8C" size="27rpx"></u--text>
								</view>
							</block>
						</template>
						<template slot="footer">
							<block v-if="item.BuyType == '4' && item.package_row.length">
								<block v-for="(cItem, cIndex) in item.package_row" :key="cItem.ProId">
									<OrderItem :PicPath="cItem.PicPath_0" :Name_en="cItem.Name_en" :isPrice="false"
										:goodsPrice="cItem.Price" :isQty="true" Qty="1" style="padding-right: 0;"
										class="package-item-1">
										<template slot="skuInfo">
											<view v-for="(value, key) in mainSku(cItem.ProId, item.Property)"
												:key="key">
												<u--text :text="`${key}: ${value}`" color="#8C8C8C"
													size="27rpx"></u--text>
											</view>
										</template>
									</OrderItem>
								</block>
							</block>
						</template>
					</OrderItem>
				</view>
				<u-gap height="2rpx" bgColor="#F7F7F7" style="margin: 0 31rpx;"></u-gap>

				<!-- Coupon  -->
				<u-cell :border="false" center :titleStyle="titleStyle">
					<u-icon slot="icon" size="46rpx" name="/static/assets/mine/coupon.png"></u-icon>
					<u--text slot="title" text="Coupon" color="#262626" size="29rpx" bold></u--text>
					<view slot="value" style="text-align: right;">
						<text style="font-weight: bold; font-size: 29rpx; color: #262626;">
							-{{ priceSymbol }}{{ couponPriceData }}
						</text>
						<text v-if="OrderStatusDetail.CouponCode" style="color: #8C8C8C; font-size: 26rpx;">
							（{{ OrderStatusDetail.CouponCode }}）
						</text>
					</view>
				</u-cell>

				<!--Coins  -->
				<u-cell v-if="OrderStatusDetail.orders_price_ary.UseGold > 0" :border="false" center
					:titleStyle="titleStyle">
					<u-icon slot="icon" size="46rpx" name="/static/assets/mine/coins.png"></u-icon>
					<u--text slot="title" text="Coins" color="#262626" size="29rpx" bold></u--text>
					<view slot="value" style="text-align: right;">
						<text style="font-weight: bold; font-size: 29rpx; color: #262626;">
							-{{ priceSymbol }}{{ OrderStatusDetail.orders_price_ary.UseGold }}
						</text>
						<text v-if="OrderStatusDetail.GoldText" style="color: #8C8C8C; font-size: 26rpx;">
							（{{ OrderStatusDetail.GoldText }}）
						</text>
					</view>
				</u-cell>

				<!--Gift Card  -->
				<u-cell v-if="OrderStatusDetail.CardCode" :border="false" center :titleStyle="titleStyle">
					<u-icon slot="icon" size="46rpx" name="/static/assets/mine/gift_card.png"></u-icon>
					<u--text slot="title" text="Gift Card" color="#262626" size="29rpx" bold></u--text>
					<view slot="value" style="text-align: right;">
						<text style="font-weight: bold; font-size: 29rpx; color: #262626;">
							-{{ priceSymbol }}{{ OrderStatusDetail.orders_price_ary.CardPrice ||
								OrderStatusDetail.orders_price_ary.CradPrice }}
						</text>
						<text v-if="OrderStatusDetail.CardCode" style="color: #8C8C8C; font-size: 26rpx;">
							（{{ OrderStatusDetail.CardCode }}）
						</text>
					</view>
				</u-cell>
				<!-- 如果没有CardCode但有CradPrice，显示普通礼品卡 -->
				<view
					v-else-if="OrderStatusDetail.orders_price_ary.CradPrice && OrderStatusDetail.orders_price_ary.CradPrice != '0.00'">
					<u-cell :border="false" center
						:value="`-${priceSymbol}${OrderStatusDetail.orders_price_ary.CradPrice}`"
						:titleStyle="titleStyle">
						<u-icon slot="icon" size="46rpx" name="/static/assets/mine/gift_card.png"></u-icon>
						<u--text slot="title" text="Gift Card" color="#262626" size="29rpx" bold></u--text>
					</u-cell>
				</view>
				<u-cell :border="false" center title="Shipping" :value="`${priceSymbol}${currentShippingPrice}`"
					:titleStyle="titleStyle">
					<u-icon slot="icon" size="46rpx" name="/static/assets/mine/shipping.png"></u-icon>
				</u-cell>
				<u-cell :border="false" center title="Taxes" :value="`${priceSymbol}${calculatedTax}`"
					:titleStyle="titleStyle">
					<u-icon slot="icon" size="46rpx" name="/static/assets/mine/taxes.png"></u-icon>
				</u-cell>
				<u-cell :border="false" center title="Handling Charge"
					:value="`${priceSymbol}${calculatedServiceCharge}`" :titleStyle="titleStyle">
					<u-icon slot="icon" size="46rpx" name="/static/assets/mine/handling_charge.png"></u-icon>
				</u-cell>
			</u-cell-group>

			<!-- 订单详情 -->
			<u-cell-group class="common-box-style" :border="false"
				:custom-style="{ 'marginTop': '31rpx', overflow: 'hidden' }">
				<u-cell :border="false" center title="Order Date" :value="OrderStatusDetail.OrderTime"
					:titleStyle="titleStyle">
				</u-cell>
				<u-cell :border="false" center title="Order Number" :value="OrderStatusDetail.orderNo"
					:titleStyle="titleStyle">
				</u-cell>
				<u-cell :border="false" center title="Order Payment" :value="`${priceSymbol}${calculatedTotalPrice}`"
					:titleStyle="titleStyle">
				</u-cell>

				<!-- Payment Method - 待支付状态和支付失败状态都显示可选择的支付方式 -->
				<block v-if="OrderStatusDetail.OrderStatus == 1 || OrderStatusDetail.OrderStatus == 3">
					<!-- Payment Method Selection -->
					<u-cell-group class="common-box-style" :border="false" :custom-style="{ 'paddingBottom': '10rpx' }">
						<u-cell>
							<u--text slot="title" text="Payment Method" color="rgba(0,0,0,0.8)" size="31rpx"
								bold></u--text>
						</u-cell>

						<view v-for="(item, index) in payChannelsData" :key="item.PId"
							@click="choosePaymentMethod(item, index)">
							<template>
								<u-cell :border="false">
									<u--image v-if="item.LogoPath" :src="item.LogoPath" slot="icon" width="40px"
										height="20px" mode="aspectFit"></u--image>
									<u--text slot="title" :text="item.Name_en" size="26rpx" bold
										margin="0 0 0 5px"></u--text>

									<template slot="right-icon">
										<u-icon v-show="currentPaymentIdx === index" name="checkmark-circle-fill"
											color="#FF5A1E" size="38rpx"></u-icon>
									</template>
								</u-cell>

								<u-cell v-if="item.CardImg && item.CardImg.length" :border="false">
									<template slot="title">
										<scroll-view scroll-x style="width: 240rpx;">
											<view class="flex" style="margin-right: 5rpx;">
												<view v-for="(cardImg, cardIndex) in item.CardImg" :key="cardIndex">
													<u--image :src="cardImg" width="40px" height="20px"
														mode="aspectFit"></u--image>
												</view>
											</view>
										</scroll-view>
									</template>
									<u--text slot="value" text="Credit or Debit Cards" color="#8C8C8C" size="26rpx"
										bold></u--text>
									<u-icon slot="right-icon" name="/static/assets/common/arrow_next.png"></u-icon>
								</u-cell>
							</template>
						</view>
					</u-cell-group>
				</block>
				<!-- 其他状态只显示支付方式名称 -->
				<block v-else>
					<u-cell :border="false" center title="Payment Method" :value="currentPaymentMethodName"
						:titleStyle="titleStyle"></u-cell>
				</block>

				<block v-if="OrderStatusDetail.Trade_Number">
					<u-cell :border="false" center title="Trade Number" :value="OrderStatusDetail.Trade_Number"
						:titleStyle="titleStyle"></u-cell>
				</block>



			</u-cell-group>

			<!-- OrderStatus = 5 已发货， 6： 已完成 -->
			<view class="footer-options-box"
				v-if="OrderStatusDetail.OrderStatus == 5 || OrderStatusDetail.OrderStatus == 6">
				<view class="payment-options">
					<!-- 客服、物流、delivery、review -->
					<view class="more-options-btn">
						<u--image src="/static/assets/mine/order_services.png" width="85rpx" height="85rpx"></u--image>
						<view class="options-right">
							<u-button class="logistics-btn" text="Logistics" shape="circle"
								@click="handleLogistics"></u-button>

							<u-button v-if="OrderStatusDetail.OrderStatus == 5" class="delivery-btn" text="Delivery"
								shape="circle" :loading="isDeliveryLoading" @click="handleDelivery"></u-button>

							<u-button v-if="OrderStatusDetail.OrderStatus == 6" class="delivery-btn" text="Review"
								shape="circle" @click="handleReview"></u-button>
						</view>
					</view>
				</view>

				<u-gap height="65rpx" bgColor="#fff"></u-gap>
			</view>

			<!-- 支付按钮 OrderStatus = 1 待支付, OrderStatus = 3 支付失败 -->
			<PaymentButton v-if="OrderStatusDetail.OrderStatus == 1 || OrderStatusDetail.OrderStatus == 3"
				:isPaymentLoading="isPaymentLoading" :Price="calculatedTotalPrice" @handlePayment="handlePayment">
			</PaymentButton>
		</view>

		<!-- coupon -->
		<popup-box ref="couponRef" mainTitle="Coupon">
			<template slot="content">
				<coupon-list :selectCouponCid="selectCouponCid" :couponData="couponData"
					@handleSelectCoupon="handleSelectCoupon" />
			</template>
		</popup-box>

		<!-- address -->
		<popup-box ref="addressRef" mainTitle="Address">
			<template slot="content">
				<address-list :selectAddAid="selectAddAid" @handleSelectAdd="handleSelectAdd" />
			</template>
		</popup-box>

		<view v-if="!isHidePage && (OrderStatusDetail.OrderStatus == 1 || OrderStatusDetail.OrderStatus == 3)">
			<!-- PayPal button -->
			<PaypalButton :style="{ paddingBottom: safeAreaBottom + 'px', opacity: $config.payment.btnOpacity }"
				class="web-paypal-page" ref="paypalButton" @cancel="onOrderDetailPaymentCancel" @error="onPaymentError"
				@success="onPaymentSuccess" @open-view="onOpenView" :payFetch="handlePayment"
				v-show="showPayPalIframe && !isHidePage" />

			<!-- Stripe button -->
			<view class="stripe-payment" v-show="showStripePayment">
				<StripeButton :clientSecret="stripeOrderInfo.client_secret" :orderInfo="stripeOrderInfo" />
			</view>

			<!-- Amazon Pay button -->
			<amazon-pay-button v-show="showAmazonPay" :payFetch="handlePayment" @open-view="onOpenView" />
		</view>

		<!-- Credit or Debit Cards -->
		<popup-box ref="isCreditDebitRef" mainTitle="Credit or Debit Cards">
			<template slot="content">
				<view class="credit-debit-box">
					<u--input v-model.trim="creditData.IssuingBank" placeholder="Bank Name" border="none"
						:placeholderStyle="creditPlaceholderStyle" clearable></u--input>
					<u--input v-model.trim="creditData.CardNo" placeholder="Card Number" border="none"
						:placeholderStyle="creditPlaceholderStyle" clearable></u--input>
					<u--input v-model.trim="creditData.CardSecurityCode" placeholder="CYC" border="none"
						:placeholderStyle="creditPlaceholderStyle" clearable></u--input>
					<view class="date-box">
						<view class="item" @click.native.stop="handleOpenMM">
							<u--text :text="creditData.CardExpireMonth || 'MM'"
								:color="creditData.CardExpireMonth ? '#262626' : '#8C8C8C'" size="30rpx" bold></u--text>
							<u-icon name="arrow-down" color="#CCCCCC"></u-icon>
						</view>
						<view class="item" @click.native.stop="handleOpenYY">
							<u--text :text="creditData.CardExpireYear || 'YY'"
								:color="creditData.CardExpireYear ? '#262626' : '#8C8C8C'" size="30rpx" bold></u--text>
							<u-icon name="arrow-down" color="#CCCCCC"></u-icon>
						</view>
					</view>

					<SubmitButton class="credit-btn" @handleConfirm="handleCreditConfirm"></SubmitButton>
				</view>
			</template>
		</popup-box>

		<!-- 月份 -->
		<u-picker class="picker-box" title="MM" cancelText="Cancel" cancelColor="#8C8C8C" confirmText="Confirm"
			confirmColor="#FF5A1E" :show="isCreditMMShow" :columns="[creditMMData]" :defaultIndex="defaultMMIndex"
			closeOnClickOverlay @close="isCreditMMShow = false" @cancel="isCreditMMShow = false"
			@confirm="chooseCreditMM"></u-picker>

		<!-- 年份 -->
		<u-picker class="picker-box" title="YY" cancelText="Cancel" cancelColor="#8C8C8C" confirmText="Confirm"
			confirmColor="#FF5A1E" :show="isCreditYYShow" :columns="[creditYYData]" :defaultIndex="defaultYYIndex"
			closeOnClickOverlay @close="isCreditYYShow = false" @cancel="isCreditYYShow = false"
			@confirm="chooseCreditYY"></u-picker>

	</view>
</template>

<script>
import {
	getOrderDetail,
	payCommonCreate,
	getOrderTrackRegister,
	getCoupon,
	getPayChannels
} from "@/api/orderDetails.js";
import {
	userAddress
} from '@/api/address.js';
import OrderStatusInfo from './OrderStatusInfo.vue';
import {
	mapGetters
} from "vuex";
import payment from '@/mixins/payment.js';
import paypal_handle from '@/mixins/paypal_handle.js';
import StripeButton from '@/components/StripeButton/StripeButton.vue'
import AmazonPayButton from '@/components/AmazonPayButton/AmazonPayButton.vue'
import { PriceCalculator } from '@/pages/goodsDetail/utils/skuManager.js';
import { over } from "lodash-es";
import paymentSDKManager from '@/utils/payment/paymentSDKManager.js'
export default {
	mixins: [payment, paypal_handle],
	components: {
		OrderStatusInfo,
		StripeButton,
		AmazonPayButton
	},
	data() {
		return {
			isHidePage: false,
			isPageLoading: true,
			isPaymentLoading: false,
			isDeliveryLoading: false,
			OrderStatusDetail: {},
			OrderId: '',
			shippingTypeImg: '',
			ExpressType: '',
			TrackingNumber: '',
			couponData: [],
			CouponPrice: '0.00',
			selectCouponPrice: '',
			selectCouponData: {},
			selectCouponCid: '',
			selectAddData: {},
			selectAddAid: '',

			userInfoData: {},
			userAddInfo: {},
			// 标题样式
			titleStyle: {
				'font-weight': 'bold',
				'font-size': '29rpx',
				'color': '#262626',
				'white-space': 'nowrap'
			},
			priceSymbol: '',
			stripeOrderInfo: {},
			// 支付方式相关
			payChannelsData: [],
			currentPaymentIdx: 0,
			// Credit Card 相关
			creditData: {
				IssuingBank: '',
				CardNo: '',
				CardSecurityCode: '',
				CardExpireMonth: '',
				CardExpireYear: ''
			},
			isCreditMMShow: false,
			creditMMData: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'],
			isCreditYYShow: false,
			creditYYData: Array.from({
				length: new Date().getFullYear() - 1900 + 1
			}, (_, i) => String(1900 + i)).reverse(),
			defaultMMIndex: [],
			defaultYYIndex: [],
			creditPlaceholderStyle: 'color:#8C8C8C; font-size: 30rpx; font-weight:bold',
		};
	},
	onLoad(e) {
		this.isPageLoading = true;
		if (e.OrderId) {
			this.OrderId = e.OrderId;
			this.getOrderDetail();
			this.getPayChannels();
		}
	},
	mounted() {
		// 页面加载完成，无需预加载SDK
	},
	computed: {
		...mapGetters(['statusBarAndNavHeight', 'safeAreaBottom']),
		showPayPalIframe() {
			// 支持动态切换支付方式
			if (this.payChannelsData.length > 0) {
				return this.payChannelsData[this.currentPaymentIdx]?.PId == 1 && (this.OrderStatusDetail.OrderStatus == 1 || this.OrderStatusDetail.OrderStatus == 3);
			}
			return this.OrderStatusDetail.PId == '1' && (this.OrderStatusDetail.OrderStatus == 1 || this.OrderStatusDetail.OrderStatus == 3)
		},
		showStripePayment() {
			// 支持动态切换支付方式
			if (this.payChannelsData.length > 0) {
				return this.payChannelsData[this.currentPaymentIdx]?.PId == 86 && (this.OrderStatusDetail.OrderStatus == 1 || this.OrderStatusDetail.OrderStatus == 3);
			}
			return this.OrderStatusDetail.PId == 86 && (this.OrderStatusDetail.OrderStatus == 1 || this.OrderStatusDetail.OrderStatus == 3);
		},
		showAmazonPay() {
			// 支持动态切换支付方式
			if (this.payChannelsData.length > 0) {
				return this.payChannelsData[this.currentPaymentIdx]?.PId == 89 && (this.OrderStatusDetail.OrderStatus == 1 || this.OrderStatusDetail.OrderStatus == 3);
			}
			return this.OrderStatusDetail.PId == 89 && (this.OrderStatusDetail.OrderStatus == 1 || this.OrderStatusDetail.OrderStatus == 3);
		},
		// 当前选中的支付方式名称
		currentPaymentMethodName() {
			if (this.payChannelsData.length > 0) {
				return this.payChannelsData[this.currentPaymentIdx]?.Name_en || this.OrderStatusDetail.PaymentMethod;
			}
			return this.OrderStatusDetail.PaymentMethod;
		},
		shipAddName() {
			const {
				FirstName,
				LastName,
				PhoneNumber
			} = this.userInfoData;

			return `${FirstName} ${LastName}`;
		},
		PhoneNumber() {
			const {
				PhoneNumber
			} = this.userInfoData

			return `${PhoneNumber}`
		},
		shipAddress() {
			const {
				Country,
				State,
				City,
				AddressLine1
			} = this.userInfoData
			return `${Country},${State},${City},${AddressLine1}`;
		},
		mainSku() {
			return (proId, property) => {
				const mainSkuData = property[proId];
				if (mainSkuData && Object.keys(mainSkuData).length) {
					return mainSkuData;
				} else {
					return '';
				}
			}
		},
		couponPriceData() {
			return this.selectCouponPrice || this.CouponPrice;
		},
		// 当前运费 - 直接使用接口返回值
		currentShippingPrice() {
			if (!this.OrderStatusDetail.orders_price_ary?.ShippingPrice) {
				return '0.00';
			}
			return PriceCalculator.ceilToString(parseFloat(this.OrderStatusDetail.orders_price_ary.ShippingPrice), 2);
		},
		// 计算税费 - 根据地址和商品金额重新计算
		calculatedTax() {
			// 如果是已完成订单，直接使用接口返回的税费
			if (this.OrderStatusDetail.OrderStatus > 3) {
				return this.OrderStatusDetail.orders_price_ary?.TaxPrice || '0.00';
			}

			// 对于待支付和支付失败的订单，可能需要重新计算税费
			// 这里暂时使用接口返回值，后续可根据地址变更重新计算
			const taxPrice = this.OrderStatusDetail.orders_price_ary?.TaxPrice || '0.00';
			return PriceCalculator.ceilToString(parseFloat(taxPrice), 2);
		},
		// 计算手续费 - 根据当前选择的支付方式计算
		calculatedServiceCharge() {
			const currentPayment = this.payChannelsData[this.currentPaymentIdx];
			if (!currentPayment) return '0.00';

			const additionalFeePercent = PriceCalculator.divide(parseFloat(currentPayment.AdditionalFee || 0), 100);
			const affixPrice = parseFloat(currentPayment.AffixPrice || 0);

			// 如果百分比和额外费用都为0，则不计算手续费
			if (additionalFeePercent === 0 && affixPrice === 0) {
				return '0.00';
			}

			// 获取商品总价、运费、税费
			const goodsAmount = parseFloat(this.OrderStatusDetail.orders_price_ary?.ProductPrice || 0);
			const shippingFee = parseFloat(this.currentShippingPrice);
			const taxAmount = parseFloat(this.calculatedTax);

			// 优惠扣减
			const couponDiscount = parseFloat(this.OrderStatusDetail.orders_price_ary?.CouponPrice || 0);
			const giftCardDiscount = parseFloat(this.OrderStatusDetail.orders_price_ary?.CardPrice || this.OrderStatusDetail.orders_price_ary?.CradPrice || 0);

			// 手续费基数 = 商品金额 + 运费 + 税费 - 优惠券 - 礼品卡
			const serviceChargeBase = PriceCalculator.subtract(
				PriceCalculator.subtract(
					PriceCalculator.add(
						PriceCalculator.add(goodsAmount, shippingFee),
						taxAmount
					),
					couponDiscount
				),
				giftCardDiscount
			);

			// 手续费 = 基数 * 百分比 + 额外费用
			const serviceCharge = PriceCalculator.add(
				PriceCalculator.multiply(serviceChargeBase, additionalFeePercent),
				affixPrice
			);

			return PriceCalculator.ceilToString(Math.max(0, serviceCharge), 2);
		},
		// 计算最终总价 - 包含手续费
		calculatedTotalPrice() {
			// 如果是已完成订单，直接使用接口返回的总价
			if (this.OrderStatusDetail.OrderStatus > 3) {
				return this.OrderStatusDetail.totalPrice || '0.00';
			}

			// 对于待支付和支付失败的订单，重新计算包含手续费的总价
			const goodsAmount = parseFloat(this.OrderStatusDetail.orders_price_ary?.ProductPrice || 0);
			const shippingFee = parseFloat(this.currentShippingPrice);
			const taxAmount = parseFloat(this.calculatedTax);
			const serviceCharge = parseFloat(this.calculatedServiceCharge);

			// 优惠扣减
			const couponDiscount = parseFloat(this.OrderStatusDetail.orders_price_ary?.CouponPrice || 0);
			const giftCardDiscount = parseFloat(this.OrderStatusDetail.orders_price_ary?.CardPrice || this.OrderStatusDetail.orders_price_ary?.CradPrice || 0);
			const goldDiscount = parseFloat(this.OrderStatusDetail.orders_price_ary?.UseGold || 0);

			// 总价 = 商品金额 + 运费 + 税费 + 手续费 - 优惠券 - 礼品卡 - 金币
			const totalPrice = PriceCalculator.subtract(
				PriceCalculator.subtract(
					PriceCalculator.subtract(
						PriceCalculator.add(
							PriceCalculator.add(
								PriceCalculator.add(goodsAmount, shippingFee),
								taxAmount
							),
							serviceCharge
						),
						couponDiscount
					),
					giftCardDiscount
				),
				goldDiscount
			);

			return PriceCalculator.ceilToString(Math.max(0, totalPrice), 2);
		}
	},
	watch: {
		isCreditMMShow(bool, old) {
			if (!bool) {
				this.$refs.isCreditDebitRef.isShow = true;
			}
		},
		isCreditYYShow(bool, old) {
			if (!bool) {
				this.$refs.isCreditDebitRef.isShow = true;
			}
		},
		// 监听支付方式变化，重新计算手续费
		currentPaymentIdx() {
			// 支付方式变化时会自动重新计算calculatedServiceCharge
		}
	},
	methods: {
		// 在支付前确保对应SDK已加载
		async ensureSDKForPayment(paymentId) {
			console.log(`🚀 确保支付方式 ${paymentId} 的SDK已加载...`);

			try {
				await paymentSDKManager.ensureSDKByPaymentId(paymentId);
				console.log(`✅ 支付方式 ${paymentId} SDK加载完成`);
				return true;
			} catch (error) {
				console.error(`❌ 支付方式 ${paymentId} SDK加载失败:`, error);
				throw error;
			}
		},
		// 订单详情
		getOrderDetail() {
			getOrderDetail({
				OrderId: this.OrderId
			}).then(res => {
				console.log(res)
				this.OrderStatusDetail = res;
				this.userInfoData = res?.shiptoAry;
				console.log('this.userInfoData', this.userInfoData)
				this.shippingTypeImg = res?.shippingTypeImg;
				this.ExpressType = res.validPackage[0]?.ExpressType;
				this.TrackingNumber = res.validPackage[0]?.TrackingNumber;
				this.CouponPrice = res.orders_price_ary.CouponPrice;
				this.priceSymbol = res.NewSymbol;

				// 根据当前订单的PId设置默认选中的支付方式
				this.setCurrentPaymentMethod();
			}).finally(() => {
				this.isPageLoading = false;
			});
		},
		// 获取支付方式列表
		getPayChannels() {
			getPayChannels().then(res => {
				this.payChannelsData = res;
				this.setCurrentPaymentMethod();
			})
		},
		// 设置当前选中的支付方式
		setCurrentPaymentMethod() {
			if (this.payChannelsData.length > 0 && this.OrderStatusDetail.PId) {
				const currentIndex = this.payChannelsData.findIndex(item => item.PId == this.OrderStatusDetail.PId);
				if (currentIndex !== -1) {
					this.currentPaymentIdx = currentIndex;
				}
			}
		},
		// 选择支付方式
		choosePaymentMethod(item, index) {
			this.currentPaymentIdx = index;

			// 如果选择的是Authorize.Net (PId == 87)，打开信用卡填写弹窗
			if (item.PId == 87) {
				this.$refs.isCreditDebitRef.isShow = true;
				this.creditData.PId = item.PId;
			} else {
				// 其他支付方式直接更新
				this.updateOrderPaymentMethod(item.PId);
			}
		},
		// 更新订单支付方式（本地更新）
		updateOrderPaymentMethod(newPId) {
			// 更新本地数据
			this.OrderStatusDetail.PId = newPId;
			this.OrderStatusDetail.PaymentMethod = this.payChannelsData[this.currentPaymentIdx]?.Name_en;
		},
		// Credit Card 相关方法
		handleOpenMM() {
			this.isCreditMMShow = true;
			this.$refs.isCreditDebitRef.isShow = false;
		},
		chooseCreditMM(MM) {
			this.creditData.CardExpireMonth = MM?.value[0];
			this.isCreditMMShow = false;
		},
		handleOpenYY() {
			this.isCreditYYShow = true;
			this.$refs.isCreditDebitRef.isShow = false;
		},
		chooseCreditYY(YY) {
			this.creditData.CardExpireYear = YY?.value[0];
			this.isCreditYYShow = false;
		},
		handleCreditConfirm() {
			const {
				IssuingBank,
				CardNo,
				CardSecurityCode,
				CardExpireMonth,
				CardExpireYear
			} = this.creditData;

			if (IssuingBank == '') {
				return this.$toast({
					title: 'Please select the issuing bank'
				})
			}
			if (CardNo == '') {
				return this.$toast({
					title: 'Please enter the card number'
				})
			}
			if (CardSecurityCode == '') {
				return this.$toast({
					title: 'Please enter the card security code'
				})
			}
			if (CardExpireMonth == '') {
				return this.$toast({
					title: 'Please select the card expiration month'
				})
			}
			if (CardExpireYear == '') {
				return this.$toast({
					title: 'Please select the card expiration year'
				})
			}

			// 信用卡信息验证通过，更新支付方式为Authorize.Net
			this.$refs.isCreditDebitRef.isShow = false;
			this.updateOrderPaymentMethod(87);
		},
		// 处理支付	 
		async handlePayment() {
			try {
				this.isPaymentLoading = true;

				const {
					PId,
					orderNo
				} = this.OrderStatusDetail;

				// 在支付开始前确保对应SDK已加载
				try {
					await this.ensureSDKForPayment(PId);
				} catch (error) {
					this.$toast({
						title: 'Payment system loading failed, please try again',
						icon: 'none'
					});
					this.isPaymentLoading = false;
					return false;
				}

				// 如果选择的是Authorize.Net支付，验证信用卡信息是否完整
				if (PId == 87) {
					const {
						IssuingBank,
						CardNo,
						CardSecurityCode,
						CardExpireMonth,
						CardExpireYear
					} = this.creditData;

					if (!IssuingBank || !CardNo || !CardSecurityCode || !CardExpireMonth || !CardExpireYear) {
						this.$toast({
							title: 'Please complete credit card information',
							icon: 'none'
						});
						this.isPaymentLoading = false;
						return false;
					}
				}

				uni.showLoading({
					title: 'Loading...',
					mask: true
				})

				let payCommonData = {
					OId: orderNo,
					PId,
					'authorized[CardNo]': '',
					'authorized[CardExpireMonth]': '',
					'authorized[CardExpireYear]': '',
					'authorized[CardSecurityCode]': '',
					'authorized[IssuingBank]': '',
				};

				// 如果是Authorize.Net支付且有信用卡数据，创建正确的数据结构
				if (PId == 87 && this.creditData.IssuingBank) {
					payCommonData.authorized = {
						IssuingBank: this.creditData.IssuingBank,
						CardNo: this.creditData.CardNo,
						CardSecurityCode: this.creditData.CardSecurityCode,
						CardExpireMonth: this.creditData.CardExpireMonth,
						CardExpireYear: this.creditData.CardExpireYear
					};
				}
				setTimeout(() => {
					this.onOpenView();
				}, 100);
				const payCommonCreateResult = await this.paymentCommonCreateFn(payCommonData)
				if (this.OrderStatusDetail.PId == 1) {
					this.payData = payCommonCreateResult;
					this.payData.paymentNumber = payCommonCreateResult.paymentNumber;
					uni.hideLoading();
					return this.payData.paymentNumber;
				}
				else if (this.OrderStatusDetail.PId == 86) {
					this.stripeOrderInfo = payCommonCreateResult;
					uni.hideLoading();
					return;
				}
				if (this.OrderStatusDetail.PId == 89) {
					// 确保返回正确的数据格式
					this.amazonPayData = payCommonCreateResult;
					return payCommonCreateResult;
				}
				await this.loadAffirmJs();
				const affirmCancelCallback = () => {
					this.$toast({
						title: 'Payment Cancelled'
					})
					this.getOrderDetail()
				}
				await this.loadAffirm(payCommonCreateResult, orderNo, this.OrderId, affirmCancelCallback)
				uni.hideLoading();
			} catch (error) {
				uni.hideLoading();
				this.isPaymentLoading = false;
				console.log('错误', error);
			} finally {
				this.isPaymentLoading = false;
			}
		},
		handleDelivery() {
			this.isDeliveryLoading = true;

			setTimeout(() => {
				this.isDeliveryLoading = false
				uni.showToast({
					mask: false,
					title: 'Delivery Successful',
					image: "/static/assets/mine/delivery_success.png"
				})
			}, 2000)
		},
		handleReview() {
			this.$tab.navigateTo('/pages/tabs/mine/orderReview/orderReview');
		},
		handleLogistics() {
			getOrderTrackRegister({
				OrderId: this.OrderId
			}).then(res => {
				this.$tab.navigateTo(
					`/pages/tabs/mine/logisticsDetail/logisticsDetail?OrderId=${this.OrderId}`);
			})
		},
		handleOpenCoupon() {
			this.$refs.couponRef.isShow = true;
			this.selectCouponCid = +this.selectCouponData?.cid;

			const data = {
				CId: this.OrderId
			}

			getCoupon(data).then(res => {
				this.couponData = res;
			})
		},
		handleSelectCoupon(data) {
			this.selectCouponData = data;
			this.selectCouponPrice = data?.cutprice;

			setTimeout(() => {
				this.$refs.couponRef.isShow = false;
			}, 300)
		},
		handleSelectAddress() {
			this.$refs.addressRef.isShow = true;
			this.selectAddAid = this.userInfoData?.AId;
		},
		handleSelectAdd(data) {
			console.log(data)
			this.userInfoData = data;
			setTimeout(() => {
				this.$refs.addressRef.isShow = false;
			}, 300);
		},
		handleClickOrderItem(item) {
			this.$tab.navigateTo(`/pages/goodsDetail/goodsDetail?ProId=${item.ProId}`);
		},
		onHide() {
			this.isHidePage = true;
			// 移除监听器
			uni.$off('addressSelected', this.handleDirectAddressSelected);
		},
		onShow() {
			this.isHidePage = false;
		}
	}
}
</script>

<style lang="scss" scoped>
.order-status-box {
	padding: 23rpx 31rpx 250rpx;
	box-sizing: border-box;

	.address-box {
		margin: 46rpx 0 31rpx;
		padding: 30rpx 33rpx;
		background: #FFFFFF;
		border-radius: 31rpx;
		overflow: hidden;

		.shopping-box {
			display: flex;
			align-items: center;
			margin-bottom: 33rpx;

			.shippingImg {
				margin-right: 37rpx;
			}
		}

		.address-info {
			display: flex;
			align-items: center;

			.address-img {
				margin-right: 25rpx;
			}
		}

		.flow-box {
			display: flex;
			align-items: center;
			font-weight: bold;
			font-size: 29rpx;
			color: #262626;
		}
	}

	.footer-options-box {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		background: #FFFFFF;
		box-shadow: 0px -4rpx 8rpx 0px rgba(0, 0, 0, 0.1);
		box-sizing: border-box;

		.payment-options {
			height: 108rpx;
			padding: 13rpx 33rpx;
			box-sizing: border-box;

			.payment-button {
				position: relative;

				.amount {
					position: absolute;
					left: 0;
					top: 0;
					z-index: 10;
					width: 312rpx;
					height: 80rpx;
					background: #FBDED1;
					border-radius: 48rpx 88rpx 4rpx 48rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					font-weight: bold;
					font-size: 38rpx;
					color: #FF5A1E;
					border: 1px solid #FF5A1E;
				}

				.payment-btn {
					background: transparent;
					border: none;
					font-weight: 600;
					font-size: 38rpx;
					color: #FFFFFF;
					background: #FF5A1E;
					height: 85rpx;

					border-radius: 69rpx;
					display: flex;
					justify-content: flex-end;
					padding-right: 120rpx;
				}
			}

			.more-options-btn {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.options-right {
					display: flex;
					justify-content: space-between;
					align-items: center;
					@include flex-gap(0, 33rpx); // 替换了 column-gap

					.logistics-btn {
						width: 233rpx;
						height: 85rpx;
						border-radius: 65rpx;
						font-weight: 600;
						font-size: 38rpx;
						color: #FF5A1E;
						background: #FBDED1;
					}

					.delivery-btn {
						width: 233rpx;
						height: 85rpx;
						border-radius: 65rpx;
						font-weight: 600;
						font-size: 38rpx;
						color: #FFFFFF;
						background: #FF5A1E;
					}
				}
			}
		}
	}
}

.web-paypal-page {
	position: fixed;
	bottom: -10rpx;
	right: 33rpx;
	z-index: 150;
	height: 92px;
	width: 50vw;
}

.credit-debit-box {
	display: flex;
	flex-direction: column;
	@include flex-gap(30rpx, 0); // 替换了 row-gap

	.u-input {
		background: #F0F0F0;
		border-radius: 31rpx;
		padding: 20rpx 31rpx !important;
		font-size: 35rpx;
		font-weight: bold;
		color: #262626;
	}

	.date-box {
		display: flex;
		justify-content: space-between;
		align-items: center;
		@include flex-gap(0, 42rpx); // 替换了 column-gap

		.item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			flex: 1;
			background: #F0F0F0;
			border-radius: 31rpx;
			padding: 20rpx 31rpx !important;
			font-size: 35rpx;
		}
	}

	.credit-btn {
		margin-top: 110rpx;
	}
}

.picker-box {
	::v-deep {
		.u-popup__content {
			border-radius: 30rpx 30rpx 0px 0px;

			.u-toolbar {
				border-bottom: 1px solid #E7E7E7;

				.u-toolbar__wrapper__cancel,
				.u-toolbar__wrapper__confirm {
					font-size: 30rpx;
					font-weight: bold;
				}

				.u-toolbar__title {
					font-size: 38rpx;
					color: #262626;
					font-weight: bold;
				}
			}
		}
	}
}
</style>