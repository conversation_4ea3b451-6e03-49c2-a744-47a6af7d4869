<template>
  <view class="mine-container">
    <navbar :isLeftShow="false"></navbar>
    <view class="mine-header">
      <view class="header-box-left"
        :style="consumerImg ? `background: url(${consumerImg}) no-repeat center center; background-size: 100% 100%;` : ''">
        <u-avatar :src="avatarImg" size="120rpx"></u-avatar>
      </view>
      <view class="user-info">
        {{ NickName }}
      </view>
    </view>

    <view class="content-section">
      <view class="section-item" v-for="(item, index) in sectionData" :key="index" @click="handleToPage(item.pageUrl)">
        <text class="count">{{ item.value }}</text>
        <text class="title">{{ item.title }}</text>
      </view>
    </view>

    <view class="order-box">
      <u-cell-group class="order-header" :border="false">
        <u-cell title="My Order" isLink url="/pages/tabs/mine/myOrder/myOrder" :titleStyle="myOrderStyle"
          :rightIconStyle="rightIconStyle"></u-cell>
      </u-cell-group>
      <view class="order-section">
        <view class="order-section-item" @click="handleToPage('/pages/tabs/mine/myOrder/myOrder?tabIndex=1')">
          <view class="order-icon-box">
            <image class="icon-img" src="/static/assets/mine/unpaid.png"></image>
          </view>
          <text class="title">Unpaid</text>
        </view>
        <view class="order-section-item" @click="handleToPage('/pages/tabs/mine/myOrder/myOrder?tabIndex=2')">
          <view class="order-icon-box">
            <image class="icon-img" src="/static/assets/mine/review.png"></image>
          </view>
          <text class="title">On Hold</text>
        </view>
        <view class="order-section-item" @click="handleToPage('/pages/tabs/mine/myOrder/myOrder?tabIndex=3')">
          <view class="order-icon-box">
            <image class="icon-img" src="/static/assets/mine/delivery.png"></image>
          </view>
          <text class="title">Delivery</text>
        </view>
        <view class="order-section-item" @click="handleToPage('/pages/tabs/mine/myOrder/myOrder?tabIndex=4')">
          <view class="order-icon-box">
            <image class="icon-img" src="/static/assets/mine/aftermarket.png"></image>
          </view>
          <text class="title">Archived</text>
        </view>
      </view>
    </view>

    <u-cell-group class="option-box" :border="false">
      <u-cell title="Address" isLink size="large" :titleStyle="titleStyle" :rightIconStyle="rightIconStyle"
        @click="handleToPage('/pages/tabs/cart/addressBook/addressBook')">
        <u--image src="/static/assets/mine/address.png" slot="icon" width="23" height="23"></u--image>
      </u-cell>
      <u-cell title="Services" :border="false" isLink size="large" :titleStyle="titleStyle"
        :rightIconStyle="rightIconStyle" @click="$store.commit('user/TO_SERVICE')">
        <u--image src="/static/assets/mine/services.png" slot="icon" width="23" height="23"></u--image>
      </u-cell>
    </u-cell-group>
    <view class="bottom-bar">
      <text class="version-text">Version {{ appVersion }}</text>
    </view>
  </view>
</template>

<script>
import { getUserCenterInfo } from "@/api/home.js";
import { mapGetters } from "vuex";
import tabBarResetScroll from "@/mixins/tabBarResetScroll.js";

export default {
  mixins: [tabBarResetScroll],
  data() {
    return {
      avatarImg: "",
      NickName: "",
      consumerImg: '',
      appVersion: require("@/version.json").version,
      sectionData: [
        {
          title: "Favorite",
          value: 0,
          pageUrl: "/pages/tabs/mine/myFavorite/myFavorite",
        },
        {
          title: "Record",
          value: 0,
          pageUrl: "/pages/tabs/mine/myRecords/myRecords",
        },
        {
          title: "Coupons",
          value: 0,
          pageUrl: "/pages/tabs/mine/myCoupons/myCoupons",
        },
        {
          title: "Coins",
          value: 0,
          pageUrl: "/pages/tabs/mine/CoinsRecord/CoinsRecord",
        },
      ],
      orderSection: [{}],
      myOrderStyle: {
        fontSize: "29rpx",
        fontWeight: "bold",
        color: "rgba(0,0,0,0.7)",
      },
      rightIconStyle: {
        fontSize: "35rpx",
        fontWeight: "bold",
      },
      titleStyle: {
        fontSize: "27rpx",
        fontWeight: "bold",
        color: "#262626",
        marginLeft: "15rpx",
      },
    };
  },
  onShow() {
    // 调用 mixin 的 onShow
    tabBarResetScroll.onShow.call(this);

    this.getUserCenterInfo();
  },
  computed: {
    ...mapGetters(["platForm"]),
  },
  methods: {
    handleToPage(pageUrl) {
      this.$tab.navigateTo(pageUrl);
    },
    getUserCenterInfo() {
      getUserCenterInfo().then((res) => {
        console.log(res.Avatar);
        this.avatarImg = res.Avatar;
        this.NickName = res.NickName;
        this.sectionData[0].value = res.Favorite;
        this.sectionData[1].value = res.Record;
        this.sectionData[2].value = res.Coupons;
        this.sectionData[3].value = res.Coins;
        this.consumerImg = res.ConsumerImg || '';
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.mine-container {
  padding: 23rpx 31rpx;

  .mine-header {
    display: flex;
    align-items: center;
    @include flex-gap(0, 52rpx); // 替换了 column-gap
    padding: 0 21rpx 42rpx;

    .header-box-left {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 163rpx;
      height: 167rpx;
    }

    .user-info {
      font-weight: bold;
      font-size: 38rpx;
      color: #262626;
    }
  }

  .content-section {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    @include flex-gap(0, 52rpx); // 替换了 column-gap
    justify-items: center;

    .section-item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .count {
        font-weight: 500;
        font-size: 38rpx;
        color: #262626;
      }

      .title {
        font-weight: 400;
        font-size: 27rpx;
        color: #8c8c8c;
      }
    }
  }

  .order-box {
    background: #ffffff;
    border-radius: 31rpx;
    margin: 52rpx 0 31rpx;
    box-sizing: border-box;
    overflow: hidden;

    .order-section {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      justify-items: center;

      .order-section-item {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 40rpx 0 31rpx;
        box-sizing: border-box;

        .order-icon-box {
          width: 46rpx;
          height: 46rpx;

          .icon-img {
            width: 100%;
            height: 100%;
          }
        }

        .title {
          font-weight: 600;
          font-size: 23rpx;
          color: #262626;
        }

        &:nth-child(2) {
          border-left: 2rpx solid #eaeaea;
          border-right: 2rpx solid #eaeaea;
        }

        &:nth-child(3) {
          border-right: 2rpx solid #eaeaea;
        }
      }
    }
  }

  .option-box {
    background: #ffffff;
    border-radius: 31rpx;
    box-sizing: border-box;
    overflow: hidden;
  }

  .bottom-bar {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20rpx 0;
    margin-top: 40rpx;

    .version-text {
      font-size: 24rpx;
      color: #999;
      font-weight: 400;
    }
  }
}
</style>
