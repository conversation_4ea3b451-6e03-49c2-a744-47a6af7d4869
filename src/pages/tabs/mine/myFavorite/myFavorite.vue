<template>
	<view>
		<navbar autoBack />
		<mescroll-body :up="upOption" @init="mescrollInit" @down="downCallback" @up="upCallback">
			<view class="favorite-box">
				<view class="favorite-box-item" v-for="(item, index) in list" :key="index">
					<view class="flex justify-between align-start">
						<!-- <u--image :src="item.img" width="180rpx" height="190rpx" mode="widthFix" duration="0"></u--image> -->
						<view class="pictrue">
							<easy-loadimage mode="widthFix" :imgStyle="{ maxHeight: '180rpx' }" :scroll-top="scrollTop"
								@tap="handleToPage(item.ProId)" loading-mode="skeleton-1"
								:image-src="item.img"></easy-loadimage>
						</view>

						<u--text :text="item.name" color="#262626" size="30rpx" bold :lines="2" margin="0 10px 0 10px"
							@tap="handleToPage(item.ProId)" style="flex: 2"></u--text>
						<u--text :text="`${priceSymbol}${item.Price}`" color="#FF5A1E" size="38rpx" bold
							@tap="handleToPage(item.ProId)" align="right"></u--text>
					</view>
					<view class="option-bottom">
						<u--image src="/static/assets/mine/delete.png" width="62rpx" height="62rpx"
							@click="delMyFavorite(item.ProId)"></u--image>
						<u--image src="/static/assets/mine/add.png" width="62rpx" height="62rpx"
							@tap="onAddCart(item)"></u--image>
					</view>
				</view>
			</view>

			<addPopup ref="addPopupRef"></addPopup>

			<u-modal :show="show" title="Are you sure delete it?" confirmText="Sure" cancelText="Cancel"
				showCancelButton closeOnClickOverlay confirmColor="#FF5A1E" @confirm="handleDeleteFavorite"
				@cancel="show = false" @close="show = false"></u-modal>
		</mescroll-body>
	</view>
</template>

<script>
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import {
	getMyFavorite,
	deleteFavorite,
	productDetail
} from '@/api/home.js';
import {
	mapGetters
} from "vuex";

export default {
	mixins: [MescrollMixin],
	data() {
		return {
			scrollTop: 0,
			show: false,
			ProId: '',
			list: [],
			upOption: {
				toTop: {
					duration: 0
				}
			}
		};
	},
	computed: {
		...mapGetters(["priceSymbol"])
	},
	onPageScroll({
		scrollTop
	}) {
		this.scrollTop = scrollTop;
	},
	methods: {
		upCallback(page) {
			getMyFavorite({
				page: page.num,
			}).then(res => {
				console.log(res);
				if (page.num == 1) {
					this.list = [];
				}

				this.list = this.list.concat(res.result);
				this.mescroll.endByPage(res.result.length, res.total_pages);
			}).catch(() => {
				this.mescroll.endErr();
			})
		},
		// 删除
		delMyFavorite(ProId) {
			this.show = true;
			this.ProId = ProId;
		},
		handleDeleteFavorite() {
			deleteFavorite({
				ProId: this.ProId
			}).then(res => {
				this.$toast({
					title: 'Deletion successful'
				});
				this.mescroll.resetUpScroll()
			}).finally(() => {
				this.show = false;
			});
		},
		onAddCart(params) {
			console.log("🚀 ~ file: myFavorite.vue:105 ~ params:", params)
			uni.showLoading();

			productDetail({
				ProId: params.ProId
			}).then(res => {
				this.$refs.addPopupRef.isAddCartShow = true;
				this.$refs.addPopupRef.productImg = params.img;
				this.$refs.addPopupRef.productDetailData = res;
			}).finally(() => {
				uni.hideLoading()
			})
		},
		handleToPage(ProId) {
			this.$tab.navigateTo(`/pages/goodsDetail/goodsDetail?ProId=${ProId}`)
		}
	}
}
</script>

<style lang="scss" scoped>
.favorite-box {
	padding: 23rpx 31rpx;
	box-sizing: border-box;
	display: grid;
  @include flex-gap(31rpx, 0); // 替换了 row-gap

	.favorite-box-item {
		background: #fff;
		border-radius: 31rpx;
		padding: 31rpx;

		.pictrue {
			width: 180rpx;
			height: 180rpx;
			border-radius: 31rpx;

			.easy-loadimage {
				width: 100%;
				height: 100%;
			}
		}

		.option-bottom {
			display: flex;
			justify-content: flex-end;
  @include flex-gap(0, 50rpx); // 替换了 column-gap
		}
	}
}
</style>