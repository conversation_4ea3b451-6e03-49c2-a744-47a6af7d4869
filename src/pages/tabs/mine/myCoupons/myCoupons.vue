<template>
	<view>
		<navbar autoBack isImmersion :scrollTop="scrollTop" />
		<mescroll-body :up="upOptions" :down="downOption" @init="mescrollInit" @down="downCallback" @up="upCallback">
			<view class="coupons-box">
				<u--image src="/static/assets/mine/coupon_bg.png" width="750rpx" height="456rpx"></u--image>
		
				<view class="coupons-list">
					<view class="coupon-item-box" v-for="(item, index) in list" :key="index">
						<view class="coupon-item">
							<view class="coupon-left">
								<text class="sign">{{ priceSymbol }}</text>
								<text class="price">{{ item.Money }}</text>
							</view>
							<view class="coupon-right">
								<view class="coupon-code">
									<u--text :text="`Code: ${ item.CouponNumber }`" color="#915A16" size="35rpx" :lines="1"
										style="font-weight: 500;" margin="0 19rpx 0 0"></u--text>
									<i class="iconfont icon-fuzhi" @click="copyText(item.CouponNumber)"></i>
								</view>
								<view class="coupon-expires">
									<text class="title">Expires: To {{ item.EndTime }}</text>
								</view>
							</view>
		
							<u-icon v-if="item.Desc" class="arrow-down-btn"
								:name="item.showDescription ? 'arrow-up' : 'arrow-down'" color="#CCCCCC"
								@click="handleOpen(index)"></u-icon>
						</view>
		
						<u-transition :show="item.showDescription" mode="fade-down">
							<view class="coupon-detail-box">
								{{ item.Desc }}
							</view>
						</u-transition>
					</view>
				</view>
			</view>
		</mescroll-body>
	</view>
</template>

<script>
	import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
	import {
		getMyCoupon
	} from "@/api/home.js";
	import {
		mapGetters
	} from "vuex";

	export default {
		mixins: [MescrollMixin], // 使用mixin
		data() {
			return {
				list: [],
				scrollTop: 0,
				downOption: {
					native: true
				},
				upOptions: {
					empty: {
						tip: 'No data'
					}
				}
			};
		},
		computed: {
			...mapGetters(['priceSymbol'])
		},
		onPageScroll({ scrollTop }) {
			this.scrollTop = scrollTop;
		},
		methods: {
			upCallback(page) {
				getMyCoupon({
					page: page.num
				}).then(res => {
					console.log(res);
					if (page.num == 1) { //如果是第一页需手动制空列表
						this.list = [];
					}

					this.list = this.list.concat(res.result); //追加新数据
					this.list.forEach(item => {
						this.$set(item, 'showDescription', false);
					});

					setTimeout(() => {
						this.mescroll.endByPage(res.result.length, res.total_pages);
					}, 500)
				}).catch(() => {
					this.mescroll.endErr();
				})
			},
			copyText(text) {
				uni.setClipboardData({
					data: text, // 要复制的文本内容
					success: () => {
						this.$toast({
							title: 'Copy successful'
						});
					},
					fail: () => {
						this.$toast({
							title: 'Copy failed'
						});
					}
				});
			},
			handleOpen(index) {
				this.list[index].showDescription = !this.list[index].showDescription;
			}
		}
	}
</script>

<style lang="scss" scoped>
	.coupons-box {
		.coupons-list {
			padding: 25rpx 31rpx;
			box-sizing: border-box;
			display: grid;
  @include flex-gap(31rpx, 0); // 替换了 row-gap

			.coupon-item-box {
				overflow: hidden;

				.coupon-item {
					padding: 31rpx;
					background: #FFFFFF;
					border-radius: 31rpx;
					display: flex;
					align-items: center;
  @include flex-gap(0, 23rpx); // 替换了 column-gap
					box-sizing: border-box;
					position: relative;
					z-index: 666;

					.coupon-left {
						width: 165rpx;
						height: 165rpx;
						border-radius: 15px;
						background: url(/static/assets/mine/Group.png) no-repeat center center #FFE8D3;
						background-size: 112rpx 112rpx;
						display: flex;
						justify-content: center;
						align-items: center;

						.sign {
							font-weight: 600;
							font-size: 23rpx;
							color: #915A16;
						}

						.price {
							font-weight: 600;
							font-size: 38rpx;
							color: #915A16;
							margin-left: 2rpx;
							margin-bottom: 8rpx;
						}
					}

					.coupon-right {
						flex: 1;
						display: grid;
  @include flex-gap(25rpx, 0); // 替换了 row-gap

						.coupon-code {
							display: flex;
							align-items: center;
							justify-content: space-between;

							.icon-fuzhi {
								color: #915A16;
								font-size: 16px;
								opacity: .5;
							}
						}

						.coupon-expires {
							.title {
								font-weight: bold;
								font-size: 31rpx;
								color: #C8AD8B;
							}
						}
					}

					.arrow-down-btn {
						position: absolute;
						right: 31rpx;
						bottom: 31rpx;
					}
				}

				.coupon-detail-box {
					margin-top: -40rpx;
					padding: 59rpx 19rpx 31rpx;
					font-weight: 400;
					font-size: 27rpx;
					color: rgba(0, 0, 0, 0.5);
					background: #f4f4f4;
					border-radius: 0 0 31rpx 31rpx;
				}
			}
		}
	}
</style>