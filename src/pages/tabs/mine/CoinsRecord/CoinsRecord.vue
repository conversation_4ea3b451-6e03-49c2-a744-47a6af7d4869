<template>
    <view class="coins-record-page">
        <!-- 导航栏 -->
        <navbar autoBack title="Shop" />

        <!-- 标签切换 -->
        <view class="tab-container">
            <view class="tab-item" :class="{ active: currentTab === 'flow' }" @click="switchTab('flow')">
                <text class="tab-text">Earned</text>
            </view>
            <view class="tab-item" :class="{ active: currentTab === 'order' }" @click="switchTab('order')">
                <text class="tab-text">Used</text>
            </view>
        </view>

        <!-- 记录列表 -->
        <mescroll-body :up="upOption" :down="downOptions" @init="mescrollInit" @down="downCallback" @up="upCallback">

            <view class="record-list" v-if="recordList.length">
                <view class="record-item" v-for="(item, index) in recordList" :key="item.GLId">

                    <view class="record-content">
                        <view class="record-info">
                            <text class="record-title">{{ getRecordTitle(item) }}</text>
                            <text class="record-date">{{ item.AccTime }}</text>
                        </view>

                        <view class="record-amount">
                            <text class="amount-text" :class="{
                                'positive': item.Type === '1' && parseFloat(item.Golds) > 0,
                                'negative': item.Type === '2' || parseFloat(item.Golds) < 0
                            }">
                                {{ formatAmount(item.Golds) }}
                            </text>
                        </view>
                    </view>
                </view>
            </view>
        </mescroll-body>
    </view>
</template>

<script>
import { getUserGoldLogs } from '@/api/coins.js'
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js"

export default {
    name: 'CoinsRecord',
    mixins: [MescrollMixin],
    components: {
        // navbar组件会自动注册
    },
    data() {
        return {
            currentTab: 'flow', // 'flow'=获得金币记录 'order'=使用金币记录
            recordList: [],
            isLoading: false,
            downOptions: {
                // use: false
            },
            upOption: {
                page: {
                    num: 0,
                    size: 10
                },
                noMoreSize: 5,
                textNoMore: 'No more data',
                textLoading: 'Loading...',
                empty: {
                    use: true,
                    tip: 'No records found'
                }
            }
        }
    },

    methods: {
        /**
         * 初始化mescroll
         */
        mescrollInit(mescroll) {
            this.mescroll = mescroll
        },

        /**
         * 下拉刷新
         */
        downCallback() {
            this.recordList = []
            this.mescroll.resetUpScroll()
        },

        /**
         * 上拉加载
         */
        upCallback(page) {
            this.loadRecords(page)
        },

        /**
         * 切换标签
         */
        switchTab(tab) {
            if (this.currentTab === tab) return

            this.currentTab = tab
            this.recordList = []

            // 重置滚动加载
            if (this.mescroll) {
                this.mescroll.resetUpScroll()
            }
        },

        /**
         * 加载记录数据
         */
        async loadRecords(page) {
            try {
                this.isLoading = true

                const params = {
                    type: this.currentTab === 'flow' ? '1' : '2',
                    page: page.num
                }

                const res = await getUserGoldLogs(params)

                if (res && res.result) {
                    const newData = res.result

                    if (page.num === 1) {
                        this.recordList = newData
                    } else {
                        this.recordList = [...this.recordList, ...newData]
                    }

                    this.mescroll.endSuccess(newData.length)
                } else {
                    this.mescroll.endErr()
                }

            } catch (error) {
                console.error('加载金币记录失败:', error)
                this.mescroll.endErr()
            } finally {
                this.isLoading = false
            }
        },

        /**
         * 获取记录标题
         */
        getRecordTitle(item) {
            // 优先使用备注信息
            if (item.Remark && item.Remark.trim()) {
                return item.Remark
            }

            // 根据类型和金额判断
            const amount = parseFloat(item.Golds)

            if (item.Type === '1') {
                // 获得金币记录
                if (amount > 0) {
                    return 'Coins earned'
                } else {
                    return 'Coins expired'
                }
            } else {
                // 使用金币记录
                return 'Coins used'
            }
        },

        /**
         * 格式化金额显示
         */
        formatAmount(amount) {
            const num = parseFloat(amount)
            if (num > 0) {
                return `+${Math.abs(num)}`
            } else {
                return `-${Math.abs(num)}`
            }
        }
    }
}
</script>

<style scoped lang="scss">
.coins-record-page {
    background-color: #f8f8f8;
    min-height: 100vh;
}

.tab-container {
    display: flex;
    justify-content: center;
    gap: 20rpx;
    margin: 20rpx 30rpx;
    background: #fff;
    border-radius: 31rpx 31rpx 31rpx 31rpx;
    border: 4rpx solid #fff;
    box-sizing: border-box;

    .tab-item {
        width: 337rpx;
        height: 85rpx;
        border-radius: 29rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        &.active {
            background-color: #FF5A1E;

            .tab-text {
                color: #ffffff;
                font-weight: 600;
            }
        }

        .tab-text {
            font-size: 28rpx;
            color: #8C8C8C;
            transition: all 0.3s ease;
        }
    }
}

.record-list {
    padding: 0;
}

.record-item {
    background-color: #ffffff;
    border-radius: 0;
    margin-bottom: 1rpx;
    padding: 30rpx 20rpx;

    .record-content {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .record-info {
            flex: 1;
            padding-right: 20rpx;

            .record-title {
                display: block;
                font-size: 28rpx;
                color: #262626;
                font-weight: 400;
                margin-bottom: 6rpx;
                line-height: 1.3;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .record-date {
                font-size: 22rpx;
                color: #8C8C8C;
                line-height: 1.2;
            }
        }

        .record-amount {
            flex-shrink: 0;

            .amount-text {
                font-size: 28rpx;
                font-weight: 600;

                &.positive {
                    color: #00C851;
                }

                &.negative {
                    color: #262626;
                }
            }
        }
    }
}

.empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300rpx;

    .empty-text {
        font-size: 26rpx;
        color: #8C8C8C;
    }
}
</style>
