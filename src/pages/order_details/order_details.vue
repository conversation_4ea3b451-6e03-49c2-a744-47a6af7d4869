<template>
	<view>
		<navbar autoBack />
		<u-loading-page :loading="isPageLoading && !isPaymentPayPalLoad" bg-color="#F0F0F0" iconSize="30"
			loadingText="Loading"></u-loading-page>

		<view v-if="!isPageLoading" class="order-details-box">

			<!-- 地址信息 -->
			<AddressInfo ref="addressInfo" @addressChanged="handleAddressChanged" />

			<!-- 物流方式 -->
			<u-cell-group class="common-box-style" :border="false"
				v-if="logisticsData.info && Object.keys(logisticsData.info).length">
				<view style="height:96rpx;display: flex;align-items: center;padding-left:30rpx;">
					<u--text text="Shipping Method" size="31rpx" color="#262626" bold style="width: 260px;"></u--text>
				</view>
				<u-gap height="1" bgColor="#F7F7F7"></u-gap>
				<view v-for="(value, key) in logisticsData.info" :key="key" class="shipping-methods-list">
					<u-cell v-for="(item, index) in value" :key="item.SId" :border="false"
						@click="chooseLogistics(key, item, index)">
						<view slot="title">
							<u--text :text="item.Name" size="29rpx" color="rgba(0, 0, 0, 0.50)" bold></u--text>
						</view>
						<view slot="right-icon">
							<radio :checked="currentLogistics === index" color="#FF5A1E" style="transform:scale(0.7)" />
						</view>
					</u-cell>
				</view>
			</u-cell-group>


			<!-- 商品详情 -->
			<view style=" border-radius: 31rpx;overflow: hidden;background:#fff;" class="u-cell-group">
				<block v-for="item in orderCheckoutData.cart_ary">
					<OrderItem :PicPath="item.PicPath" :Name_en="item.Name_en" :goodsPrice="item.Price" :Qty="item.Qty">
						<template slot="skuInfo">
							<!-- 普通商品Sku -->
							<block v-if="item.BuyType == 0 || item.BuyType == 5 && Object.keys(item.Property).length">
								<view v-for="(value, key) in item.Property" :key="key">
									<u--text :text="`${key}: ${value}`" color="#8C8C8C" size="27rpx"></u--text>
								</view>
							</block>

							<!-- 组合促销 主Sku -->
							<block
								v-if="item.BuyType == '4' && mainSku(item.ProId, item.Property) && Object.keys(item.Property).length">
								<view v-for="(value, key) in mainSku(item.ProId, item.Property)" :key="key">
									<u--text :text="`${key}:${value}`" color="#8C8C8C" size="27rpx"></u--text>
								</view>
							</block>

							<view v-if="item.CardData">
								<u--text :text="`Recipient email: ${item.CardData.card_email}`" color="#8C8C8C"
									size="27rpx"></u--text>
								<u--text :text="`Recipient name: ${item.CardData.card_name}`" color="#8C8C8C"
									size="27rpx"></u--text>
								<u--text :text="`Message: ${item.CardData.card_content}`" color="#8C8C8C"
									size="27rpx"></u--text>
								<u--text :text="`Send on: ${item.CardData.card_time}`" color="#8C8C8C"
									size="27rpx"></u--text>
							</view>
							<view v-if="information">
								<u--text :text="`Recipient email: ${information.card_email}`" color="#8C8C8C"
									size="27rpx"></u--text>
								<u--text :text="`Recipient name: ${information.card_name}`" color="#8C8C8C"
									size="27rpx"></u--text>
								<u--text :text="`Message: ${information.card_content}`" color="#8C8C8C"
									size="27rpx"></u--text>
								<u--text :text="`Send on: ${information.card_time}`" color="#8C8C8C"
									size="27rpx"></u--text>
							</view>
						</template>
						<template slot="footer">
							<block v-if="item.BuyType == '4' && item.package_row.length">
								<block v-for="(cItem, cIndex) in item.package_row" :key="cItem.ProId">
									<OrderItem :PicPath="cItem.PicPath_0" :Name_en="cItem.Name_en" :isPrice="false"
										:goodsPrice="cItem.Price" :isQty="true" Qty="1" style="padding-right: 0;"
										class="package-item-1">
										<template slot="skuInfo">
											<view v-for="(value, key) in mainSku(cItem.ProId, item.Property)"
												:key="key">
												<u--text :text="`${key}: ${value}`" color="#8C8C8C"
													size="27rpx"></u--text>
											</view>
										</template>
									</OrderItem>
								</block>
							</block>
						</template>
					</OrderItem>
				</block>
				<!-- 赠品 -->
				<block v-for="(item, index) in orderCheckoutData.GiftProducts">
					<OrderItem :PicPath="item.PicPath" :Name_en="item.Name_en" :goodsPrice="item.Price" :Qty="item.Qty"
						:isGift="true" v-if="showAllMoreGift ? true : index <= 1">
						<template slot="skuInfo">
							<!-- 普通商品Sku -->
							<block v-if="item.BuyType == 0 && Object.keys(item.Property).length">
								<view v-for="(value, key) in item.Property" :key="key">

									<u--text :text="`${key}: ${value}`" color="#8C8C8C" size="27rpx"></u--text>
								</view>
							</block>
						</template>
					</OrderItem>
				</block>
				<u-gap height="2rpx" bgColor="#F7F7F7" style="margin: 0 31rpx;"></u-gap>
				<view class="more-free-gift-arrow" v-if='orderCheckoutData.GiftProducts.length > 2'
					@click="showAllMoreGift = !showAllMoreGift">More free gift detail <u-icon
						:name="showAllMoreGift ? 'arrow-up' : 'arrow-down'" size="15rpx" color="#8C8C8C"></u-icon>
				</view>

				<u-cell-group class="common-box-style" :border="false" :custom-style="{ 'paddingBottom': '10rpx' }">

					<u-cell :border="false" center :titleStyle="titleStyle" @click="handleOpenCoupon">
						<u-icon slot="icon" size="46rpx" name="/static/assets/mine/coupon.png"></u-icon>
						<u--text slot="title" text="Coupon" color="#262626" size="29rpx" bold></u--text>
						<template slot="value">
							<u--text v-if="selectCouponData.length" :text="`-${priceSymbol}${product.discount.coupon}`"
								color="#8C8C8C" size="27rpx" bold align="right"></u--text>
							<u--text v-else text="Select" color="#8C8C8C" size="27rpx" bold align="right"></u--text>
						</template>
						<u-icon slot="right-icon" name="/static/assets/common/arrow_next.png"></u-icon>
					</u-cell>

					<u-cell :border="false" center title="Coins" :titleStyle="titleStyle" @click="isCoinsShow = true">
						<u-icon slot="icon" size="46rpx" name="/static/assets/mine/coins.png"></u-icon>
						<u--text slot="title" text="Coins" color="#262626" size="29rpx" bold></u--text>
						<u-icon slot="right-icon" name="/static/assets/common/arrow_next.png"></u-icon>
						<template slot="value">
							<u--text v-if="product.discount.goldCoins"
								:text="`-${priceSymbol}${product.discount.goldCoins}`" color="#8C8C8C" size="27rpx" bold
								align="right"></u--text>

							<u--text v-else text="Select" color="#8C8C8C" size="27rpx" bold align="right"></u--text>
						</template>
					</u-cell>

					<u-cell :border="false" center :titleStyle="titleStyle" @click="handleOpenGiftCard">
						<u-icon slot="icon" size="46rpx" name="/static/assets/mine/gift_card.png"></u-icon>
						<u--text slot="title" text="Gift Card" color="#262626" size="29rpx" bold></u--text>
						<template slot="value">
							<u--text v-if="giftCardData.cid" :text="`-${priceSymbol}${product.discount.giftCard}`"
								color="#8C8C8C" size="27rpx" bold align="right"></u--text>

							<u--text v-else text="Select" color="#8C8C8C" size="27rpx" bold align="right"></u--text>
						</template>
						<u-icon slot="right-icon" name="/static/assets/common/arrow_next.png"></u-icon>
					</u-cell>
					<u-cell :border="false" center title="Shipping" :value="`${priceSymbol}${currentShippingPrice}`"
						:titleStyle="titleStyle">
						<u-icon slot="icon" size="46rpx" name="/static/assets/mine/shipping.png"></u-icon>
					</u-cell>
					<u-cell :border="false" center title="Taxes" :value="`${priceSymbol}${calculatedTax}`"
						:titleStyle="titleStyle">
						<u-icon slot="icon" size="46rpx" name="/static/assets/mine/taxes.png"></u-icon>
					</u-cell>
					<u-cell :border="false" center title="Handling Charge"
						:value="`${priceSymbol}${calculatedServiceCharge}`" :titleStyle="titleStyle">
						<u-icon slot="icon" size="46rpx" name="/static/assets/mine/handling_charge.png"></u-icon>
					</u-cell>
				</u-cell-group>
			</view>
			<!-- buy together  -->
			<BuyTogether :recommendAddCart="recommendAddCart" :loading="isRecommendLoading"
				@reload-more-goods="reloadMoreGoods" @change-total-goods-amount="handleAddonPriceUpdate"
				v-if="recommendAddCart" ref='refBuyTogether' />
			<!-- Payment Method -->
			<u-cell-group class="common-box-style" :border="false" :custom-style="{ 'paddingBottom': '10rpx' }">
				<u-cell>
					<u--text slot="title" text="Payment Method" color="rgba(0,0,0,0.8)" size="31rpx" bold></u--text>
				</u-cell>

				<view v-for="(item, index) in payChannelsData" :key="item.PId" @click="choosePayment(item, index)">
					<template>
						<u-cell :border="false">
							<u--image v-if="item.LogoPath" :src="item.LogoPath" slot="icon" width="40px" height="20px"
								mode="aspectFit"></u--image>
							<u--text slot="title" :text="item.Name_en" size="26rpx" bold margin="0 0 0 5px"></u--text>

							<template slot="right-icon">
								<u-icon v-show="currentPaymentIdx === index" name="checkmark-circle-fill"
									color="#FF5A1E" size="38rpx"></u-icon>
							</template>
						</u-cell>

						<u-cell v-if="item.CardImg.length" :border="false">
							<template slot="title">
								<scroll-view scroll-x style="width: 240rpx;">
									<view class="flex" style="margin-right: 5rpx;">
										<view v-for="(cardImg, cardIndex) in item.CardImg" :key="cardIndex">
											<u--image :src="cardImg" width="40px" height="20px"
												mode="aspectFit"></u--image>
										</view>
									</view>
								</scroll-view>
							</template>
							<u--text slot="value" text="Credit or Debit Cards" color="#8C8C8C" size="26rpx"
								bold></u--text>
							<u-icon slot="right-icon" name="/static/assets/common/arrow_next.png"></u-icon>
						</u-cell>
					</template>
				</view>
			</u-cell-group>

			<!-- Billing Address -->
			<u-cell-group class="common-box-style" :border="false" :custom-style="{ 'paddingBottom': '10rpx' }">
				<u-cell>
					<u--text slot="title" text="Billing Address" color="rgba(0,0,0,0.8)" size="31rpx" bold></u--text>
				</u-cell>
				<u-cell :border="false" v-for="(item, index) in billingAddress" :key="index"
					@click="chooseBillingAdd(index)">
					<u--text slot="title" :text="item.title" color="rgba(0,0,0,0.5)" size="29rpx" bold></u--text>

					<template slot="value">
						<u-icon v-show="currentBillingAddIdx === index" name="checkmark-circle-fill" color="#FF5A1E"
							size="38rpx"></u-icon>
					</template>
				</u-cell>
			</u-cell-group>



			<!-- Coupon -->
			<popup-box ref="couponRef" mainTitle="Coupon">
				<template slot="header">
					<view class="flex align-center">
						<u--text @click="couponShowType = couponShowType == 4 ? 0 : 4"
							:text="couponShowType != 4 ? 'Promotion' : 'Normal'" color="#8C8C8C" size="34rpx" bold
							align="left" style="width: 133rpx;padding-left:30rpx"></u--text>
						<u--text :text="couponShowType == 4 ? 'Promotion' : 'Normal'" color="#000" size="38rpx" bold
							align="center"></u--text>
						<u--text style="width:133rpx;padding-left:30rpx"></u--text>
					</view>
				</template>
				<template slot="content">
					<coupon-list :selectCouponCid="selectCouponCid" :selectMultipleCouponCid="selectMultipleCouponCid"
						:couponShowType="couponShowType" :couponData="couponData"
						@handleSelectCoupon="handleSelectCoupon" @handleClearCoupon="handleClearCoupon" />
				</template>
			</popup-box>

			<!-- Gift Card -->
			<u-popup :show="isGiftCardShow" round="15" closeable @close="isGiftCardShow = false">
				<view class="popup-default-box">
					<view class="popup-title">Exchange</view>
					<u--input class="ipt-box" v-model.trim="giftCardCode" placeholder="Please enter gift card code"
						border="none" :placeholderStyle="placeholderStyle" clearable></u--input>

					<view class="flex justify-around align-center" style="margin-top: 250rpx;">
						<SubmitButton btnW="300rpx" @handleConfirm="handleGiftCodeConfirm"></SubmitButton>
						<SubmitButton btnW="300rpx" text="Nonuse" bgColor="#ddd" fontColor="#000"
							@handleConfirm="handleClearGiftCard"></SubmitButton>
					</view>
				</view>
			</u-popup>

			<!-- Coins -->
			<u-popup :show="isCoinsShow" round="15" closeable @close="isCoinsShow = false">
				<view class="coins-box">
					<view class="popup-title">Exchange</view>
					<u-gap height="2rpx" bgColor="#F7F7F7"></u-gap>
					<view class="coins-content-box">
						<view class="total-coins-box flex align-center">
							<text class="total-coins">{{ exchangeCoinData.Gold || 0 }}</text>
							<u--image src="/static/assets/mine/coin.png" width="22rpx" height="22rpx"
								mode="widthFix"></u--image>
						</view>
						<view class="coin-input-box">
							<u--input class="coin-input" v-model.lazy="exchangeCoin" type="number"
								placeholder="Exchange quantity" clearable
								placeholderStyle="color: '#8C8C8C', fontSize: '35rpx'" border="none"
								@input="handleIptCoin"></u--input>

							<view class="deduction-coin">
								<u--text :text="isDeductionCoin" size="30rpx" color="#FF5A1E" align="center"></u--text>
							</view>
						</view>
						<u--text :text="exchangeRatio" size="30rpx" color="#8C8C8C" margin="0 0 50rpx 0"></u--text>
						<view class="flex justify-around align-center">
							<SubmitButton btnW="300rpx" @handleConfirm="handleCoinConfirm"></SubmitButton>
							<SubmitButton btnW="300rpx" text="Nonuse" bgColor="#ddd" fontColor="#000"
								@handleConfirm="handleClearDiscountCoin"></SubmitButton>
						</view>
					</view>
				</view>
			</u-popup>

			<!-- address -->
			<popup-box ref="addressRef" mainTitle="Address">
				<template slot="content">
					<address-list :selectAddAid="selectAddAid" @handleSelectAdd="handleSelectAdd" />
				</template>
			</popup-box>

			<!-- 账单地址 -->
			<popup-box ref="shippingBillAddressRef" mainTitle="Billing Address">
				<template slot="content">
					<address-list :selectAddAid="selectShippingBillAid"
						@handleSelectAdd="handleSelectShippingBillAdd" />
				</template>
			</popup-box>

			<!-- Credit or Debit Cards -->
			<popup-box ref="isCreditDebitRef" mainTitle="Credit or Debit Cards">
				<template slot="content">
					<view class="credit-debit-box">
						<u--input v-model.trim="creditData.IssuingBank" placeholder="Bank Name" border="none"
							:placeholderStyle="creditPlaceholderStyle" clearable></u--input>
						<u--input v-model.trim="creditData.CardNo" placeholder="Card Number" border="none"
							:placeholderStyle="creditPlaceholderStyle" clearable></u--input>
						<u--input v-model.trim="creditData.CardSecurityCode" placeholder="CYC" border="none"
							:placeholderStyle="creditPlaceholderStyle" clearable></u--input>
						<view class="date-box">
							<view class="item" @click.native.stop="handleOpenMM">
								<u--text :text="creditData.CardExpireMonth || 'MM'"
									:color="creditData.CardExpireMonth ? '#262626' : '#8C8C8C'" size="30rpx"
									bold></u--text>
								<u-icon name="arrow-down" color="#CCCCCC"></u-icon>
							</view>
							<view class="item" @click.native.stop="handleOpenYY">
								<u--text :text="creditData.CardExpireYear || 'YY'"
									:color="creditData.CardExpireYear ? '#262626' : '#8C8C8C'" size="30rpx"
									bold></u--text>
								<u-icon name="arrow-down" color="#CCCCCC"></u-icon>
							</view>
						</view>

						<SubmitButton class="credit-btn" @handleConfirm="handleCreditConfirm"></SubmitButton>
					</view>
				</template>
			</popup-box>

			<!-- 月份 -->
			<u-picker class="picker-box" title="MM" cancelText="Cancel" cancelColor="#8C8C8C" confirmText="Confirm"
				confirmColor="#FF5A1E" :show="isCreditMMShow" :columns="[creditMMData]" :defaultIndex="defaultMMIndex"
				closeOnClickOverlay @close="isCreditMMShow = false" @cancel="isCreditMMShow = false"
				@confirm="chooseCreditMM"></u-picker>

			<!-- 年份 -->
			<u-picker class="picker-box" title="YY" cancelText="Cancel" cancelColor="#8C8C8C" confirmText="Confirm"
				confirmColor="#FF5A1E" :show="isCreditYYShow" :columns="[creditYYData]" :defaultIndex="defaultYYIndex"
				closeOnClickOverlay @close="isCreditYYShow = false" @cancel="isCreditYYShow = false"
				@confirm="chooseCreditYY"></u-picker>

			<!-- payment -->
			<PaymentButton :isPaymentLoading="isPaymentLoading" :Price="finalPrice" @handlePayment="handlePayment">
			</PaymentButton>
		</view>

		<view v-if="!isHidePage && isSelectAddress">
			<!-- PayPal button -->
			<PaypalButton :style="{ paddingBottom: safeAreaBottom + 'px', opacity: $config.payment.btnOpacity }"
				class="web-paypal-page" ref="paypalButton" @cancel="onPaymentCancel" @error="onPaymentError"
				@success="onPaymentSuccess" @open-view="onOpenView" :payFetch="handlePayment"
				v-show="showPayPalIframe && !isHidePage" />

			<!-- Stripe button -->
			<view class="stripe-payment" v-show="showStripePayment">
				<StripeButton :clientSecret="stripeOrderInfo.client_secret" :orderInfo="stripeOrderInfo" />
			</view>

			<!-- Amazon Pay button -->
			<amazon-pay-button v-show="showAmazonPay" :payFetch="handlePayment" @open-view="onOpenView"
				:style="{ paddingBottom: safeAreaBottom + 'px' }" @error="onAmazonPayError" @retry="onAmazonPayRetry" />
		</view>
	</view>
</template>

<script>
import {
	getCartCheckout,
	getLogistics,
	createOrder,
	getCoupon,
	useCoupon,
	clearCoupon,
	getRecommendAddCart,
	getPayChannels,
	useCard,
	getGoldInfo
} from '@/api/orderDetails.js';
import {
	mapGetters
} from 'vuex';
import payment from '@/mixins/payment.js';
import BuyTogether from './components/BuyTogether.vue';
import paypal_handle from '@/mixins/paypal_handle.js';
import { PriceCalculator } from '@/pages/goodsDetail/utils/skuManager.js';
import AddressInfo from './components/AddressInfo.vue';
import StripeButton from '@/components/StripeButton/StripeButton.vue'
import AmazonPayButton from '@/components/AmazonPayButton/AmazonPayButton.vue'
import paymentSDKManager from '@/utils/payment/paymentSDKManager.js'
export default {
	mixins: [payment, paypal_handle],
	components: {
		BuyTogether,
		AddressInfo,
		StripeButton,
		AmazonPayButton
	},
	data() {
		return {
			// stripe 支付信息
			stripeOrderInfo: {},
			// Amazon Pay 支付信息
			amazonPayData: {},
			isHidePage: false,
			couponShowType: 0,
			showAllMoreGift: false,
			information: null,
			refBuyTogether: null,
			isPageLoading: true,
			isPaymentPayPalLoad: false,
			isCouponShow: false,
			isGiftCardShow: false,
			isCoinsShow: false,
			isPaymentLoading: false,
			isGiftCardLoading: false,
			isFirstAddressFlag: false,
			giftCardData: {},
			currentLogistics: null,
			currentPaymentIdx: 0,
			currentBillingAddIdx: 0,
			couponCode: "",
			giftCardCode: "",
			// 物流方式
			logisticsData: [],
			// 确认订单数据
			orderCheckoutData: [],
			ProIdData: [], // 商品ID
			otherGoodsProdId: [],
			orderDetails: {},
			userInfoData: {},
			BuyTogetherData: {},
			couponList: [], // 优惠券列表
			recommendAddCart: {}, // 推荐商品
			payChannelsData: [], // 支付方式
			order_shipping_methodData: {}, // 物流方式
			logisticsShipId: '',
			selectAddAid: '',
			selectShippingBillAid: '',
			CouponPrice: '0.00',
			selectCouponPrice: '',
			order_shipping_bill_aid: '',
			couponData: [],
			selectCouponData: [],
			selectMultipleCouponCid: [],
			orderCouponCode: [],
			selectCouponCid: '',
			billingAddress: [{
				title: 'Same as shipping address',
				billType: 1
			},
			{
				title: 'Use a diffierent billing address',
				billType: 2
			}
			],
			exchangeCoin: '',
			deductionCoin: 0,
			exchangeCoinData: {},
			creditData: {
				IssuingBank: '',
				CardNo: '',
				CardSecurityCode: '',
				CardExpireMonth: '',
				CardExpireYear: ''
			},
			isCreditMMShow: false,
			creditMMData: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'],
			isCreditYYShow: false,
			creditYYData: Array.from({
				length: new Date().getFullYear() - 1900 + 1
			}, (_, i) => String(1900 + i)).reverse(),
			defaultMMIndex: [],
			defaultYYIndex: [],
			// 标题样式
			titleStyle: {
				'font-weight': 'bold',
				'font-size': '29rpx',
				'color': '#262626',
				'white-space': 'nowrap'
			},
			placeholderStyle: "color:#8C8C8C; font-size: 35rpx; font-weight:bold",
			creditPlaceholderStyle: 'color:#8C8C8C; font-size: 30rpx; font-weight:bold',
			coinIptTimer: null,
			product: {
				originalPrice: 0,
				discount: {
					coupon: 0,
					goldCoins: 0,
					giftCard: 0
				},
				isCouponUsed: false,
				isGoldCoinsUsed: false,
				isGiftCardUsed: false
			},
			currentTaxRate: 0, // 当前税率
			isRecommendLoading: false, // 推荐商品加载状态
			isHidePage: false,
			paymentCheckTimer: null, // 支付状态检查定时器
			isPaymentInProgress: false, // 支付是否进行中

		};
	},
	onLoad(e) {
		if (!this.isLogin) {
			this.$toast({
				title: '请重新登录'
			})
		}

		if (this.isLogin) {
			this.orderDetails = e;

			this.getPayChannels();

			this.getOrderDetailData();
		}
	},

	mounted() {
		window.addEventListener('message', this.handleIframeMessage);
		// 添加页面可见性监听，处理iOS webview第三方支付返回
		this.addVisibilityListener();
	},
	onHide() {
		this.isHidePage = true;
	},
	onShow() {
		this.isHidePage = false;
		this.reset();

		// 检查支付结果 - 统一处理iOS手势返回和webview场景
		this.checkPaymentResultOnShow();

		uni.$once('handleAddFirstAddress', (newAddBool) => {
			this.getOrderDetailData();
		});
	},
	destroyed() {
		clearTimeout(this.coinIptTimer);
		// 移除页面可见性监听
		this.removeVisibilityListener();
	},
	computed: {
		...mapGetters(['priceSymbol', 'isLogin', 'affirm_public_api_key', 'country_code', 'locale', 'statusBarAndNavHeight', 'safeAreaBottom']),
		// 是否显示PayPal支付方式
		showPayPalIframe() {
			return this.payChannelsData[this.currentPaymentIdx]?.PId == 1;
		},
		// 是否显示Stripe支付方式
		showStripePayment() {
			return this.payChannelsData[this.currentPaymentIdx]?.PId == 86;
		},
		// 是否显示Amazon Pay支付方式
		showAmazonPay() {
			return this.payChannelsData[this.currentPaymentIdx]?.PId == 89;
		},
		//是否选择地址
		isSelectAddress() {
			return this.userInfoData?.AId;
		},
		mainSku() {
			return (proId, property) => {
				const mainSkuData = property[proId];
				if (mainSkuData && Object.keys(mainSkuData).length) {
					return mainSkuData;
				} else {
					return '';
				}
			}
		},
		couponPriceData() {
			return this.selectCouponPrice || this.CouponPrice;
		},
		isDeductionCoin() {
			return `-${this.priceSymbol}${this.deductionCoin}`
		},
		exchangeRatio() {
			return `Exchange ratio${this.exchangeCoinData?.gold_convert}:1`
		},
		// 计算税费
		calculatedTax() {
			const buyTogetherAmount = PriceCalculator.multiply(this.BuyTogetherData.ItemPrice || 0, this.BuyTogetherData.Qty || 0) || 0;
			const goodsAmount = PriceCalculator.add(this.product.originalPrice, buyTogetherAmount) || 0;
			const shippingFee = parseFloat(this.currentShippingPrice);
			const couponDiscount = this.product.isCouponUsed ? this.product.discount.coupon : 0;

			// 税费基数 = 商品金额 + 运费 - 优惠券金额
			const taxBase = PriceCalculator.subtract(
				PriceCalculator.add(goodsAmount, shippingFee),
				couponDiscount
			);

			// 使用当前税率计算税费（确保税率是小数形式）
			let taxRate = this.currentTaxRate || 0;

			// 如果税率大于1，说明是百分比形式，需要除以100
			if (taxRate > 1) {
				taxRate = PriceCalculator.divide(taxRate, 100);
			}

			const taxAmount = PriceCalculator.multiply(taxBase, taxRate);


			// 使用PriceCalculator向上取整到两位小数
			return PriceCalculator.ceilToString(taxAmount, 2);
		},
		// 获取当前选中的运费
		currentShippingPrice() {
			if (!this.order_shipping_methodData || !this.order_shipping_methodData.ShippingPrice) {
				return '0.00';
			}
			// 使用PriceCalculator向上取整到两位小数
			return PriceCalculator.ceilToString(parseFloat(this.order_shipping_methodData.ShippingPrice), 2);
		},
		// 计算手续费
		calculatedServiceCharge() {
			const currentPayment = this.payChannelsData[this.currentPaymentIdx];
			if (!currentPayment) return '0.00';


			const additionalFeePercent = PriceCalculator.divide(parseFloat(currentPayment.AdditionalFee || 0), 100); // 百分比转小数
			const affixPrice = parseFloat(currentPayment.AffixPrice || 0); // 额外费用

			// 如果百分比和额外费用都为0，则不计算手续费
			if (additionalFeePercent === 0 && affixPrice === 0) {
				return '0.00';
			}
			const buyTogetherAmount = PriceCalculator.multiply(this.BuyTogetherData.ItemPrice || 0, this.BuyTogetherData.Qty || 0) || 0;
			const goodsAmount = PriceCalculator.add(this.product.originalPrice, buyTogetherAmount) || 0;
			const shippingFee = parseFloat(this.currentShippingPrice);
			const taxAmount = parseFloat(this.calculatedTax);
			const couponDiscount = this.product.isCouponUsed ? this.product.discount.coupon : 0;
			const giftCardDiscount = this.product.isGiftCardUsed ? this.product.discount.giftCard : 0;

			// 手续费基数 = 商品金额 + 运费 + 税费 - 优惠券 - 礼品卡
			const serviceChargeBase = PriceCalculator.subtract(
				PriceCalculator.subtract(
					PriceCalculator.add(
						PriceCalculator.add(goodsAmount, shippingFee),
						taxAmount
					),
					couponDiscount
				),
				giftCardDiscount
			);

			// 手续费 = 基数 * 百分比 + 额外费用
			const serviceCharge = PriceCalculator.add(
				PriceCalculator.multiply(serviceChargeBase, additionalFeePercent),
				affixPrice
			);
			// 使用PriceCalculator向上取整到两位小数
			return PriceCalculator.ceilToString(serviceCharge, 2);
		},

		finalPrice() {
			const buyTogetherAmount = PriceCalculator.multiply(this.BuyTogetherData.ItemPrice || 0, this.BuyTogetherData.Qty || 0) || 0;
			const goodsAmount = PriceCalculator.add(this.product.originalPrice, buyTogetherAmount) || 0;
			const shippingFee = parseFloat(this.currentShippingPrice);
			const taxAmount = parseFloat(this.calculatedTax);
			const serviceCharge = parseFloat(this.calculatedServiceCharge);
			const couponDiscount = this.product.isCouponUsed ? this.product.discount.coupon : 0;
			const giftCardDiscount = this.product.isGiftCardUsed ? this.product.discount.giftCard : 0;
			const goldCoinsDiscount = this.product.isGoldCoinsUsed ? this.product.discount.goldCoins : 0;

			// 总价 = 商品金额 + 运费 + 税费 + 手续费 - 优惠券 - 礼品卡 - 金币
			const totalPrice = PriceCalculator.subtract(
				PriceCalculator.subtract(
					PriceCalculator.subtract(
						PriceCalculator.add(
							PriceCalculator.add(
								PriceCalculator.add(goodsAmount, shippingFee),
								taxAmount
							),
							serviceCharge
						),
						couponDiscount
					),
					giftCardDiscount
				),
				goldCoinsDiscount
			);

			// 使用PriceCalculator向上取整到两位小数
			return PriceCalculator.ceilToString(Math.max(0, totalPrice), 2);
		}
	},
	watch: {
		isCreditMMShow(bool, old) {
			if (!bool) {
				this.$refs.isCreditDebitRef.isShow = true;
			}
		},
		isCreditYYShow(bool, old) {
			if (!bool) {
				this.$refs.isCreditDebitRef.isShow = true;
			}
		},
		isCoinsShow(bool) {
			if (bool) {
				this.getGoldInfo();
			}
		},
		// 监听支付方式变化，重新计算手续费
		currentPaymentIdx() {
			// 支付方式变化时会自动重新计算finalPrice
		}
	},
	methods: {
		// 在支付前确保对应SDK已加载
		async ensureSDKForPayment(paymentId) {
			console.log(`🚀 确保支付方式 ${paymentId} 的SDK已加载...`);

			try {
				await paymentSDKManager.ensureSDKByPaymentId(paymentId);
				console.log(`✅ 支付方式 ${paymentId} SDK加载完成`);
				return true;
			} catch (error) {
				console.error(`❌ 支付方式 ${paymentId} SDK加载失败:`, error);
				throw error;
			}
		},

		handleAddonPriceUpdate(e) {
			this.BuyTogetherData = e || {}
		},
		reset() {
			this.isPaymentLoading = false;
			this.isPaymentInProgress = false; // 重置支付状态 
			this.$hideLoading();
		},
		getOrderDetailData() {
			// isBuyType   0 购物车结算  1 立即购买
			if (this.orderDetails.isBuyType == 0) {
				const data = {
					CId: this.orderDetails.CIdData
				};
				// 确认订单页面
				this.getOrderCheckoutData(data);
			} else if (this.orderDetails.isBuyType == 1) {
				let {
					paymentData
				} = this.orderDetails;
				paymentData = JSON.parse(paymentData);
				if (paymentData['Data[card_email]']) {
					this.information = {
						card_email: paymentData['Data[card_email]'],
						card_name: paymentData['Data[card_name]'],
						card_content: paymentData['Data[card_content]'],
						card_time: paymentData['Data[card_time]']
					};
				}
				// 确认订单页面
				this.getOrderCheckoutData(paymentData);
			}
		},
		// 获取确认订单数据
		getOrderCheckoutData(data) {
			getCartCheckout(data).then(res => {
				this.orderCheckoutData = res;
				this.product.originalPrice = res?.total_price;

				// 初始化税率
				if (res?.Tax !== undefined) {
					// TaxRate 是税率比例
					let taxRate = parseFloat(res.Tax);
					// 如果税率大于1，说明是百分比形式（如8表示8%），需要除以100
					if (taxRate > 1) {
						taxRate = taxRate / 100;
					}
					this.currentTaxRate = taxRate;
				} else {
					// 没有税率信息时，设为0
					this.currentTaxRate = 0;
				}

				// 获取商品ID
				this.ProIdData = this.orderCheckoutData?.cart_ary.map(item => item.ProId);
				if (this.ProIdData.length) {
					// 推荐加购商品
					this.getRecommendAddCart({
						'ProId[]': this.ProIdData
					})
				}

				this.getDefaultAddress();
			}).finally(() => {
				this.isPageLoading = false;
			})
		},
		getDefaultAddress() {
			// 地址管理现在由 AddressInfo 组件处理
			// 这个方法保留为空，或者可以删除
			// AddressInfo 组件会通过 addressChanged 事件通知地址变化
		},
		// 获取物流方式
		getLogisticsData(data) {
			getLogistics(data).then(res => {
				this.logisticsData = res;
				// 更新税率（如果地址变更返回了新的税率）
				if (res.TaxRate !== undefined) {
					let taxRate = parseFloat(res.TaxRate);
					// 如果税率大于1，说明是百分比形式，需要除以100
					if (taxRate > 1) {
						taxRate = taxRate / 100;
					}
					this.currentTaxRate = taxRate;
				}

				this.handleSelectDefaultLogistics();
			})
		},
		// 默认选择第一条物流方式
		handleSelectDefaultLogistics() {
			const {
				info
			} = this.logisticsData;
			if (Object.keys(info).length) {
				this.currentLogistics = 0;
				this.logisticsShipId = Object.keys(info)[0];
				this.order_shipping_methodData = info[this.logisticsShipId][0];
			}
		},
		// 获取付款通道
		getPayChannels() {
			getPayChannels().then(res => {
				this.payChannelsData = res;
			})
		},
		// 物流方式
		chooseLogistics(shipId, SidData, index) {
			this.currentLogistics = index;
			this.order_shipping_methodData = {};
			this.logisticsShipId = shipId;
			this.order_shipping_methodData = SidData;
		},
		// 选择支付方
		choosePayment(item, index) {
			this.currentPaymentIdx = index;

			if (item.PId == 87) {
				this.$refs.isCreditDebitRef.isShow = true;
				this.creditData.PId = item.PId;
			}
		},
		// 推荐加购商品
		getRecommendAddCart(data) {
			getRecommendAddCart(data).then(res => {
				this.recommendAddCart = res;
			}).finally(() => {
				this.isRecommendLoading = false;
			})
		},
		// 切换加购商品
		reloadMoreGoods() {
			this.isRecommendLoading = true;
			this.getRecommendAddCart({
				'ProId[]': this.ProIdData
			})
		},
		// billing address
		chooseBillingAdd(index) {
			this.currentBillingAddIdx = index;

			if (this.currentBillingAddIdx === 1) {
				this.$refs.shippingBillAddressRef.isShow = true;
			}
		},
		// Coupon
		handleCouponConfirm() {
			if (!this.couponCode) {
				this.$toast({
					title: "The Coupon code cannot be empty"
				});

				return;
			}
		},
		// Gift Code 
		handleOpenGiftCard() {
			this.isGiftCardShow = true;
			this.giftCardCode = '';
		},
		handleGiftCodeConfirm() {
			if (!this.giftCardCode) {
				this.$toast({
					title: "The Gift Card code cannot be empty"
				});

				return;
			}

			this.isGiftCardLoading = true;
			let data = {};
			if (this.orderDetails.isBuyType == 0) {
				data = {
					CId: this.orderDetails.CIdData,
					code: this.giftCardCode
				}
			}

			if (this.orderDetails.isBuyType == 1) {
				data = {
					ProId: this.ProIdData[0],
					code: this.giftCardCode
				}
			}
			useCard(data).then(res => {
				this.giftCardData = res;
				this.isGiftCardShow = false;

				this.product.discount.giftCard = this.giftCardData?.cutprice;
				this.product.isGiftCardUsed = true;

				this.$toast({
					title: "Gift Card applied successfully"
				})
			}).finally(() => {
				this.isGiftCardLoading = false;
			})
		},
		handleClearGiftCard() {
			this.giftCardData = {};
			this.isGiftCardShow = false;
			this.product.discount.giftCard = 0;
			this.product.isGiftCardUsed = false;
		},
		// 使用优惠券
		useCoupon() {
			useCoupon().then(res => {
				console.log(res)
			})
		},
		// 清除优惠券
		clearCoupon() {
			clearCoupon().then(res => {
				console.log(res)
			})
		},
		// 获取支付方式id
		getPayChannelsPid() {
			const pid = this.payChannelsData[this.currentPaymentIdx]?.PId;
			return pid;
		},
		// 获取账单地址方式
		getBillType() {
			const billType = this.billingAddress[this.currentBillingAddIdx]?.billType;
			return billType;
		},
		handleOpenCoupon() {
			const singleCoupon = this.selectCouponData.filter(item => item.CouponWay != 4);
			const multipleCoupon = this.selectCouponData.filter(item => item.CouponWay == 4);
			const multipleCouponCid = multipleCoupon.map(item => item.cid);

			if (singleCoupon.length) {
				this.selectCouponCid = singleCoupon[0].cid;
			} else {
				this.selectCouponCid = null;
			}

			if (multipleCouponCid.length) {
				this.selectMultipleCouponCid = multipleCouponCid;
			} else {
				this.selectMultipleCouponCid = [];
			}

			let data = {}
			if (this.orderDetails.isBuyType == 0) {
				data = {
					CId: this.orderDetails.CIdData
				}
			}
			let {
				paymentData
			} = this.orderDetails;
			paymentData = JSON.parse(paymentData);
			if (this.orderDetails.isBuyType == 1) {
				data = paymentData
			}

			getCoupon(data).then(res => {
				this.$refs.couponRef.isShow = true;
				this.couponData = res;

			})
		},
		handleSelectCoupon(data) {
			const couponPrice = data.reduce((accumulator, currentValue) => {
				let price = 0;
				if (currentValue.type == 0) {
					// 折扣价，计算折扣金额
					const discountRate = PriceCalculator.divide(parseFloat(currentValue.discount), 100); // 转换为小数
					const originalPrice = parseFloat(currentValue.pro_price || 0);
					const discountAmount = PriceCalculator.multiply(originalPrice, PriceCalculator.subtract(1, discountRate));
					price = discountAmount;
				} else {
					// 固定金额优惠
					price = parseFloat(currentValue?.cutprice || 0);
				}
				return PriceCalculator.add(accumulator, price);
			}, 0)
			this.selectCouponData = data;
			this.product.isCouponUsed = true;
			this.product.discount.coupon = couponPrice;

			this.$toast({
				title: 'Coupons applied successfully'
			})
			setTimeout(() => {
				this.$refs.couponRef.isShow = false;
			}, 200)
		},
		handleClearCoupon() {
			this.product.isCouponUsed = false;
			this.product.discount.coupon = 0;
			this.selectCouponData = [];

			setTimeout(() => {
				this.$refs.couponRef.isShow = false;
			}, 200)
		},
		getGoldInfo() {
			getGoldInfo().then(res => {
				this.exchangeCoinData = res;
			})
		},
		handleIptCoin(event) {
			if (this.coinIptTimer) {
				clearTimeout(this.coinIptTimer)
			}

			this.coinIptTimer = setTimeout(() => {
				let value = event;
				const {
					Gold,
					gold_use_max
				} = this.exchangeCoinData;

				value = value.replace(/[^\d]/g, '');

				if (/^0/.test(value)) {
					value = 1;
				}

				if (value < 0) {
					value = 1;
				}

				if (gold_use_max == 0) {
					if (value > Gold) {
						value = Gold;
					}
				} else {
					if (value > gold_use_max) {
						value = gold_use_max;
					}
				}

				this.exchangeCoin = String(value);
				this.deductionCoin = PriceCalculator.ceilToString(PriceCalculator.divide(value, this.exchangeCoinData?.gold_convert || 1), 2);
			}, 200)
		},
		handleCoinConfirm() {
			const {
				Gold,
				gold_use_max
			} = this.exchangeCoinData;

			if (this.exchangeCoin == '') {
				return this.$toast({
					title: 'please enter the number of coins'
				})
			}

			this.isCoinsShow = false;
			this.product.discount.goldCoins = +this.deductionCoin;
			this.product.isGoldCoinsUsed = true;
			this.$toast({
				title: 'Gold  Coins applied successfully'
			})
		},
		handleClearDiscountCoin() {
			this.deductionCoin = 0;
			this.exchangeCoin = '';
			this.isCoinsShow = false;

			this.product.discount.goldCoins = 0
			this.product.isGoldCoinsUsed = false;
		},
		handleSelectAdd(data) {
			this.userInfoData = data;

			// 地址变更后重新获取物流数据和运费，这会同时更新税率
			if (this.userInfoData?.AId) {
				if (this.orderDetails.isBuyType == 0) {
					const logisticsData = {
						CId: this.userInfoData?.CId, // 国家ID
						order_cid: this.orderDetails.CIdData, // 购物车ID
						AId: this.userInfoData?.AId // 收货地址ID
					}
					this.getLogisticsData(logisticsData);
				}

				if (this.orderDetails.isBuyType == 1) {
					let {
						paymentData
					} = this.orderDetails;
					paymentData = JSON.parse(uni.getStorageSync('paymentData'));

					const logisticsData = {
						CId: this.userInfoData?.CId, // 国家ID
						AId: this.userInfoData?.AId, // 收货地址ID
						...paymentData
					}
					this.getLogisticsData(logisticsData);
				}
			}

			setTimeout(() => {
				this.$refs.addressRef.isShow = false;
			}, 200);
		},
		handleSelectShippingBillAdd(data) {
			this.order_shipping_bill_aid = data?.AId;
			this.selectShippingBillAid = data?.AId; // 新增：同步回显勾选

			setTimeout(() => {
				this.$refs.shippingBillAddressRef.isShow = false;
			}, 200);
		},
		// 处理地址变化
		handleAddressChanged(address) {
			this.userInfoData = address;

			// 地址变更后重新获取物流数据和运费，这会同时更新税率
			if (this.userInfoData?.AId) {
				if (this.orderDetails.isBuyType == 0) {
					const logisticsData = {
						CId: this.userInfoData?.CId, // 国家ID
						order_cid: this.orderDetails.CIdData, // 购物车ID
						AId: this.userInfoData?.AId // 收货地址ID
					}
					this.getLogisticsData(logisticsData);
				}

				if (this.orderDetails.isBuyType == 1) {
					let {
						paymentData
					} = this.orderDetails;
					paymentData = JSON.parse(uni.getStorageSync('paymentData'));

					const logisticsData = {
						CId: this.userInfoData?.CId, // 国家ID
						AId: this.userInfoData?.AId, // 收货地址ID
						...paymentData
					}
					this.getLogisticsData(logisticsData);
				}
			}
		},
		handleOpenMM() {
			this.isCreditMMShow = true;
			this.$refs.isCreditDebitRef.isShow = false;
		},
		chooseCreditMM(MM) {
			this.creditData.CardExpireMonth = MM?.value[0];
			this.isCreditMMShow = false;
		},
		handleOpenYY() {
			this.isCreditYYShow = true;
			this.$refs.isCreditDebitRef.isShow = false;
		},
		chooseCreditYY(YY) {
			this.creditData.CardExpireYear = YY?.value[0];
			this.isCreditYYShow = false;
		},
		handleCreditConfirm() {
			const {
				IssuingBank,
				CardNo,
				CardSecurityCode,
				CardExpireMonth,
				CardExpireYear
			} = this.creditData;

			if (IssuingBank == '') {
				return this.$toast({
					title: 'Please select the issuing bank'
				})
			}
			if (CardNo == '') {
				return this.$toast({
					title: 'Please enter the card number'
				})
			}
			if (CardSecurityCode == '') {
				return this.$toast({
					title: 'Please enter the card security code'
				})
			}
			if (CardExpireMonth == '') {
				return this.$toast({
					title: 'Please select the card expiration month'
				})
			}
			if (CardExpireYear == '') {
				return this.$toast({
					title: 'Please select the card expiration year'
				})
			}

			this.$refs.isCreditDebitRef.isShow = false;
		},
		// Payment
		async handlePayment() {
			try {

				if (!this.userInfoData.AId) {
					this.$toast({
						title: 'Please select a address'
					})
					return false;
				}

				if (this.currentLogistics === null) {
					this.$toast({
						title: 'Please select the shipping method'
					})
					return false;
				}

				if (this.getBillType() == 2) {
					this.$toast({
						title: 'Please select the billing address'
					})
					return false;
				}

				this.isPaymentLoading = true;
				this.isPaymentInProgress = true; // 标记支付开始
				this.$showLoading();

				// 在支付开始前确保对应SDK已加载
				const currentPaymentId = this.getPayChannelsPid();
				try {
					await this.ensureSDKForPayment(currentPaymentId);
				} catch (error) {
					this.$toast({
						title: 'Payment system loading failed, please try again',
						icon: 'none'
					});
					this.reset();
					return false;
				}

				let data = {};
				const orderCouponCode = this.selectCouponData.map(item => item.coupon);

				data = {
					order_coupon_code: orderCouponCode, // 优惠券的code
					order_card_code: this.giftCardData?.coupon, // 卡券码
					order_discount_price: this.product.discount?.goldCoins,
					order_shipping_address_aid: this.userInfoData?.AId, //  收货地址id 
					order_shipping_method_sid: JSON.stringify({
						[this.logisticsShipId]: this.order_shipping_methodData?.SId
					}), // 物流id对应 
					order_shipping_method_type: JSON.stringify({
						[this.logisticsShipId]: this.order_shipping_methodData?.type
					}), // 海运或者空运
					order_shipping_price: JSON.stringify({
						[this.logisticsShipId]: this.logisticsData?.IsInsurance
					}),
					order_shipping_oversea: this.logisticsShipId,
					order_payment_method_pid: this.getPayChannelsPid(),
					billtype: this.getBillType(),
					order_shipping_bill_aid: this.getBillType() == 2 ? this.order_shipping_bill_aid : ''
				}
				// 购物车
				if (this.orderDetails.isBuyType == 0) {
					const cartTypeData = {
						order_cid: this.orderDetails?.CIdData
					}
					data = {
						...data,
						...cartTypeData
					}
				}

				// 立即下单
				if (this.orderDetails.isBuyType == 1) {

					let {
						paymentData
					} = this.orderDetails;
					paymentData = JSON.parse(paymentData);
					//合并信息,赠送礼品卡信息
					if (paymentData['Data[GPId]']) {
						data['goldtype'] = 2; // 兑换
					}
					data = {
						...data,
						...paymentData
					}
				}
				const refBuyTogether = this.$refs?.refBuyTogether
				if (refBuyTogether && refBuyTogether.otherGoodsProdId.length) {
					let { chekData } = refBuyTogether
					data['recommendData[ProId]'] = chekData.ProId;
					data['recommendData[Qty]'] = chekData.Qty;
					data['recommendData[Attr]'] = chekData.Attr ? JSON.stringify(chekData.Attr) : undefined;
				}
				if (this.getPayChannelsPid() == 87) {
					data['authorized[IssuingBank]'] = this.$rsaEncrypt(this.creditData.IssuingBank);
					data['authorized[CardNo]'] = this.$rsaEncrypt(this.creditData.CardNo);
					data['authorized[CardSecurityCode]'] = this.$rsaEncrypt(this.creditData
						.CardSecurityCode);
					data['authorized[CardExpireMonth]'] = this.$rsaEncrypt(this.creditData
						.CardExpireMonth);
					data['authorized[CardExpireYear]'] = this.$rsaEncrypt(this.creditData.CardExpireYear);
				}

				// Create the order
				setTimeout(() => {
					this.onOpenView();
				}, 100);
				const orderResult = await createOrder(data);
				const paramsString = await this.paymentCommonCreateFn(orderResult);
				if (orderResult) {
					uni.setStorage({
						key: 'paymentOrderId',
						data: orderResult.OId
					});
				}
				// Stripe 支付
				if (this.getPayChannelsPid() == 86) {
					this.stripeOrderInfo = paramsString;
					this.reset()
					return
				}

				// Amazon Pay 支付set
				if (this.getPayChannelsPid() == 89) {
					console.log('🔍 Amazon Pay 原始数据类型:', typeof paramsString);
					// 确保返回正确的数据格式
					this.amazonPayData = paramsString;
					return paramsString;
				}
				// Extract payment number from the params
				const params = new URLSearchParams(paramsString);
				this.payData = orderResult;
				this.payData.paymentNumber = params.get('paymentNumber');

				// Store order data for PayPal component to access
				this.orderData = {
					orderNo: params.get('paymentNumber'),
					orderDetails: orderResult
				};
				this.reset()
				return this.payData.paymentNumber;
			} catch (err) {
				console.error('Payment error:', err);
				uni.showToast({
					title: 'Payment preparation failed',
					icon: 'none'
				});
				this.reset();
			} finally {

			}
		},

		// Amazon Pay 错误处理
		onAmazonPayError(error) {
			console.error('❌ Amazon Pay错误:', error);
			this.$toast({
				title: 'Amazon Pay payment failed',
				icon: 'none'
			});
			this.reset();
		},

		// Amazon Pay 重试处理
		onAmazonPayRetry() {
			console.log('🔄 Amazon Pay重试');
		},

		// onShow时检查支付结果 - 统一处理各种场景
		checkPaymentResultOnShow() {
			uni.getStorage({
				key: 'paymentOrderId',
				success: (res) => {
					console.log("🚀 ~ onShow检查支付结果 ~ res:", res);
					if (res.data) {
						uni.removeStorage({
							key: 'paymentOrderId'
						});
						this.$tab.redirectTo(`/pages/tabs/mine/OrderStatus/OrderStatus?OrderId=${res.data}`);
					}
				}
			});
		},

		// 添加页面可见性监听
		addVisibilityListener() {
			// #ifdef H5
			if (typeof document !== 'undefined') {
				this.handleVisibilityChange = this.handleVisibilityChange.bind(this);
				document.addEventListener('visibilitychange', this.handleVisibilityChange);

				// 兼容不同浏览器
				document.addEventListener('webkitvisibilitychange', this.handleVisibilityChange);
				window.addEventListener('focus', this.handleVisibilityChange);
				window.addEventListener('blur', this.handleVisibilityChange);
			}
			// #endif
		},

		// 移除页面可见性监听
		removeVisibilityListener() {
			// #ifdef H5
			if (typeof document !== 'undefined' && this.handleVisibilityChange) {
				document.removeEventListener('visibilitychange', this.handleVisibilityChange);
				document.removeEventListener('webkitvisibilitychange', this.handleVisibilityChange);
				window.removeEventListener('focus', this.handleVisibilityChange);
				window.removeEventListener('blur', this.handleVisibilityChange);
			}
			// #endif
		},

		// 处理页面可见性变化
		handleVisibilityChange() {
			// #ifdef H5
			const isVisible = !document.hidden && document.visibilityState === 'visible';

			console.log('🔍 页面可见性变化:', isVisible, '支付进行中:', this.isPaymentInProgress);

			// 当页面重新变为可见且之前有支付在进行中时，检查支付结果
			if (isVisible && this.isPaymentInProgress) {
				console.log('⚡ 检测到iOS第三方支付返回，开始检查支付结果');

				// 延迟检查，确保支付结果已写入存储
				setTimeout(() => {
					this.checkPaymentResultAfterReturn();
				}, 300);
			}
			// #endif
		},

		// 支付返回后检查结果
		checkPaymentResultAfterReturn() {
			uni.getStorage({
				key: 'paymentOrderId',
				success: (res) => {
					console.log("🚀 ~ webview支付返回检查 ~ res:", res);
					if (res.data) {
						this.isPaymentInProgress = false; // 重置支付状态
						uni.removeStorage({
							key: 'paymentOrderId'
						});
						this.$tab.redirectTo(`/pages/tabs/mine/OrderStatus/OrderStatus?OrderId=${res.data}`);
					} else {
						// 如果没有找到支付结果，启动轮询检查
						this.startPaymentResultPolling();
					}
				},
				fail: () => {
					// 存储读取失败，启动轮询检查
					this.startPaymentResultPolling();
				}
			});
		},

		// 启动支付结果轮询（用于iOS webview场景）
		startPaymentResultPolling() {
			if (this.paymentCheckTimer) {
				clearInterval(this.paymentCheckTimer);
			}

			let checkCount = 0;
			const maxChecks = 10; // 最多检查10次（约30秒）

			console.log('🔄 启动支付结果轮询检查');

			this.paymentCheckTimer = setInterval(() => {
				checkCount++;

				uni.getStorage({
					key: 'paymentOrderId',
					success: (res) => {
						if (res.data) {
							console.log('✅ 轮询检查到支付结果:', res.data);
							clearInterval(this.paymentCheckTimer);
							this.paymentCheckTimer = null;
							this.isPaymentInProgress = false;

							uni.removeStorage({
								key: 'paymentOrderId'
							});
							this.$tab.redirectTo(`/pages/tabs/mine/OrderStatus/OrderStatus?OrderId=${res.data}`);
							return;
						}
					}
				});

				// 达到最大检查次数，停止轮询
				if (checkCount >= maxChecks) {
					clearInterval(this.paymentCheckTimer);
					this.paymentCheckTimer = null;
					this.isPaymentInProgress = false;
					console.log('⏰ 支付结果轮询超时，停止检查');
				}
			}, 3000); // 每3秒检查一次
		}

	},
}
</script>

<style lang="scss" scoped>
.order-details-box {
	display: grid;
	// @include flex-gap(31rpx, 0); // 替换了 row-gap
	padding: 23rpx 31rpx 100rpx;
	box-sizing: border-box;

	>.u-cell-group {
		margin-bottom: 31rpx;
	}

	.package-item-1 {
		::v-deep .u-text {
			&.price-text {
				// display: none!important;
			}
		}
	}

	.address-box {
		overflow: hidden;
		font-family: PingFang SC-Bold !important;

		.u-cell {
			height: 160rpx;

			::v-deep .u-cell__body {
				height: 100%;
			}
		}
	}

	.shipping-methods {
		padding: 31rpx;
		box-sizing: border-box;
	}

	.exchange-btn {
		width: 173rpx;
		height: 42rpx;
		line-height: 42rpx;
		background: linear-gradient(276deg, #FF793D 0%, #FF8F5D 100%);
		border-radius: 15rpx;
		font-weight: bold;
		font-size: 27rpx;
		color: #FFFFFF;
		text-align: center;
	}



	.popup-default-box {
		padding: 31rpx;
		box-sizing: border-box;
	}

	.popup-title {
		font-weight: bold;
		font-size: 38rpx;
		color: rgba(0, 0, 0, 0.8);
		text-align: center;
		margin-bottom: 31rpx;
	}

	.ipt-box {
		background: #F0F0F0;
		border-radius: 31rpx;
		padding: 33rpx 31rpx !important;
		font-size: 35rpx;
	}

	.credit-debit-box {
		display: flex;
		flex-direction: column;
		@include flex-gap(30rpx, 0); // 替换了 row-gap

		.u-input {
			background: #F0F0F0;
			border-radius: 31rpx;
			padding: 20rpx 31rpx !important;
			font-size: 35rpx;
			font-weight: bold;
			color: #262626;
		}

		.date-box {
			display: flex;
			justify-content: space-between;
			align-items: center;
			@include flex-gap(0, 42rpx); // 替换了 column-gap

			.item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				flex: 1;
				background: #F0F0F0;
				border-radius: 31rpx;
				padding: 20rpx 31rpx !important;
				font-size: 35rpx;
			}
		}

		.credit-btn {
			margin-top: 110rpx;
		}
	}

	.coins-box {
		.popup-title {
			padding: 31rpx 0 0;
		}

		.coins-content-box {
			padding: 0 31rpx 31rpx;

			.total-coins-box {
				margin-top: 46rpx;

				.total-coins {
					font-weight: bold;
					font-size: 46rpx;
					color: #FF5A1E;
					margin-right: 6rpx;
				}
			}

			.coin-input-box {
				margin: 50rpx auto 48rpx;
				height: 112rpx;
				border-radius: 30rpx;
				padding: 15rpx 15rpx 15rpx 30rpx;
				background: #F0F0F0;
				display: flex;
				justify-content: space-between;
				align-items: center;
				box-sizing: border-box;
				@include flex-gap(0, 10px); // 替换了 column-gap

				.coin-input {}

				.deduction-coin {
					min-width: 137rpx;
					max-width: 180rpx;
					height: 83rpx;
					padding: 0 8px;
					border-radius: 30rpx;
					box-sizing: border-box;
					border: 2rpx solid rgba(0, 0, 0, 0.1);
					display: flex;
					justify-content: space-between;
					align-items: center;
				}
			}
		}
	}

	.picker-box {
		::v-deep {
			.u-popup__content {
				border-radius: 30rpx 30rpx 0px 0px;

				.u-toolbar {
					border-bottom: 1px solid #E7E7E7;

					.u-toolbar__wrapper__cancel,
					.u-toolbar__wrapper__confirm {
						font-size: 30rpx;
						font-weight: bold;
					}

					.u-toolbar__title {
						font-size: 38rpx;
						color: #262626;
						font-weight: bold;
					}
				}
			}
		}
	}
}

.web-paypal-page {
	position: fixed;
	bottom: -10rpx;
	right: 33rpx;
	z-index: 150;
	height: 62px;
	width: 50vw;
}

.more-free-gift-arrow {
	display: flex;
	align-items: center;
	font-size: 26rpx;
	color: #8C8C8C;
	padding: 17rpx 30rpx;
	font-family: PingFang SC-Regular;

	.u-icon {
		margin-left: 16rpx;
	}
}

.price-breakdown {
	.u-cell {
		padding: 25rpx 31rpx;

		&:first-child {
			padding-bottom: 10rpx;
		}

		&:last-child {
			padding-top: 15rpx;
			background-color: #F9F9F9;
		}
	}
}

.shipping-methods-list {
	padding-bottom: 30rpx;

	::v-deep .u-cell__body {
		padding-top: 30rpx;
		padding-bottom: 0;
	}
}
</style>