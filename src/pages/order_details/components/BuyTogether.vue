<template>
	<view class="buy-together-box common-box-style PingFang-SC-Regular" v-if="Object.keys(recommendAddCart).length">
		<view class="buy-top">
			<u--text text="Buy Together" color="#262626" size="28rpx" bold></u--text>
			<view class="reload-icon" :class="{ 'loading': loading }" @click="!loading && reloadMoreGoods()">
				<u-loading-icon v-if="loading" color="#fff" size="20"></u-loading-icon>
				<u-icon v-else name="reload" color="#fff" size="20" bold></u-icon>
			</view>
		</view>
		<view class="product-card">
			<u--image :src="recommendAddCart.PicPath_0" width="181rpx" height="104rpx" radius="10rpx" mode="aspectFit"
				@click="handleAddToCart" style="min-width: 30%;"></u--image>
			<view class="product-details" @click="handleAddToCart">
				<view class="product-name ">
					<view style="flex:1;display: flex;">
						<u--text :text="recommendAddCart.Name_en" lines="1" color="#262626" size="28rpx" bold></u--text>
					</view>
					<!-- <span style="color: #FF5A1E;font-size: 28rpx;font-weight: bold;" v-if="checkData.ItemPrice">{{priceSymbol+checkData.ItemPrice}}</span> -->
				</view>
				<uni-section class="uni-section-box" :title="item.Name_en" titleFontSize="16" titleColor="#8C8C8C "
					v-for="(item, indexW) in skuData.attr" :key="indexW">
					<view class="version-list">
						<block v-for="(cItem, indexN) in item.children" :key="indexN">
							<view :class="['tagItem', { 'on': cItem.selected === 1 }]" v-if='cItem.selected === 1'>
								:{{ cItem.name }}
							</view>
						</block>
					</view>
				</uni-section>
			</view>

			<view class="product-select">
				<u-checkbox-group v-model="otherGoodsProdId">
					<u-checkbox :name="recommendAddCart.ProId" shape="circle" inactiveColor="#FF5A1E"
						activeColor="#FF5A1E" size="42rpx" @change="handleCheckboxChange"></u-checkbox>
				</u-checkbox-group>
			</view>
		</view>
		<!-- Specification Popup -->
		<addPopup ref="addPopupRef" :isCart="false" :initSelectedSku="selectedSku" @config="handleConfigureChange"></addPopup>
	</view>
</template>

<script>
import {
	mapGetters
} from 'vuex';
import {
	productDetail,
} from '@/api/home.js';
export default {
	props: {
		recommendAddCart: {},
		loading: false
	},
	data() {
		return {
			addPopupRef: null,
			otherGoodsProdId: [],
			skuData: {},
			selectedSku: {},
			checkData: {}
		};
	},
	watch: {
		// 监听recommendAddCart变化，当商品更新时重新加载规格
		'recommendAddCart.ProId': function (newVal) {
			if (newVal) {
				this.skuData = {}
				this.selectedSku = {}
				this.otherGoodsProdId = []
				this.checkData = {}
				this.$emit('change-total-goods-amount', null);
			}
		}
	},
	computed: {
		...mapGetters(['priceSymbol', 'cartNum']),
		// 检查产品是否有规格
		hasSpecifications() {
			return this.recommendAddCart.attribute &&
				Object.keys(this.recommendAddCart.attribute).length > 0;
		},

	},
	methods: {
		// 处理复选框选择
		handleCheckboxChange(e) {
			if (e && this.hasSpecifications) {
				if (this.hasSpecifications) {
					this.handleAddToCart();
					setTimeout(() => {
						this.otherGoodsProdId = []
					})
				} else {
					this.checkData = {
						ProId: this.recommendAddCart.ProId,
						Qty: 1,
					}
				}

			} else {
				this.skuData = {}
				this.selectedSku = {}
				this.checkData = {}
				this.$emit('change-total-goods-amount', null);
			}
		},
		// 处理点击产品弹窗规格属性
		handleAddToCart() {
			productDetail({
				ProId: this.recommendAddCart.ProId
			}).then(res => {
				this.$refs.addPopupRef.isAddCartShow = true;
				this.$refs.addPopupRef.productImg = this.recommendAddCart.PicPath_0;
				this.$refs.addPopupRef.productDetailData = res;
			}).finally(() => {
				uni.hideLoading()
			})
		},
		// 重新加载更多商品
		reloadMoreGoods() {
			// 向父组件发出事件，重新加载推荐产品
			this.$emit('reload-more-goods',null);
		},
		handleConfigureChange(e) {
			let {
				skuData,
				selectedSku
			} = e
			this.selectedSku = selectedSku
			this.skuData = skuData
			this.otherGoodsProdId = [this.recommendAddCart.ProId]
			this.checkData = e
			const totalGoodsAmount = this.recommendAddCart.Price + this.recommendAddCart.MarketPrice;
			this.$emit('change-total-goods-amount', e);
		}
	}
};
</script>

<style lang="scss" scoped>
// 其他商品
.buy-together-box {
	padding: 25rpx 30rpx;
	box-sizing: border-box;
	background-color: #fff;

	.buy-top {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;

		.reload-icon {
			width: 60rpx;
			height: 60rpx;
			background: #FF5A1E;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: opacity 0.3s ease;
			cursor: pointer;

			&.loading {
				opacity: 0.7;
				cursor: not-allowed;
			}
		}
	}

	.product-card {
		display: flex;
		align-items: center;
		border-radius: 20rpx;

		.product-details {
			flex: 1;
			margin-left: 20rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;

			.product-name {
				margin-bottom: 8rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				flex:1;
			}

			.product-model {
				margin-bottom: 6rpx;
			}

			.product-attributes {
				display: flex;
				flex-direction: column;
			}
		}

		.product-select {
			margin-left: 15rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			::v-deep .u-checkbox__icon-wrap {
				margin-right: 0;
				border: 2rpx solid #FF5A1E;
			}
		}
	}
}

// 规格选择弹窗
.spec-popup-container {
	padding: 30rpx;

	.spec-popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-bottom: 20rpx;
		border-bottom: 1px solid #f5f5f5;
		margin-bottom: 20rpx;
	}

	.product-info {
		display: flex;
		padding-bottom: 30rpx;
		margin-bottom: 20rpx;

		.product-details {
			flex: 1;
			margin-left: 20rpx;

			.price-info {
				margin-top: 10rpx;
				color: #FF5A1E;
			}
		}
	}

	.spec-popup-content {
		padding: 20rpx 0;

		.spec-item {
			margin-bottom: 30rpx;

			.spec-values {
				display: flex;
				flex-wrap: wrap;

				.spec-value-item {
					padding: 10rpx 20rpx;
					background-color: #f5f5f5;
					border-radius: 10rpx;
					margin-right: 20rpx;
					margin-bottom: 20rpx;
					font-size: 26rpx;
					color: #666;
					display: flex;
					align-items: center;

					.spec-image {
						width: 40rpx;
						height: 40rpx;
						margin-right: 10rpx;
						border-radius: 4rpx;
					}

					&.active {
						background-color: #FF5A1E;
						color: #fff;
					}
				}
			}
		}
	}

	.spec-popup-footer {
		margin-top: 30rpx;
	}


}

.uni-section-box {
	margin-bottom: 31rpx;
	display: flex;
	color: #8C8C8C;

	::v-deep .uni-section-header {
		padding: 0 !important;
		font-weight: bold;

	}

	.version-list {
		.tagItem {
			font-size: 29rpx;
			color: #8C8C8C;
		}
	}
}
</style>