<template>

    <!-- 地址信息 -->
    <view class="address-box common-box-style">
        <!-- 已选择地址 -->
        <view v-if="currentAddress && currentAddress.AId" class="address-item" @click="handleSelectAddress">
            <view class="address-icon">
                <u-icon size="52rpx" name="/static/assets/mine/orderAddress.png"></u-icon>
            </view>
            <view class="address-content">
                <view class="address-title">
                    <text class="address-name">{{ currentAddress.FirstName || '' }} {{ currentAddress.PhoneNumber || ''
                    }}</text>
                </view>
                <view class="address-detail">
                    <text class="address-location">{{ formatAddressLocation(currentAddress) }}</text>
                </view>
            </view>
            <view class="address-arrow">
                <u-icon name="/static/assets/common/arrow_next.png"></u-icon>
            </view>
        </view>

        <!-- 未选择地址 -->
        <view v-else class="address-item address-item--empty" @click="handleSelectAddress">
            <view class="address-icon">
                <u-icon size="52rpx" name="/static/assets/mine/orderAddress.png"></u-icon>
            </view>
            <view class="address-content">
                <view class="address-title">
                    <text class="address-placeholder">Please select a address</text>
                </view>
            </view>
            <view class="address-arrow">
                <u-icon name="/static/assets/common/arrow_next.png"></u-icon>
            </view>
        </view>
    </view>

</template>

<script>
import { userAddress } from '@/api/address.js';

export default {
    name: 'AddressInfo',
    data() {
        return {
            addressList: [],
            currentAddress: {}
        }
    },
    created() {
        this.loadAddressList();
    },
    mounted() {
        // 监听地址选择事件
        console.log('🔧 设置 addressSelected 事件监听器');
        uni.$on('addressSelected', this.handleDirectAddressSelected);
    },
    destroyed() {
        uni.$off('addressSelected', this.handleDirectAddressSelected);
    },

    methods: {
        // 格式化地址显示
        formatAddressLocation(address) {
            if (!address) return '';
            const parts = [
                address.Country,
                address.StateName || address.State,
                address.City
            ].filter(Boolean);
            return parts.join(', ');
        },

        // 加载地址列表
        loadAddressList() {
            userAddress({
                page: 1,
                size: 100,
                type: 0
            }).then(res => {
                this.addressList = res.result || [];
                this.setDefaultAddress();
            }).catch(err => {
                console.error('加载地址列表失败:', err);
            });
        },
        // 直接处理从 addressBook 返回的地址选择
        handleDirectAddressSelected(selectedAddress) {
            console.log('🎯 order_details 接收到地址选择:', selectedAddress);
            // 更新 AddressInfo 组件的显示
            this.currentAddress = selectedAddress;
            this.$emit('addressChanged', selectedAddress);
        },
        // 设置默认地址
        setDefaultAddress() {
            if (!this.addressList.length) {
                this.currentAddress = {};
                this.$emit('addressChanged', this.currentAddress);
                return;
            }

            // 查找默认地址
            let defaultAddress = this.addressList.find(item => item.IsDefault == 1);

            // 如果没有默认地址，选择第一个
            if (!defaultAddress) {
                defaultAddress = this.addressList[0];
            }
            this.currentAddress = Object.assign({}, defaultAddress);

            // 通知父组件地址已选择
            this.$emit('addressChanged', this.currentAddress);
        },
        // 处理地址选择点击
        handleSelectAddress() {
            // 跳转到地址选择页面，传递当前选中的地址ID
            const currentAId = this.currentAddress.AId || '';
            this.$tab.navigateTo(`/pages/tabs/cart/addressBook/addressBook?fromOrderDetail=1&currentAId=${currentAId}`);
        },
    }
}
</script>

<style lang="scss" scoped>
.address-box {
    overflow: hidden;
    font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    margin-bottom: 31rpx;

    .address-item {
        display: flex;
        align-items: center;
        min-height: 183rpx;
        padding: 20rpx 30rpx;
        background: #fff;
        border-radius: 31rpx;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;


        .address-icon {
            flex-shrink: 0;
            margin-right: 24rpx;
            display: flex;
            align-items: center;
        }

        .address-content {
            flex: 1;
            min-width: 0;
            /* 防止文本溢出 */

            .address-title {
                margin-bottom: 8rpx;

                .address-name {
                    font-size: 28rpx;
                    color: #262626;
                    font-weight: bold;
                    line-height: 1.4;
                }

                .address-placeholder {
                    font-size: 29rpx;
                    color: #262626;
                    font-weight: bold;
                    line-height: 1.4;
                }
            }

            .address-detail {
                .address-location {
                    font-size: 28rpx;
                    color: #262626;
                    font-weight: bold;
                    line-height: 1.4;
                    word-wrap: break-word;
                    word-break: break-all;
                }
            }
        }

        .address-arrow {
            flex-shrink: 0;
            margin-left: 24rpx;
            display: flex;
            align-items: center;
        }

        /* 空状态样式 */
        &--empty {
            background-color: #fff;

            .address-placeholder {
                color: #999999;
            }
        }
    }
}
</style>