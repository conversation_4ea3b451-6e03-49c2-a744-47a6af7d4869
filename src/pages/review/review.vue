<template>
	<view class="review-box">
		<navbar autoBack />

		<mescroll-body :up="upOption" :down="downOptions" @init="mescrollInit" @down="downCallback" @up="upCallback">
			<view class="review-score">
				<view class="review-score-left">
					<u--text :text="RatingCount" size="62rpx" bold align="center" margin="0 0 6px 0"></u--text>
					<u-rate v-model="RatingCount" disabled active-color="#FF5A1E" inactive-color="#D9D9D9" readonly
						allowHalf></u-rate>
					<u--text :text="`${TotalRating}  Reviews`" color="#8C8C8C" size="29rpx" align="center" bold
						margin="3px 0 0 0" style="font-family: PingFang SC-Bold;"></u--text>
				</view>
				<u-line color="#F7F7F7" direction="col" length="190rpx" margin="0 40rpx"></u-line>
				<view class="review-progress">
					<view class="progress-item" v-for="(item, key) in RatingLevel" :key="key">
						<text class="title">{{ item.star }} Star</text>
						<u-line-progress class="line-progress" :percentage="progressValue(item.count)"
							activeColor="#FF5A1E" inactiveColor="#D9D9D9" height="12rpx"
							:showText="false"></u-line-progress>
						<text class="title" style="font-family: PingFang SC-Bold;">{{ progressValue(item.count)
						}}%</text>
					</view>
				</view>
			</view>


			<view class="review-box-list" v-show="list.length">
				<view v-if="IsReview == 1" class="self-headerImg"
					@click="$tab.navigateTo(`/pages/tabs/mine/orderReview/orderReview?ProId=${ProId}`)">
					<u-avatar :src="avatarImg" size="96rpx"></u-avatar>
					<u--text text="Write..." color="#8C8C8C" size="27rpx" prefixIcon="edit-pen"
						iconStyle="color: #8C8C8C;font-size: 37rpx;margin-right: 3px;"></u--text>
				</view>
				<view class="review-item-box" v-for="(item, index) in list" :key="'bk' + index">
					<view class="flex align-center" @click="handleCommentList(item)">
						<u-avatar :src="item.Avatar" size="77rpx" style="margin-right: 13rpx;"></u-avatar>
						<view class="flex justify-between align-center flex-sub">
							<view>
								<u--text :text="item.Name" color="#262626" size="27rpx" bold
									style="font-family:PingFang SC-Bold;"></u--text>
								<u--text :text="item.AccTime" color="#8C8C8C" size="27rpx"
									style="font-family:PingFang SC-Regular;"></u--text>
							</view>

							<u-rate disabled v-model="item.Rating" active-color="#FF5A1E" inactive-color="#D9D9D9"
								readonly gutter="0" allowHalf></u-rate>
						</view>
					</view>

					<view style="padding: 30rpx;" @click="handleCommentList(item)">
						<mote-lines-divide :line="2" expandText="More" foldHint="Pack up">

							<text style="color: #262626; font-size: 29rpx; font-weight: 500;"
								:style="{ fontFamily: 'PingFang SC-Medium' }" v-html="item.Content"></text>
						</mote-lines-divide>
					</view>

					<imageList style="margin-bottom: 30rpx;" :imgList="item.PicPath"></imageList>

					<view class="flex justify-end align-center">
						<view class="flex align-center" @click.stop="handleCommentLike(item)">
							<template v-if="item.IsAgree == 1">
								<u--image src="/static/assets/home/<USER>" width="38rpx" height="38rpx"
									duration="0"></u--image>
							</template>
							<template v-else>
								<u--image src="/static/assets/home/<USER>" width="38rpx" height="38rpx"
									duration="0"></u--image>
							</template>
							<u--text :text="item.Agree" color="#262626" size="27rpx" bold margin="0 0 0 4rpx"></u--text>
						</view>
						<view class="flex align-center" @click.stop="handleCommentList(item)"
							style="margin-left: 40rpx;">
							<u--image src="/static/assets/home/<USER>" width="38rpx" height="38rpx"
								duration="0"></u--image>
							<u--text :text="item.reply && item.reply.length || 0" color="#262626" size="27rpx" bold
								margin="0 0 0 4rpx"></u--text>
						</view>
					</view>

					<u-line color="#F7F7F7" margin="30rpx 0"></u-line>
				</view>
			</view>

		</mescroll-body>

		<!-- 评论 -->
		<view @touchmove.stop.prevent>
			<u-popup :show="isCommentShow" :round="11" closeable @close="closeCommentPopup">
				<view class="comment-box">
					<u--text :text="commentTitle" size="38rpx" color="rgba(0,0,0,0.8)" align="center" bold
						margin="0 0 50rpx 0"></u--text>

					<template v-if="commentList.length">
						<scroll-view :scroll-top="commentScrollTop" scroll-y="true" style="height: calc(100% - 110px);"
							@scroll="handleCommentScroll" @scrolltolower="onScrollToLower" lower-threshold="100">
							<view class="review-item-box">
								<view v-for="(item, index) in commentList" :key="index">
									<view class="flex">
										<u-avatar :src="item.Avatar" size="77rpx"
											style="margin-right: 13rpx;"></u-avatar>
										<view class="flex justify-between align-center flex-sub comment-item">
											<view class="comment-info">
												<view class="flex">
													<u--text :text="item.Name" color="#262626" size="25rpx" bold
														margin="0 5px 0 0"
														style="width: fit-content; flex: none;"></u--text>

												</view>
												<mote-lines-divide :line="2" expandText="More" foldHint="Pack up">
													<text style="color: #262626; font-size: 26rpx; font-weight: 500;"
														v-html="item.Content"></text>
												</mote-lines-divide>
												<view class="flex align-center  justify-between"
													style="margin: 4px 0 0 0;">
													<view class="flex  align-center">
														<u--text :text="item.AccTime" color="#8C8C8C" size="25rpx"
															style="width: fit-content; flex: none;"></u--text>
														<span style="margin-left: 30rpx;"></span>
														<u--text text="Reply" color="#8C8C8C" size="25rpx" bold
															class="flex-0" @click="handleReply(item, item)"></u--text>
													</view>
													<view class="z1" @click="handleCommentLike(item)">
														<u--text :text="item.Agree" v-if="item.Agree > 0"
															color="#8C8C8C" size="25rpx" bold class="flex-0"></u--text>
														<img src="../../static/assets/home/<USER>" alt=""
															v-if="item.IsAgree == 1">
														<img src="../../static/assets/home/<USER>" alt="" v-else>
													</view>
												</view>
											</view>
										</view>
									</view>
									<template v-if="item.children && item.children.length">
										<seeMore :dataList="item.children" :styleObject="styleObject">
											<template v-slot:content="{ item: cItem, index: cIndex }">
												<view class="qa-item">
													<view class="flex" style="flex:1">
														<u-avatar :src="cItem.Avatar" size="65rpx"
															style="margin-right: 19rpx;"></u-avatar>
														<view style="flex:1">
															<u--text v-if="cItem.Name" :text="cItem.Name" size="28rpx"
																bold color="#8C8C8C"></u--text>

															<mote-lines-divide :line="20" expandText="More"
																foldHint="Pack up">
																<view style="display:inline-flex; align-items: center;">
																	<u-icon style="display: inline-block;"
																		name="play-right-fill" color="#111"
																		size="25rpx"></u-icon>
																	<u--text :text="cItem.Author" color="#FF5A1E"
																		size="26rpx" bold></u--text>

																</view>
																<text
																	style="color: #666666;word-break: break-all !important; font-size: 26rpx; font-weight: 500; word-wrap: break-word; white-space: pre-wrap;"
																	v-html="cItem.Content"></text>
															</mote-lines-divide>
															<view class="flex  justify-between align-center"
																style="margin: 4px 0 0 0;">
																<view class="flex">
																	<u--text :text="cItem.AccTime" color="#8C8C8C"
																		size="25rpx"
																		style="width: fit-content; flex: none;"></u--text>
																	<span style="margin-left: 30rpx;"></span>
																	<u--text text="Reply" color="#8C8C8C" size="25rpx"
																		bold class="flex-0"
																		@click="handleReply(cItem, item)"></u--text>
																</view>

																<view class="z1" @click.stop="handleCommentLike(cItem)">
																	<u--text :text="cItem.Agree" v-if="cItem.Agree > 0"
																		color="#8C8C8C" size="25rpx" bold
																		class="flex-0"></u--text>
																	<img src="../../static/assets/home/<USER>"
																		alt="" v-if="cItem.IsAgree == 1">
																	<img src="../../static/assets/home/<USER>"
																		alt="" v-else>

																</view>
															</view>
														</view>
													</view>
												</view>
											</template>
										</seeMore>
									</template>
								</view>
							</view>

							<!-- 滚动加载状态提示 -->
							<view class="scroll-load-status">
								<view v-if="isLoadingMore" class="loading-tip">
									<u-loading-icon mode="spinner" color="#f8f8f" size="40rpx"></u-loading-icon>
									<text class="loading-text">Loading more comments...</text>
								</view>
								<view v-else-if="hasMoreComments" class="more-tip">
									<text class="tip-text">Scroll down to load more ({{ remainingCount }} left)</text>
								</view>
								<view v-else-if="allCommentList.length > pageSize" class="end-tip">
									<text class="end-text">All {{ allCommentList.length }} comments loaded</text>
								</view>
							</view>
						</scroll-view>
					</template>

					<template v-else>
						<view class="flex justify-center align-center" style="height: calc(100% - 52px);">
							<u--image src="/static/assets/common/not_comment.png" width="385rpx"
								height="385rpx"></u--image>
						</view>
					</template>
				</view>

				<view class="comment-input-box">
					<view class="comment-input" @click="handleCommentInput">
						<u-icon name="edit-pen" color="#8C8C8C" size="37rpx"></u-icon>
						<u--text text="Write..." color="#8C8C8C" size="27rpx" margin="0 0 0 4px"></u--text>
					</view>
				</view>
			</u-popup>
		</view>
		<u-popup class="comment-detail-popup" :show="isCommentDetailShow" zIndex="99999" :closeable="false"
			:overlayStyle="{ 'z-index': 99999 }" @close="isCommentDetailShow = false">
			<view class="comment-detail-box">
				<u--textarea ref="textareaRef" class="comment-text" v-model="commentValue" :placeholder="placeholder"
					:focus="focus" autoHeight border="none" :selectionStart="focusPosition"
					:selectionEnd="focusPosition" :confirmType="null" @focus="handleFocus"></u--textarea>
				<u-button v-show="commentValue !== ''" :loading="isSureLoading" class="sure-btn" color="#FF5A1E"
					@click="handleConfirm">Sure</u-button>
			</view>
		</u-popup>
	</view>
</template>

<script>
import {
	getCommentList,
	commentLike,
	addComment,
	addCommentAgain
} from "@/api/orderDetails.js";
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import MoteLinesDivide from "@/components/mote-lines-divide/mote-lines-divide"
// import keyboardObserver from "keyboard-height";

export default {
	mixins: [MescrollMixin], // 使用mixin
	components: {
		MoteLinesDivide
	},
	data() {
		return {
			expandedReplies: {}, // 记录每个评论的展开状态
			commentScrollTop: 0,
			old: {
				scrollTop: 0
			},
			focusPosition: -1,
			avatarImg: '',
			ProId: '',
			commentTitle: '---',
			RatingCount: 0,
			TotalRating: 0,
			isCommentShow: false,
			isCommentDetailShow: false,
			focus: false,
			submit: false,
			isSureLoading: false,
			commentValue: '',
			placeholder: 'Write...',
			KeyboardHeight: 0,
			parentRid: '',
			replyRid: '',
			isReply: false,
			IsReview: 0,
			replyIndex: '',
			RatingLevel: {},
			list: [],
			commentList: [],
			// 滚动加载相关数据
			allCommentList: [], // 存储所有评论数据
			currentPage: 1,     // 当前页码
			pageSize: 10,       // 每页显示数量
			totalPages: 0,      // 总页数
			isLoadingMore: false, // 是否正在加载更多
			downOptions: {
				// use: false
			},
			commentInputStyle: {
				background: '#F0F0F0'
			},
			styleObject: {
				'margin-left': '160rpx'
			},
			defaultPhoneHeight: window.innerHeight, // 屏幕默认高度
			upOption: {
				toTop: {
					duration: 0
				}
			}
		};
	},
	onLoad(e) {
		this.ProId = e?.ProId;
	},
	onShow() {
		uni.$once('handleReviewRefresh', (refresh) => {
			if (refresh) {
				this.reloadList();
			}
		})
	},
	computed: {
		progressValue() {
			return ratingCount => {
				if (this.TotalRating == 0 || ratingCount == 0) {
					return 0;
				}
				return parseInt((ratingCount / this.TotalRating * 100));
			}
		},
		// 滚动加载相关计算属性
		hasMoreComments() {
			return this.commentList.length < this.allCommentList.length;
		},
		remainingCount() {
			return this.allCommentList.length - this.commentList.length;
		}
	},
	watch: {
		commentList(val) {
			const replyLen = val?.length;
			this.commentTitle = replyLen > 0 ? `Comment(${replyLen})` : `Not Comment`
		}
	},
	mounted() {
		window.addEventListener('resize', this.handleResize);
	},
	methods: {
		handleResize() {
			const currentHeight = window.innerHeight;
			if (currentHeight < this.defaultPhoneHeight) {
				console.log('软键盘弹起');
			} else {
				this.isCommentDetailShow = false;
			}
		},
		reloadList() {
			this.mescroll.resetUpScroll();
		},
		upCallback(page) {
			getCommentList({
				ProId: this.ProId,
				page: page.num
			}).then(res => {
				console.log(res);
				this.RatingCount = res?.Rating;
				this.TotalRating = res?.TotalRating;
				let rateLevel = res?.RatingLevel || {}
				this.RatingLevel = Object.keys(rateLevel)
					.sort((a, b) => b - a)
					.map(key => ({ star: key, count: rateLevel[key] }));
				this.IsReview = res?.IsReview;

				if (page.num == 1) {
					this.list = [];
				}
				this.list = this.list.concat(res.reviews);
				console.log("🚀 ~ file: review.vue:344 ~ this.list:", this.list, this.list.length)
				this.mescroll.endByPage(res.reviews.length, res.total_pages);
			}).catch(() => {
				//联网失败, 结束加载
				this.mescroll.endErr();
			})
		},
		handleCommentLike(item) {
			commentLike({
				ProId: this.ProId,
				RId: item.RId
			}).then(res => {
				if (item.Agree < res) {
					this.$toast({
						title: 'Like successfully'
					})
				}

				item.IsAgree = Number(item.IsAgree) == 0 || item.IsAgree == undefined ? 1 : 0;
				item.Agree = res;
			}).catch(() => {
				this.$toast({
					title: 'Failed to like'
				})
			})
		},
		// 回复 - 修改为支持滚动加载
		handleCommentList(item) {
			this.allCommentList = item?.reply ?? [];
			this.currentPage = 1;
			this.totalPages = Math.ceil(this.allCommentList.length / this.pageSize);
			this.loadInitialComments();
			this.isCommentShow = true;
			this.parentRid = item?.RId;
		},

		// 加载初始评论（第一页）
		loadInitialComments() {
			this.commentList = this.allCommentList.slice(0, this.pageSize);
		},

		// 加载更多评论
		loadMoreComments() {
			if (this.isLoadingMore || !this.hasMoreComments) return;

			this.isLoadingMore = true;

			// 模拟网络延迟
			setTimeout(() => {
				const currentLength = this.commentList.length;
				const nextBatch = this.allCommentList.slice(currentLength, currentLength + this.pageSize);
				this.commentList = [...this.commentList, ...nextBatch];
				this.isLoadingMore = false;
			}, 500);
		},

		// 滚动到底部时触发
		onScrollToLower() {
			console.log('滚动到底部，尝试加载更多...');
			this.loadMoreComments();
		},

		// 关闭评论弹窗并重置数据
		closeCommentPopup() {
			this.isCommentShow = false;
			this.currentPage = 1;
			this.allCommentList = [];
			this.commentList = [];
			this.totalPages = 0;
			this.isLoadingMore = false;
		},
		handleCommentInput() {
			this.isCommentDetailShow = true;
			this.placeholder = 'Write...';
			this.isReply = false;
			this.focus = true;
		},
		handleReply(item, parentItem) {
			console.log("🚀 ~ file: review.vue:386 ~ item:", item)
			this.placeholder = `Reply to： ${item?.Name}`;
			this.replyRid = item?.RId;
			this.isCommentDetailShow = true;
			this.isReply = true;
			this.clickParentItem = parentItem;
			this.commentValue = '';
			this.$nextTick(() => {
				this.focus = true;
			})
		},
		handleFocus() {
			const textarea = this.$refs.textareaRef;
			if (textarea) {
				this.focusPosition = this.commentValue.length;
			}
		},
		handleCommentScroll(e) {
			this.old.scrollTop = e.detail.scrollTop
		},
		replaceCommentByRId(list, newItem) {
			for (let i = 0; i < list.length; i++) {
				if (list[i].RId === newItem.RId) {
					list.splice(i, 1, newItem);
					return true;
				}
				if (list[i].reply && Array.isArray(list[i].reply)) {
					if (replaceCommentByRId(list[i].reply, newItem)) return true;
				}
			}

			return false;
		},
		handleConfirm() {
			this.isSureLoading = true;

			addComment({
				ProId: this.ProId,
				Content: this.commentValue,
				RId: this.isReply ? this.replyRid : this.parentRid,
			}).then(res => {
				const findItem = this.list.find(item => item.RId === this.parentRid)
				console.log("🚀 ~ file: review.vue:426 ~ findItem:", findItem)
				if (!this.isReply) {
					// 主评论回复，添加到所有数据的开头
					this.allCommentList.unshift({ ...res, reply: [], Agree: 0, IsAgree: 0 });
					this.totalPages = Math.ceil(this.allCommentList.length / this.pageSize);

					// 重新加载第一页，显示新评论
					this.loadInitialComments();

					for (let key of this.list) {
						if (key.RId === this.parentRid) {
							key.ReplyLen += 1
						}
					}
				}
				else {
					// 子评论回复，添加到对应的子评论中
					for (let item of this.allCommentList) {
						if (item.RId === this.clickParentItem.RId) {
							const newReply = { ...res, reply: [], Agree: 0, IsAgree: 0 };
							if (item.children) {
								item.children.unshift(newReply);
							} else {
								item.children = [newReply];
							}
							break;
						}
					}
					// 子评论添加后，重新加载当前显示的数据
					this.loadInitialComments();
				}

				this.$toast({
					title: 'Comment successfully'
				})


			}).catch(() => {
				this.$toast({
					title: 'Failed to comment'
				})
			}).finally(() => {
				this.commentValue = '';
				this.isCommentDetailShow = false;
				this.replyIndex = '';
				uni.hideKeyboard();
				this.focus = false;
				this.isSureLoading = false;
			})
		},

	}
}
</script>

<style lang="scss" scoped>
.review-box {
	padding: 27rpx 30rpx;
	box-sizing: border-box;
	font-family: PingFang SC-Medium;

	.review-score {
		background: #fff;
		border-radius: 30rpx;
		padding: 30rpx 30rpx 30rpx 35rpx;
		box-sizing: border-box;
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 35rpx;

		.review-score-left {
			width: 35%;
			display: flex;
			flex-direction: column;
			align-items: center;
		}

		.review-progress {
			flex: 1;
			display: flex;
			flex-direction: column;
			@include flex-gap(10rpx, 0); // 替换了 row-gap

			.progress-item {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.title {
					font-weight: bold;
					font-size: 25rpx;
					color: #B7B7B7;
				}

				.line-progress {
					margin: 0 16rpx 0;
				}
			}
		}
	}

	.review-box-list {
		background: #fff;
		padding: 30rpx;
		border-radius: 30rpx;
		box-sizing: border-box;

		.self-headerImg {
			height: 96rpx;
			background: #F0F0F0;
			border-radius: 96rpx;
			display: flex;
			@include flex-gap(0, 13rpx); // 替换了 column-gap
			margin-bottom: 73rpx;
		}
	}


	.comment-box {
		padding: 30rpx 30rpx 0;
		height: 70vh;

		.review-item-box {
			display: flex;
			flex-direction: column;
			@include flex-gap(50rpx, 0); // 替换了 row-gap

			.comment-item {
				@include flex-gap(0, 20px); // 替换了 column-gap

				.comment-info {
					width: 100%;
				}
			}

			.qa-item {
				margin: 30rpx 0 30rpx 80rpx;
			}
		}
	}

	.comment-input-box {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background: #fff;
		box-shadow: 0px -4rpx 8rpx 0px rgba(0, 0, 0, 0.1);
		padding-bottom: 15px;

		.comment-input {
			padding: 25rpx 37rpx;
			margin: 5px 30rpx 10px;
			background: #F0F0F0;
			border-radius: 58rpx;
			display: flex;
		}
	}

	.commentInput {
		margin: 17rpx 30rpx;
		background: #F0F0F0;

		::v-deep .u-input__content {
			padding: 20rpx 30rpx;

			.u-icon__icon {
				color: #8C8C8C !important;
			}

			.input-placeholder {
				color: #8C8C8C !important;
			}
		}
	}

	.comment-detail-box {
		background: #fff;
		display: flex;
		justify-content: space-between;
		align-items: center;
		@include flex-gap(0, 8px); // 替换了 column-gap

		.comment-text {
			touch-action: none;
			max-height: 80px;
			overflow: hidden;
			overflow-y: auto;
		}

		.sure-btn {
			border-radius: 16rpx;
			height: 39px;
			width: 113rpx;
			font-size: 28rpx;
			font-weight: 500;
			color: #fff;
		}
	}

	.comment-submit-box {
		position: fixed;
		display: flex;
		align-items: flex-end;
		z-index: 9900;
		left: 0;
		top: var(--window-top);
		bottom: 0;
		background-color: rgba($color: #000000, $alpha: 0.5);
		width: 100%;

		.comment-add {
			position: fixed;
			background-color: #FFFFFF;
			width: 100%;
			padding: 5rpx;
			border: 1px solid #ddd;
			transition: .3s;
			-webkit-transition: .3s;

			.textarea {
				height: 100px;
				padding: 16rpx;
				width: 100%;
			}
		}
	}

	// 滚动加载状态样式
	.scroll-load-status {
		padding: 30rpx 30rpx 125rpx;
		text-align: center;

		.loading-tip {
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 20rpx;

			.loading-text {
				font-size: 26rpx;
				color: #CBCBCB;
			}
		}

		.more-tip {
			.tip-text {
				font-size: 24rpx;
				color: #8C8C8C;
			}
		}

		.end-tip {
			.end-text {
				font-size: 24rpx;
				color: #CBCBCB;
			}
		}
	}

	.comment-detail-popup {
		.comment-detail-box {
			margin: 10px 15px 20px;

			.u-textarea {
				background: #F0F0F0;
			}
		}
	}

	.z1 {
		display: inline-flex;
		align-items: center;

		img {
			height: 34rpx;
		}
	}
}
</style>