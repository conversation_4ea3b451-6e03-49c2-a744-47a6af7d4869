<template>
	<view class="payment-box">
		<navbar autoBack />

		<!-- Amazon Pay 处理中状态 -->
		<view class="amazon-processing" v-if="isProcessingAmazonPay">
			<u-loading-icon mode="spinner" color="#FF5A1E" size="80rpx"></u-loading-icon>
			<u--text text="Processing Payment..." color="#262626" bold size="40rpx" align="center"
				margin="30rpx 0 10rpx 0"></u--text>
			<u--text text="Please wait while we verify your payment" color="#8C8C8C" size="28rpx" align="center"
				style="text-align: center;"></u--text>
		</view>

		<!-- 正常支付结果显示 -->
		<template v-else>
			<!-- 支付成功图标 -->
			<u--image src="/static/assets/common/pay_success.png" width="150" height="100"
				v-if="showSuccessIcon"></u--image>

			<!-- 支付失败图标 -->
			<u--image src="/static/assets/common/pay_failed.png" width="150" height="100"
				v-else-if="showFailedIcon"></u--image>

			<view class="payment-result-box">
				<u--text :text="paymentResultTitle" color="#262626" bold size="50rpx" align="center"
					margin="0 0 10rpx 0"></u--text>
				<u--text :text="orderNumber" color="#8C8C8C" bold size="35rpx" align="center"
					style="text-align: center;"></u--text>
			</view>
		</template>
		<view class="payment-result-box" style="margin-top:320rpx;font-size:30rpx;color:#8C8C8C" v-if="showWarmNote">
			<view style="color: #FF5A1E;margin-bottom: 20rpx;">
				*Warm note:
			</view>
			If you have paid it ,we will change the order status automatically.
		</view>
		<view class="order-detail-btn">
			<SubmitButton text="Order Detail" @handleConfirm="goToOrderDetail">
			</SubmitButton>
		</view>
	</view>
</template>

<script>
import { getOrderStatusDetail, amazonPayCapture } from "@/api/orderDetails.js";

// 订单状态常量 - 支持字符串和数字
const ORDER_STATUS = {
	PENDING_PAYMENT: ['1', '2', 1, 2],
	PAYMENT_SUCCESS: ['4', '5', '6', 4, 5, 6],
	PAYMENT_FAILED: ['0', '3', '7', '8', '9', 0, 3, 7, 8, 9] // 其他状态视为失败
};

export default {
	name: 'PaymentResult',

	data() {
		return {
			optionsData: {},
			orderInfo: {},
			isProcessingAmazonPay: false,
			processingError: null,
		};
	},

	async onLoad(options) {
		try {
			this.optionsData = options;
			console.log('支付结果页面参数:', options);

			await this.initializePaymentResult(options);
		} catch (error) {
			console.error('页面初始化失败:', error);
			this.handleError(error);
		}
	},

	computed: {
		/**
		 * 支付结果标题
		 */
		paymentResultTitle() {
			const status = this.orderInfo.OrderStatus;
			console.log('🔍 当前订单状态:', status, '类型:', typeof status);

			if (ORDER_STATUS.PENDING_PAYMENT.includes(status)) {
				return 'In Payment';
			} else if (ORDER_STATUS.PAYMENT_SUCCESS.includes(status)) {
				return 'Successful Payment';
			} else {
				return 'Failed Payment';
			}
		},

		/**
		 * 订单号显示
		 */
		orderNumber() {
			const status = this.orderInfo.OrderStatus;
			const orderNumber = this.optionsData.orderNumber || this.optionsData.orderID;

			const successStatuses = [...ORDER_STATUS.PENDING_PAYMENT, ...ORDER_STATUS.PAYMENT_SUCCESS];
			if (successStatuses.includes(status)) {
				return `Order Number: ${orderNumber}`;
			} else {
				return 'Unpaid orders will be automatically canceled after 30 minutes.';
			}
		},

		/**
		 * 是否显示成功图标 (待支付和支付成功都显示成功图标)
		 */
		showSuccessIcon() {
			const status = this.orderInfo.OrderStatus;
			const successStatuses = [...ORDER_STATUS.PENDING_PAYMENT, ...ORDER_STATUS.PAYMENT_SUCCESS];
			return successStatuses.includes(status);
		},

		/**
		 * 是否显示失败图标
		 */
		showFailedIcon() {
			const status = this.orderInfo.OrderStatus;
			return ORDER_STATUS.PAYMENT_FAILED.includes(status);
		},

		/**
		 * 是否显示温馨提示
		 */
		showWarmNote() {
			const status = this.orderInfo.OrderStatus;
			return ORDER_STATUS.PENDING_PAYMENT.includes(status);
		}
	},

	methods: {
		/**
		 * 初始化支付结果页面
		 */
		async initializePaymentResult(options) {
			// 检查是否需要处理Amazon Pay捕获
			if (this.isAmazonPayCapture(options)) {
				await this.processAmazonPayCapture(options);
			} else {
				await this.loadOrderStatus(options);
			}
		},

		/**
		 * 检查是否为Amazon Pay捕获流程
		 */
		isAmazonPayCapture(options) {
			return !!(options.amazonCheckoutSessionId && (options.orderID || options.orderNumber));
		},

		/**
		 * 处理Amazon Pay捕获流程
		 */
		async processAmazonPayCapture(options) {
			this.isProcessingAmazonPay = true;

			try {
				console.log('开始Amazon Pay捕获流程:', options);

				// 调用捕获接口
				const captureResult = await this.callAmazonPayCapture(options);
				console.log('Amazon Pay捕获结果:', captureResult);

				// 加载订单状态
				await this.loadOrderStatus(options);

			} catch (error) {
				console.error('Amazon Pay捕获失败:', error);
				this.processingError = error;

				// 即使捕获失败，也尝试加载订单状态
				try {
					await this.loadOrderStatus(options);
				} catch (statusError) {
					console.error('订单状态查询也失败:', statusError);
				}
			} finally {
				this.isProcessingAmazonPay = false;
			}
		},

		/**
		 * 调用Amazon Pay捕获接口
		 */
		async callAmazonPayCapture(options) {
			const params = {
				orderID: options.orderID || options.orderNumber,
				amazonCheckoutSessionId: options.amazonCheckoutSessionId
			};

			return await amazonPayCapture(params);
		},

		/**
		 * 加载订单状态
		 */
		async loadOrderStatus(options) {
			const orderId = this.getOrderId(options);

			if (!orderId) {
				throw new Error('缺少订单ID参数');
			}

			console.log('查询订单状态:', orderId);

			const orderData = await getOrderStatusDetail({ OrderId: orderId });
			this.orderInfo = orderData || {};

			console.log('订单状态查询结果:', this.orderInfo);

			// 调试状态匹配
			this.debugStatusMatching();
		},

		/**
		 * 调试状态匹配（临时方法）
		 */
		debugStatusMatching() {
			const status = this.orderInfo.OrderStatus;
			console.log('🔍 状态匹配调试:');
			console.log('- 当前状态:', status, '(类型:', typeof status, ')');
			console.log('- PENDING_PAYMENT包含:', ORDER_STATUS.PENDING_PAYMENT.includes(status));
			console.log('- PAYMENT_SUCCESS包含:', ORDER_STATUS.PAYMENT_SUCCESS.includes(status));
			console.log('- PAYMENT_FAILED包含:', ORDER_STATUS.PAYMENT_FAILED.includes(status));
			console.log('- 计算属性结果:');
			console.log('  - paymentResultTitle:', this.paymentResultTitle);
			console.log('  - showSuccessIcon:', this.showSuccessIcon);
			console.log('  - showFailedIcon:', this.showFailedIcon);
			console.log('  - showWarmNote:', this.showWarmNote);
		},

		/**
		 * 获取订单ID
		 */
		getOrderId(options) {
			return options.orderNumber || options.orderID || '';
		},

		/**
		 * 错误处理
		 */
		handleError(error) {
			console.error('支付结果页面错误:', error);
			// 可以在这里添加错误提示逻辑
		},

		/**
		 * 跳转到订单详情
		 */
		goToOrderDetail() {
			const orderId = this.getOrderId(this.optionsData);
			if (orderId) {
				this.$tab.redirectTo(`/pages/tabs/mine/OrderStatus/OrderStatus?OrderId=${orderId}`);
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.payment-box {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 40% 69rpx;

	.payment-result-box {
		margin-top: 96rpx;
	}

	.order-detail-btn {
		position: fixed;
		bottom: 96rpx;
		left: 0;
		right: 0;
		padding: 0 31rpx;
	}

	.amazon-processing {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 40rpx;
		text-align: center;
		margin-top: 100rpx;
	}
}
</style>