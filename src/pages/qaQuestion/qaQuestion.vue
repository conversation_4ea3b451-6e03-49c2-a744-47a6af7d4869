<template>
	<view>
		<navbar autoBack />
		<view class="order-review-box">
			<view class="review-box">
				<u--textarea class="review-txt" v-model="Question" placeholder="Write..." border="none" count
					:maxlength="-1" height="250rpx" :count="false" :adjustPosition="false" :focus="true"></u--textarea>
			</view>
			<u-button class="post-btn" text="Post" shape="circle" @click="handlePost"></u-button>
		</view>
	</view>
</template>

<script>
import {
	addQuestion,
	replyQuestion
} from "@/api/orderDetails.js";

export default {
	data() {
		return {
			ProId: '',
			QId: '',
			isLoadPage: false,
			Question: ''
		};
	},
	onLoad(e) {
		this.ProId = e.ProId;
		this.QId = e.QId;
	},
	methods: {
		handlePost() {
			if (this.Question == '') {
				return this.$toast({
					title: 'Question cannot be empty'
				})
			}

			let Fn = null;
			let data = null;

			if (this.QId) {
				Fn = replyQuestion;
				data = {
					ProId: this.ProId,
					Answer: this.Question,
					QId: this.QId
				}
			} else {
				Fn = addQuestion;
				data = {
					ProId: this.ProId,
					Question: this.Question
				}
			}

			Fn(data).then(res => {
				this.$toast({
					title: 'Post Success!'
				})

				uni.$emit('handleQaRefresh', {
					refresh: true,
					isToTop: this.QId ? false : true
				});

				setTimeout(() => {
					this.$tab.navigateBack();
				}, 500)
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.order-review-box {
	padding: 23rpx 31rpx;
	box-sizing: border-box;

	.review-box {
		background: #FFFFFF;
		border-radius: 31rpx;
		box-sizing: border-box;
		padding: 31rpx;

		.like-button-box {
			margin-bottom: 113rpx;
			display: flex;
			align-items: center;
			@include flex-gap(0, 38rpx); // 替换了 column-gap

			.like-item {
				width: 58rpx;
				height: 58rpx;
				background: #FF5A1E;
				border-radius: 12rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				.icon-star {
					font-size: 40rpx;
					color: #FFFFFF;
					transition: color .3s ease;

					&.active {
						color: gold;
					}
				}
			}
		}

		.review-txt {
			padding: 0;
			margin-bottom: 27rpx;
		}
	}

	.post-btn {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 96rpx;
		width: 688rpx;
		height: 112rpx;
		background: #FF5A1E;
		border-radius: 19rpx;
		font-weight: bold;

		color: #FFFFFF;

		::v-deep .u-button__text {
			font-size: 38rpx !important;
		}
	}
}
</style>