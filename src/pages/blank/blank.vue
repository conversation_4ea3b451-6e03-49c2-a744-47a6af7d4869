<template>
	<view class="html-content-page">
		<navbar autoBack />
		
		<!-- 加载状态 -->
		<u-loading-page :loading="isLoading" bg-color="#F0F0F0" iconSize="30" loadingText="Loading..."></u-loading-page>
		
		<!-- HTML内容渲染区域 -->
		<view class="html-container" v-if="!isLoading && htmlContent">
			<view class="html-content" v-html="htmlContent"></view>
		</view>
		
		<!-- 错误状态 -->
		<view class="error-container" v-if="!isLoading && error">
			<view class="error-message">{{ error }}</view>
			<button @click="loadContent" class="retry-btn">Retry</button>
		</view>
	</view>
</template>

<script>
	import { getPageContent } from '@/api/common.js';
	
	export default {
		data() {
			return {
				htmlContent: '',
				isLoading: true,
				error: '',
				Aid: '' // 页面ID
			}
		},
		onLoad(options) {
			// 从路由参数获取Aid
			this.Aid = options.aid || '137'; // 默认使用137
			this.loadContent();
		},
		methods: {
			async loadContent() {
				this.isLoading = true;
				this.error = '';
				
				try {
					console.log('正在获取页面内容，Aid:', this.Aid);
					const response = await getPageContent(this.Aid);
					
					console.log('API响应数据:', response);
					
					// 处理不同的响应数据格式
					let htmlContent = '';
					
					if (response) {
						// 情况1: 响应结构为 { data: { html: "..." } }
						if (response.data && response.data.html) {
							htmlContent = response.data.html;
						}
						// 情况2: 响应结构为 { html: "..." }
						else if (response.html) {
							htmlContent = response.html;
						}
						// 情况3: 响应结构为 { data: "..." }
						else if (response.data && typeof response.data === 'string') {
							htmlContent = response.data;
						}
						// 情况4: 直接返回HTML字符串
						else if (typeof response === 'string') {
							htmlContent = response;
						}
						// 情况5: 其他可能的字段
						else if (response.content) {
							htmlContent = response.content;
						}
					}
					
					if (htmlContent) {
						this.htmlContent = htmlContent;
						console.log('HTML内容获取成功，长度:', htmlContent.length);
					} else {
						throw new Error('未找到HTML内容字段');
					}
					
				} catch (error) {
					console.error('获取页面内容失败:', error);
					this.error = error.message || '获取页面内容失败，请重试';
				} finally {
					this.isLoading = false;
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
.html-content-page {
	min-height: 100vh;
	background: #fff;
}

.html-container {
	width: 100%;
	overflow-x: auto;
}

.html-content {
	padding: 20rpx;
	
	// 确保HTML内容正常显示
	::v-deep * {
		max-width: 100% !important;
		box-sizing: border-box;
	}
	
	// 处理图片自适应
	::v-deep img {
		max-width: 100% !important;
		height: auto !important;
	}
	
	// 处理表格横向滚动
	::v-deep table {
		width: 100%;
		overflow-x: auto;
		display: block;
		white-space: nowrap;
	}
}

.error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 40rpx;
	text-align: center;
}

.error-message {
	color: #999;
	font-size: 28rpx;
	margin-bottom: 40rpx;
}

.retry-btn {
	padding: 20rpx 40rpx;
	background: #007aff;
	color: white;
	border: none;
	border-radius: 10rpx;
	font-size: 28rpx;
}
</style>
