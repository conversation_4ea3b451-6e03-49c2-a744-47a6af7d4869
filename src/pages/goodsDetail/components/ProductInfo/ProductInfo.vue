<template>
	<view class="goods-info" :style="goodsInfoBg">
		<view class="flex justify-between align-center goods-info-title">
			<u--text :text="productData.pro && productData.pro.Name_en" size="46rpx" color="#262626" bold></u--text>
			<!-- 限时秒杀 -->
			<template v-if="productsType == 2">
				<u-tag text="Limited Time Deal" size="mini" bgColor="#FF5A1E" borderColor="#FF5A1E"
					shape="circle"></u-tag>
			</template>
			<!-- 限时促销 -->
			<template v-if="productsType == 6">
				<u-tag text="Promotion" size="mini" bgColor="#FF5A1E" borderColor="#FF5A1E" shape="circle"></u-tag>
			</template>
		</view>
		<u--text :text="productData.pro && productData.pro.BriefDescription_en" size="31rpx" color="#262626"
			bold></u--text>

		<view class="flex justify-between align-center" style="margin-top:17rpx">
			<view class="flex price-box" style="max-width: 65%;flex-wrap: wrap;">
				<u--text :text="priceSymbol + currentPrice" size="46rpx" color="#FF5A1E" bold
					margin="0 13rpx 0 0"></u--text>
				<u--text :show="marketPrice > 0" :text="marketPrice" size="35rpx" color="#8C8C8C"
					decoration="line-through" style="font-weight: 500"></u--text>
				<!-- <span class="current-price">{{ priceSymbol }}{{ currentPrice }}</span>
				<span class="market-price">{{ priceSymbol }}{{ marketPrice }}</span> -->
			</view>
			<view class="flex align-center">
				<u-rate v-model="rating" disabled readonly activeColor="#FFC01E" inactiveColor="#D9D9D9"
					gutter="0"></u-rate>
				<u--text :text="TotalRating" size="29rpx" color="#B7B7B7" margin="0 0 0 6rpx"
					style="font-weight: 500;"></u--text>
			</view>
		</view>
	</view>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
	name: "ProductInfo",
	props: {
		productData: {
			type: Object,
			default: () => ({})
		},
		currentPrice: {
			type: [String, Number],
			default: "0.00"
		},
		marketPrice: {
			type: [String, Number],
			default: "0.00"
		},
		rating: {
			type: [Number, String],
			default: 0
		},
		TotalRating: {
			type: [Number, String],
			default: 0
		},
		productsType: {
			type: Number,
			default: 0
		}
	},
	computed: {
		...mapGetters(['priceSymbol']),
		goodsInfoBg() {
			const isLightBg = [2, 6];
			if (isLightBg.includes(+this.productsType)) {
				return 'background: linear-gradient(180deg, #f9bfa5 0%, #ffffff 38%, rgba(255, 205, 183, 0) 100%), #FFFFFF';
			} else {
				return 'background: #fff';
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.price-box {
	min-height: 65rpx;
	display: flex;
	align-items: center;

	.current-price {
		color: #FF5A1E;
		font-weight: 700;
		font-size: 46rpx;
	}

	.market-price {
		color: #8C8C8C;
		font-size: 35rpx;
	}

	span {}

	::v-deep uni-view {}
}

.goods-info {
	padding: 62rpx 31rpx 31rpx;
	border-radius: 31rpx 31rpx 0 0;

	.goods-info-title {
		margin-bottom: 17rpx;
		@include flex-gap(0, 20rpx); // 替换了 column-gap
	}
}
</style>