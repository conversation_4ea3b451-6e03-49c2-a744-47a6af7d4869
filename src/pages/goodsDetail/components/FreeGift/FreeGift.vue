<template>
	<view class="free-gift-box" v-if="freeGiftList.length > 0">
		<view class="flex align-center justify-between" style="margin-bottom: 16rpx;">
			<u--text text="Free Gift" color="#262626" size="33rpx" bold></u--text>
		</view>
		<scroll-view scroll-x>
			<view class="free-gift-list">
				<view v-for="(item, index) in freeGiftList" :key="index" class="gift-item">
					<view class="gift-img">
						<image class="img" :src="item.PicPath_0" mode="aspectFit"></image>
					</view>
					<view class="flex-sub">
						<u--text :text="item.Name_en" :lines="1" color="#8C8C8C" size="24rpx"
							margin="0 0 10rpx 0"></u--text>
						<view class="flex justify-between align-center">
							<u--text :text="`X${item.Qty}`" :lines="1" color="#8C8C8C" size="24rpx"
								decoration="line-through"></u--text>
							<u--text :text="`${priceSymbol}${item.Price}`" :lines="1" color="#8C8C8C"
								size="24rpx" align="right" decoration="line-through"></u--text>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
	name: "FreeGift",
	props: {
		freeGiftList: {
			type: Array,
			default: () => []
		}
	},
	computed: {
		...mapGetters(['priceSymbol'])
	}
}
</script>

<style lang="scss" scoped>
.free-gift-box {
	padding: 38rpx 0 38rpx 31rpx;

	.free-gift-list {
		display: inline-flex;

		.gift-item {
			width: 384rpx;
			height: 120rpx;
			box-sizing: border-box;
			background-color: #F0F0F0;
			border-radius: 32rpx;
			box-sizing: border-box;
			display: inline-flex;
			align-items: center;
  @include flex-gap(0, 12rpx); // 替换了 column-gap
			padding: 16rpx;
			margin-right: 20rpx;

			.gift-img {
				width: 86rpx;
				height: 86rpx;
				background: #FFFFFF;
				border-radius: 50%;
				display: flex;
				justify-content: center;
				align-items: center;

				.img {
					width: 100%;
					height: 100%;
					border-radius: 50%;
					transform: scale(.8);
				}
			}
		}
	}
}
</style> 