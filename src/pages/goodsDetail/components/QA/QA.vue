<template>
	<view id="QA" class="qa-section default-pad" @click="handleQAClick">
		<view class="flex align-center justify-between" style="margin-bottom: 19rpx;">
			<u--text :text="`Q&A(${qaTotal})`" color="#262626" size="33rpx" bold></u--text>
			<image src="@/static/assets/common/arrow_next.png" style="width: 31rpx; height: 31rpx;">
			</image>
		</view>
		<template v-if="qaTotal > 0">
			<view class="flex align-center">
				<u--text :text="qaFirstList.Question" color="#262626" size="29rpx" bold></u--text>
			</view>
			<u--text :show="qaFirstList.Answer" :text="qaFirstList.Answer" color="#262626" size="27rpx" lines="3"
				lineHeight="32rpx" style="font-weight: 500;" margin="15rpx 0 0 0"></u--text>
			<view v-if="qaFirstList && qaFirstList.children && qaFirstList.children.length > 0">
				<u--text :text="qaFirstList.children[0].Answer" color="#8C8C8C" size="26rpx"
					margin="10rpx 0 0 0"></u--text>
			</view>
		</template>
		<template v-else>
			<u-empty iconSize="45" textSize="12" text="No Data" marginTop="10"></u-empty>
		</template>
	</view>
</template>

<script>
export default {
	name: "QA",
	props: {
		qaTotal: {
			type: Number,
			default: 0
		},
		qaFirstList: {
			type: Object,
			default: () => ({})
		},
		proId: {
			type: [String, Number],
			required: true
		}
	},
	methods: {
		handleQAClick() {
			this.$emit('qa-click', this.proId);
		}
	}
}
</script>

<style lang="scss" scoped>
.qa-section {
	padding: 38rpx 31rpx;
	cursor: pointer;
}

.default-pad {
	padding: 38rpx 31rpx;
}
</style>