<template>
	<view id="Information" class="default-pad">
		<view class="information-title">
			<u--text text="Information" color="#262626" size="33rpx" bold></u--text>
		</view>
		<view class="gift-option">
			<u-checkbox-group v-model="isCheckGift" activeColor="#FF5A1E" iconColor="#FFFFFF" shape="circle" size="25"
				placement="row" @change="handleGiftChange">
				<u-checkbox name="gift" label="I want to send this as a gift" labelSize="14" labelColor="#303133"
					labelDisabled="false" iconSize="12" iconPlacement="right" borderBottom="false"></u-checkbox>
			</u-checkbox-group>
		</view>

		<view v-if="isGift" class="gift-form">
			<!-- 邮箱输入框 -->
			<view class="form-item">
				<u-input v-model="giftForm.email" placeholder="Recipient Email" border="none" clearable></u-input>
			</view>

			<!-- 收件人姓名 -->
			<view class="form-item">
				<u-input v-model="giftForm.name" placeholder="Recipient name(optional)" border="none"
					clearable></u-input>
			</view>

			<!-- 消息输入框 -->
			<view class="form-item message-item">
				<u-textarea v-model="giftForm.message" placeholder="Message(optional)" border="none"
					height="100"></u-textarea>
			</view>

			<!-- 日期选择 -->
			<view class="form-item date-item" @tap="showCalendar">
				<view class="date-picker">
					<u-icon name="calendar-fill" size="22" color="#999" class="date-icon"></u-icon>
					<text v-if="giftForm.date" class="date-text">{{ giftForm.date }}</text>
					<text v-else class="placeholder">YYYY-MM-dd</text>
				</view>
			</view>
		</view>

		<!-- 日历弹出框 -->
		<!-- <u-calendar :lang="en" :show="showDatePicker" mode="single" @confirm="dateConfirm" @close="closeDatePicker"
			:minDate="minDate" color="#FF5A1E" :startText="'Start'" :endText="'End'" :confirmText="'Confirm'"
			:confirmDisabledText="'Confirm'" :title="'Select Date'" ref="calendar" round="18rpx"
			:showTitle="false"></u-calendar> -->
		<!-- Custom bottom slide-up animation -->
		<view class="custom-popup-mask" v-if="showDatePicker" @tap="closeDatePicker"></view>
		<view class="custom-popup-container" :class="{ active: showDatePicker }">
			<keep-alive>
				<week-fold-calendar @change="dateConfirm" :allowFuture="true" activeBgColor="#FF5A1E" />
			</keep-alive>
		</view>
	</view>
</template>

<script>
import weekFoldCalendar from '@/uni_modules/week-fold-calendar/components/week-fold-calendar/week-fold-calendar.vue'
export default {
	components: {
		weekFoldCalendar
	},
	props: {
		ProId: {
			type: [String, Number],
			default: ''
		}
	},
	data() {
		return {
			refCalendar: null,
			isGift: false,
			isCheckGift:false,
			showDatePicker: false,
			minDate: new Date().getTime(), // 设置最小日期为今天
			giftForm: {
				email: '',
				name: '',
				message: '',
				date: ''
			},

		};
	},
	computed: {
		// 使用导入的英语语言包
		en() {
			return en;
		}
	},
	methods: {
		handleGiftChange(value) {
			this.isGift = value.length > 0;
			// 如果取消礼品选项，可以清空表单数据
			if (!value.length) {
				this.resetGiftForm();
			}
		},
		// 打开日历选择器
		showCalendar() {
			console.log('显示日历');
			this.showDatePicker = true;
		},
		// 关闭日历选择器
		closeDatePicker() {
			console.log('关闭日历');
			this.showDatePicker = false;
		},
		// 日期确认回调
		dateConfirm(e) {
			console.log('选择日期:', e);
			this.giftForm.date = e;
			// 关闭日历
			this.showDatePicker = false;
		},
		// 格式化日期为YYYY-MM-DD
		formatDate(date) {
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`;
		},
		// 重置表单数据
		resetGiftForm() {
			this.giftForm = {
				email: '',
				name: '',
				message: '',
				date: ''
			};
		},
		// 验证表单数据
		validateGiftForm() {
			// 如果不是礼品，则不需要验证
			if (!this.isGift) return true;

			// 邮箱验证
			const emailRegex = /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/;
			if (!emailRegex.test(this.giftForm.email)) {
				uni.showToast({
					title: 'Please enter a valid email',
					icon: 'none'
				});
				return false;
			}

			// 日期验证
			if (!this.giftForm.date) {
				uni.showToast({
					title: 'Please select a delivery date',
					icon: 'none'
				});
				return false;
			}


			return true;
		},
		// 获取礼品表单数据供父组件使用
		getGiftInfo() {
			return {
				isGift: this.isGift,
				giftData: this.isGift ? this.giftForm : null
			};
		}
	}
}
</script>

<style lang="scss" scoped>
.custom-popup-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 999;
}

.custom-popup-container {
	position: fixed;
	left: 0;
	right: 0;
	bottom: -100%;
	background-color: #fff;
	z-index: 1000;
	border-radius: 24rpx 24rpx 0 0;
	box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
	transition: all 0.5s ease-in-out;
	padding-bottom: env(safe-area-inset-bottom);
}

.custom-popup-container.active {
	bottom: 0;
}

.default-pad {
	padding: 30rpx;
	background-color: #FFFFFF;
	margin-bottom: 16rpx;
	border-radius: 16rpx;
}

.information-title {
	margin-bottom: 20rpx;
}

.gift-option {
	padding: 10rpx 0;
	margin-bottom: 15rpx;
}

.checkbox-label {
	color: #333;
	font-size: 28rpx;
	margin-left: 10rpx;
}

.gift-form {
	margin-top: 20rpx;
}

.form-item {
	margin-bottom: 19rpx;
	border-radius: 12rpx;
	border: 1px solid #E2E2E2;

	.u-input {
		padding: 0 15rpx !important;
	}
}

// .message-item {
// 	height: 140rpx;
// }

.date-item {
	padding: 0;
}

.date-picker {
	width: 100%;
	height: 80rpx;
	display: flex;
	align-items: center;
	padding: 0 20rpx;
	font-size: 28rpx;
	color: #333;
}

.date-icon {
	margin-right: 10rpx;
}

.placeholder {
	color: #999;
	display: inline-block;
	padding: 10rpx 0;
}

.date-text {
	display: inline-block;
	padding: 10rpx 0;
}

::v-deep .u-checkbox__icon-wrap {
	border-color: #FF5A1E !important;
}

::v-deep .u-input__content__field-wrapper {
	background-color: transparent;
	padding: 10rpx 0;
}

::v-deep .u-textarea {
	background-color: transparent;
	height: 100%;
}

::v-deep .u-textarea__field {
	height: 100%;
	background-color: transparent !important;
}
</style>
