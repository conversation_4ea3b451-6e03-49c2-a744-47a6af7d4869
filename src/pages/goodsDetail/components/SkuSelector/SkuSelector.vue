<template>
	<view class="sku-info-box">
		<!-- 规格选择器 - 仅在 IsCombination != '0' 时显示 -->
		<template v-if="isCombination !== '0' && skuData.attr && Object.keys(skuData.attr).length > 0">
			<uni-section class="uni-section-box" :title="item.Name_en" titleFontSize="16" titleColor="#262626"
				v-for="(item, indexW) in skuData.attr" :key="indexW">
				<view class="version-list">
					<block v-for="(cItem, indexN) in item.children" :key="indexN">
						<view v-if="cItem.pic"
							:class="['picTagItem', { 'on': cItem.selected === 1, 'disabled': cItem.disabled }]"
							@click="!cItem.disabled && handleSkuSelect(indexW, indexN)">
							<u--image :src="cItem.pic" :width="picW(cItem.selected)" :height="picH(cItem.selected)"
								radius="50%" duration="0"></u--image>
							<view v-if="cItem.disabled" class="disabled-overlay"></view>
						</view>
						<view v-else :class="['tagItem', { 'on': cItem.selected === 1, 'disabled': cItem.disabled }]"
							@click="!cItem.disabled && handleSkuSelect(indexW, indexN)">
							{{ cItem.name }}
						</view>
					</block>
				</view>
			</uni-section>
		</template>

		<!-- 数量选择器 -->
		<view class="Quantity-box">
			<view class="Quantity-left ">
				<text class="main-title" fontWeight="bold" fontSize="36rpx" color="#262626">Quantity </text>
				<u--text class="intro" v-if="finalStock === 0" color="#8C8C8C " fontSize="25rpx" fontWeight="bold"
					text="(out of stock)"></u--text>
				<u--text class="intro" v-else color="#8C8C8C " fontSize="25rpx" fontWeight="bold"
					:text="`(stock:${finalStock})`"></u--text>
			</view>
			<view class="Quantity-right">
				<uni-number-box background="#fff" v-model="currentQuantity" :width="50" :min="1"
					:max="Math.max(1, finalStock)" :disabled="finalStock === 0" @change="handleQuantityChange" />
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: "SkuSelector",
	props: {
		skuData: {
			type: Array | Object,
			default: () => ([])
		},
		goodsStock: {
			type: Number,
			default: 0
		},
		quantity: {
			type: [Number, String],
			default: 1
		},
		isCombination: {
			type: String,
			default: '1'
		},
		baseStock: {
			type: [Number, String],
			default: 0
		}
	},
	computed: {
		picW() {
			return (selected) => {
				return '40rpx';
			}
		},
		picH() {
			return (selected) => {
				return '40rpx';
			}
		},
		// 根据是否开启规格选择来决定使用哪个库存
		finalStock() {
			return this.isCombination === '0' ? this.baseStock : this.goodsStock;
		},
		// 当前数量，确保从1开始，并且不超过库存
		currentQuantity: {
			get() {
				// 确保数量至少为1，如果库存为0则显示1但禁用
				if (this.finalStock === 0) {
					return 1;
				}
				// 确保数量不超过库存，最小为1
				return Math.max(1, Math.min(this.quantity, this.finalStock));
			},
			set(value) {
				// 通过事件通知父组件数量变化
				this.handleQuantityChange(value);
			}
		}
	},
	methods: {
		handleSkuSelect(indexW, indexN) {
			this.$emit('sku-select', { indexW, indexN });
		},
		handleQuantityChange(value) {
			// 确保数量至少为1，且不超过库存
			const finalValue = Math.max(1, Math.min(value, this.finalStock));
			this.$emit('quantity-change', finalValue);
		}
	}
}
</script>

<style lang="scss" scoped>
.sku-info-box {
	padding: 38rpx 31rpx;

	.uni-section-box {
		margin-bottom: 31rpx;

		::v-deep .uni-section-header {
			padding: 0 !important;
			font-weight: bold;
		}

		.version-list {
			margin-top: 19rpx;
			display: flex;
			align-items: center;
			flex-wrap: wrap;
			@include flex-gap(15rpx); // 替换了 gap 双值
			box-sizing: border-box;
			grid-auto-flow: row; 
			grid-template-columns: repeat(auto-fit, max-content);
			.tagItem {
				width: auto;
				padding: 4rpx 20rpx;
				box-sizing: border-box;
				border-radius: 12rpx;
				font-size: 29rpx;
				color: #8C8C8C;
				border: 2rpx solid #E2E2E2;
				height: 60rpx;
				display: inline-flex;
				align-items: center;
				position: relative;

				&.on {
					color: #FFFFFF;
					background: #FF5A1E;
					border-color: #FF5A1E;
				}

				&.disabled {
					background-color: #CCCCCC;
					color: #fff;
					border-color: #E8E8E8;
					cursor: not-allowed;
					pointer-events: none;
				}
			}

			.picTagItem {
				padding: 12rpx;
				border-radius: 50%;
				box-sizing: border-box;
				border: 6rpx solid transparent;
				transition: border-color .3s;
				position: relative;

				&.on {
					border-color: #FF5A1E;
				}

				&.disabled {
					opacity: 0.3;
					pointer-events: none;

					.disabled-overlay {
						position: absolute;
						top: 0;
						left: 0;
						right: 0;
						bottom: 0;
						background: rgba(255, 255, 255, 0.6);
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;

						&::after {
							content: '';
							width: 60%;
							height: 2rpx;
							background: #CCCCCC;
							transform: rotate(45deg);
						}
					}
				}
			}
		}
	}

	.Quantity-box {
		display: flex;
		align-items: center;
		justify-content: space-between;

		.Quantity-left {
			display: flex;

			.main-title {
				font-size: 34rpx;
				font-weight: bold;
				color: #262626;
			}

			.intro {
				font-weight: 500;
				font-size: 25rpx;
				color: #8C8C8C;
				white-space: nowrap;
			}
		}

		.Quantity-right {
			::v-deep .uni-numbox__plus {
				.uni-numbox--text {
					color: #FF5A1E !important;
					font-weight: bold;
				}
			}

			::v-deep .uni-numbox--text {
				font-size: 47rpx;
			}

			// 库存为0时的禁用样式
			::v-deep .uni-numbox--disabled {
				.uni-numbox--text {
					color: #CCCCCC !important;
				}

				.uni-numbox__minus,
				.uni-numbox__plus {
					.uni-numbox--text {
						color: #CCCCCC !important;
					}
				}
			}
		}
	}
}
</style>