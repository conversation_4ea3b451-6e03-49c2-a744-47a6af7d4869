<template>
    <div class="share-root">
        <div id="share-container" ref="shareContainer" class="share-container">
            <div class="share-img-box">
                <img v-if="detail.pic_arr && detail.pic_arr.length > 0" :src="detail.pic_arr[0]" alt=""
                    class="share-img" crossorigin="anonymous">
            </div>
            <div class="share-info-box">
                <div class="share-info">
                    <div class="share-title">{{ truncatedTitle }}</div>
                    <div class="share-desc">{{ truncatedDesc }}</div>
                </div>
                <div class="share-price-box">
                    <div class="share-price">{{ priceSymbol + goodsPrice }}</div>
                </div>
            </div>
        </div>

        <div v-if="showShareImage">
            <img :src="shareImage" alt="Share Image" style="width:100%;">
        </div>
    </div>
</template>

<script>
import { generateShareImage, prepareImagesForCanvas } from '@/utils/ui/shareImageGenerator';

export default {
    name: 'ShareCanvas',
    props: {
        detail: {
            type: Object,
            default: () => ({})
        },
        goodsPrice: {
            type: String || Number,
            default: '0'
        },
        priceSymbol: {
            type: String,
            default: '$'
        }
    },
    data() {
        return {
            shareImage: '',
            showShareImage: false,
            isGenerating: false
        }
    },
    computed: {
        // 使用辅助函数处理标题文字截取
        truncatedTitle() {
            return this.truncateText(this.detail.pro?.Name_en, uni.upx2px(300)); // 200px 宽度限制
        },
        // 使用辅助函数处理描述文字截取
        truncatedDesc() {
            const desc = this.detail.pro?.BriefDescription_en || this.detail.pro?.Name_en || '';
            return this.truncateText(desc, uni.upx2px(420)); // 180px 宽度限制
        }
    },
    methods: {
        /**
         * 优化的文字截取函数 - 减少Canvas创建开销
         */
        truncateText(text, maxWidth, fontSize = '14px') {
            if (!text) return '';

            // 简化的字符宽度估算，避免频繁创建Canvas
            const avgCharWidth = fontSize === '14px' ? 8 : 10; // 估算平均字符宽度
            const maxChars = Math.floor(maxWidth / avgCharWidth);

            // 如果文本长度在安全范围内，直接返回
            if (text.length <= maxChars) {
                return text;
            }

            // 简单截取，为手机性能优化
            const truncateLength = Math.max(1, maxChars - 3); // 预留省略号空间
            return text.substring(0, truncateLength) + '...';
        },

        /**
         * 统一的图片生成方法
         */
        async getShareImage() {
            // 防止重复生成
            if (this.isGenerating) {
                console.log('正在生成中，请稍候...');
                return this.shareImage;
            }

            // 如果已有图片，直接返回
            if (this.shareImage) {
                return this.shareImage;
            }

            try {
                this.isGenerating = true;

                // 等待DOM渲染完成
                await this.$nextTick();

                // 获取容器元素，添加更多检查
                const element = document.getElementById('share-container');
                if (!element) {
                    console.error('DOM中找不到share-container元素');
                    // 尝试通过ref获取
                    const refElement = this.$refs.shareContainer;
                    if (!refElement) {
                        throw new Error('找不到分享容器元素，请检查DOM结构');
                    }
                    console.log('通过ref找到了元素，继续处理...');
                    // 使用ref元素继续处理
                    const shareImage = await generateShareImage(refElement, {
                        title: this.truncatedTitle,
                        description: this.truncatedDesc,
                        price: this.priceSymbol + this.goodsPrice
                    });
                    this.shareImage = shareImage;
                    return shareImage;
                }

                // 准备产品信息
                const productInfo = {
                    title: this.truncatedTitle,
                    description: this.truncatedDesc,
                    price: this.priceSymbol + this.goodsPrice
                };

                // 使用统一生成器
                this.shareImage = await generateShareImage(element, productInfo);
                return this.shareImage;

            } catch (error) {
                console.error('生成分享图片失败:', error);
                throw error;
            } finally {
                this.isGenerating = false;
            }
        },

        /**
         * 准备图片（设置跨域属性）
         */
        prepareImages() {
            const element = document.getElementById('share-container');
            if (element) {
                prepareImagesForCanvas(element);
            }
        }
    },

    mounted() {

    }
}
</script>

<style scoped lang="scss">
.share-root {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    position: absolute;
    top: -400px;
    left: 0;
    right: 0;
    bottom: 0;

    .share-container {
        background: #fff;
        border-radius: 12px;
        width: 100%;
        box-sizing: border-box;

        .share-img-box {
            width: 100%;
            display: flex;
            justify-content: center;

            .share-img {
                width: 100%;
                max-width: 288px;
                border-radius: 8px;
                object-fit: cover;
            }
        }

        .share-info-box {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 19rpx 30rpx 26rpx;

            .share-info {
                flex: 1 1 0;
                min-width: 0;
                margin-bottom: 10px;

                .share-title {
                    font-size: 35rpx;
                    font-weight: bold;
                    color: #262626;
                    margin-bottom: 9rpx;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .share-desc {
                    font-size: 31rpx;
                    color: #8C8C8C;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }

            .share-price-box {
                flex-shrink: 0;
                display: flex;
                justify-content: flex-end;
                margin-left: 28rpx;

                .share-price {
                    font-size: 46rpx;
                    color: #FF5A1E;
                    font-weight: bold;
                    margin-right: 32rpx;
                }
            }
        }
    }
}
</style>