<template>
	<view class="nav-bar" :class="{ fixed: isNavFixed }" :style="{
		opacity: navOpacity,
		transform: `translateY(${navTranslateY}px)`,
		top: statusBarAndNavHeight + 'px'
	}">
		<view v-for="(item, index) in tabList" :key="index" class="nav-item"
			:class="{ active: currentIndex === index }" @click="handleScrollToSection(index, item.anchor)">
			{{ item.name }}
		</view>
	</view>
</template>

<script>
export default {
	name: "ProductNavigation",
	props: {
		tabList: {
			type: Array,
			default: () => []
		},
		currentIndex: {
			type: Number,
			default: 0
		},
		isNavFixed: {
			type: Boolean,
			default: false
		},
		navOpacity: {
			type: Number,
			default: 0
		},
		navTranslateY: {
			type: Number,
			default: 0
		},
		statusBarAndNavHeight: {
			type: Number,
			default: 0
		}
	},
	methods: {
		handleScrollToSection(index, anchor) {
			this.$emit('scroll-to-section', { index, anchor });
		}
	}
}
</script>

<style lang="scss" scoped>
.nav-bar {
	display: flex;
	background-color: #fff;
	z-index: 100;
	width: 100%;
	transition: all 0.3s ease;
	box-shadow: 0px 2rpx 4rpx 0px rgba(0, 0, 0, 0.1);
	position: fixed;

	.nav-item {
		flex: 1;
		text-align: center;
		height: 102rpx;
		line-height: 102rpx;
		color: #262626;
		font-size: 16px;
		font-weight: bold;
		transition: all 0.2s ease;
		font-family: PingFang SC-Bold;

		&.active {
			color: #FF5A1E;
			position: relative;

			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 30px;
				height: 2px;
				border-radius: 28rpx;
				background-color: #FF5A1E;
			}
		}
	}
}
</style> 