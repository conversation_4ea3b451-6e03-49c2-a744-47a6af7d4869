<template>
	<view class="default-pad" v-if="showPackageBuy && items.length > 0">
		<view class="flex align-center justify-between" style="margin-bottom: 21rpx;">
			<u--text :text="packageTitle" color="#262626" size="33rpx" bold></u--text>
			<u--text :text="savingsText" color="#8C8C8C" size="31rpx" bold align="right"></u--text>
		</view>

		<view class="cart-goods-box">
			<checkbox-group class="checkbox-group-box" @change="handleCheckboxChange">
				<view class="cart-item" v-for="(item, index) in items" :key="item.PId || item.ProId || index">
					<view class="cart-item-box">


						<view class="cart-item-main">

							<view class="product-info">
								<view class="cart-item-left">
									<checkbox :value="item.CId" :checked="item.checked" class="checkbox-flg"
										v-bind="checkboxObj" @click="checkboxChange(item, index)" />
								</view>
								<view class="product-info-img">
									<u--image :src="item.PicPath_0" width="185rpx" height="133rpx"
										mode="widthFix"></u--image>
								</view>
								<view class="product-details">
									<view>
										<u--text :text="item.Name" lines="1" color="#262626" bold size="30rpx"
											@click="handleSkuSelect(item, index)"></u--text>
									</view>

									<!-- 主商品规格选择 -->
									<view class="sku-selector-box">
										<view v-if="item.attr && Object.keys(item.attr).length > 0"
											:class="['sku-selector', item.popData ? 'sku-selector--selected' : 'sku-selector--unselected']"
											@click="handleSkuSelect(item, index)">
											<view class="sku-content">
												<template v-if="item.popData">
													<view v-for="(vid, attrId) in item.popData.Attr" :key="'m' + attrId"
														class="sku-attr-row">
														<u--text lines="1" :text="handleSkuName(item, attrId, vid)"
															size="26rpx" :color="'#8C8C8C'" />
													</view>
												</template>
												<template v-else>
													<u--text text="Please select a specification" size="26rpx"
														:color="'#FF5A1E'" lines="1" />
												</template>
											</view>
											<u-icon name="arrow-down" size="11"
												:color="item.popData ? '#8C8C8C' : '#FF5A1E'" />
										</view>

									</view>

								</view>
							</view>
							<view class="price-section">
								<u--text
									:text="`${priceSymbol}${isPackageBuy && item.popData ? item.popData.ItemPrice : item.CurPrice || '0.00'}`"
									color="#FF5A1E" bold size="30rpx"></u--text>
								<span style="margin-right: 10rpx;"></span>
								<u--text :text="`${priceSymbol}${item.OldPrice || '0.00'}`" color="#8C8C8C" size="26rpx"
									decoration="line-through"></u--text>
							</view>
							<view class="sub-product-check" @click="subCheckboxChange(index, subIndex)"
								style="padding-left:50rpx;">
								<view class="circle-plus">
									<u-icon name="plus" size="25" color="#C0C0C0" />
								</view>
							</view>
							<!-- 子商品列表 -->
							<view v-if="item.pro && item.pro.length > 0" class="sub-products">
								<view v-for="(subItem, subIndex) in item.pro" :key="subIndex"
									class="sub-product-item-box">
									<view
										:class="['sub-product-item', subItem.checked ? 'sub-product-item--selected' : '']">
										<!-- 左侧+号/勾选 -->
										<!-- <view class="sub-product-check" @click="subCheckboxChange(index, subIndex)">
										<view class="circle-plus">
											<u-icon name="plus" size="20" color="#C0C0C0" />
										</view>
									</view> -->
										<!-- 勾选框 -->
										<checkbox
											:style="{ visibility: packageType === 'combination-promotion' ? 'hidden' : 'visible' }"
											:value="subItem.CId" :checked="subItem.checked" class="checkbox-flg"
											v-bind="checkboxObj" @click.stop="subCheckboxChange(index, subIndex)" />
										<u--image :src="subItem.PicPath_0" width="150rpx" height="92rpx" radius="10rpx"
											mode="widthFix"></u--image>
										<view class="sub-product-info">
											<view>
												<u--text :text="subItem.Name_en" lines="1" color="#262626" bold
													size="28rpx"></u--text>
											</view>
											<!-- 规格选择按钮 -->
											<view class="sku-selector-box">


												<view v-if="subItem.attr && Object.keys(subItem.attr).length > 0"
													:class="['sku-selector', subItem.popData ? 'sku-selector--selected' : 'sku-selector--unselected']"
													@click="handleSubSkuSelect(subItem, index, subIndex)">
													<view class="sku-content">
														<template v-if="subItem.popData">
															<view v-for="(vid, attrId) in subItem.popData.Attr"
																:key="'s' + attrId" class="sku-attr-row">
																<u--text lines="1"
																	:text="handleSkuName(subItem, attrId, vid)"
																	size="24rpx" :color="'#8C8C8C'" />
															</view>
														</template>
														<template v-else>
															<u--text text="Please select a specification" size="24rpx"
																:color="'#FF5A1E'" lines="1" />
														</template>
													</view>
													<u-icon name="arrow-down" size="14"
														:color="subItem.popData ? '#8C8C8C' : '#FF5A1E'" />
												</view>
											</view>
										</view>
									</view>
									<!-- 价格区块 -->
									<view class="sub-product-price">
										<view style="display:flex;">
											<u--text
												:text="`${priceSymbol}${isPackageBuy && subItem.popData ? subItem.popData.ItemPrice : subItem.Price || '0.00'}`"
												color="#FF5A1E" bold size="30rpx"></u--text>
											<span style="margin-right: 10rpx;">

											</span>
											<u--text :text="`${priceSymbol}${subItem.OldPrice || '0.00'}`"
												color="#8C8C8C" size="26rpx" decoration="line-through"></u--text>
										</view>
									</view>
								</view>
							</view>

						</view>

					</view>
					<view style="padding:34rpx 30rpx 0;" v-if="index !== items.length - 1">
						<u-gap height="1" bgColor="#F7F7F7"></u-gap>
					</view>

				</view>
			</checkbox-group>
		</view>
		<addPopup ref="addPopupRef" :isCart="false" :initSelectedSku="selectedSku" @config="handleConfigureChange"
			class="add-package-popup">
		</addPopup>
	</view>
</template>

<script>
import { productDetail } from '@/api/home.js';
import { PriceCalculator } from '@/pages/goodsDetail/utils/skuManager.js';

export default {
	name: "PackageBuy",
	components: {
	},
	props: {
		showPackageBuy: {
			type: Boolean,
			default: false
		},
		items: {
			type: Array,
			default: () => []
		},
		priceSymbol: {
			type: String,
			default: "$"
		},
		checkboxObj: {
			type: Object,
			default: () => ({
				activeBackgroundColor: '#FF5A1E',
				activeBorderColor: '#FF5A1E',
				iconColor: '#fff'
			})
		},
		packageType: {
			type: String,
			default: 'package-buy' // 'package-buy' 或 'combination-promotion'
		}
	},
	data() {
		return {
			selectedSku: {},
			currentProductForSku: null,
			storeItemIndex: { mainIndex: null, subIndex: null },
		};
	},
	computed: {
		isPackageBuy() {
			return this.packageType !== 'combination-promotion';
		},
		// 动态标题
		packageTitle() {
			return this.packageType === 'combination-promotion' ? 'Package Promotion' : 'Package-buy';
		},

		// 计算节省金额（高精度，统计所有 checked 商品，乘以数量）
		totalSelectedPrice() {
			if (this.packageType === 'combination-promotion') {
				// 只统计父项被选中的CurPrice
				let total = 0;
				this.items.forEach(item => {
					if (item.checked) {
						total = PriceCalculator.add(total, Number(item.CurPrice || 0));
					}
				});
				return total;
			}
			let total = 0;
			this.items.forEach(item => {
				// 主商品 checked，累加 规格价*数量 或 当前价*数量
				if (item.checked) {
					const qty = Number(item.Qty) || 1;
					const price = this.getItemPrice(item);
					total = PriceCalculator.add(total, PriceCalculator.multiply(price, qty));
				}
				// 子商品 checked，累加 规格价*数量 或 当前价*数量
				if (item.pro && Array.isArray(item.pro)) {
					item.pro.forEach(subItem => {
						if (subItem.checked) {
							const qty = Number(subItem.Qty) || 1;
							const price = this.getSubItemPrice(subItem);
							total = PriceCalculator.add(total, PriceCalculator.multiply(price, qty));
						}
					});
				}
			});
			return total;
		},
		// 节省金额文本（高精度格式化）
		savingsText() {
			const savings = this.totalSelectedPrice;
			if (savings > 0) {
				return `Save ${this.priceSymbol}${PriceCalculator.roundToString(savings, 2)}`;
			}
			return `Save ${this.priceSymbol}0`;
		}
	},
	methods: {
		getItemPrice(item) {
			if (this.isPackageBuy && item.popData && item.popData.ItemPrice) {
				return Number(item.popData.ItemPrice);
			}
			return Number(item.CurPrice || 0);
		},
		getSubItemPrice(subItem) {
			if (this.isPackageBuy && subItem.popData && subItem.popData.ItemPrice) {
				return Number(subItem.popData.ItemPrice);
			}
			return Number(subItem.Price || 0);
		},
		handleCheckboxChange(event) {
			// 复制一份数据，更新checked状态
			const newItems = JSON.parse(JSON.stringify(this.items));
			const checkedValues = event.detail.value;
			newItems.forEach(item => {
				item.checked = checkedValues.includes(item.CId);
				if (item.pro && Array.isArray(item.pro)) {
					item.pro.forEach(subItem => {
						subItem.checked = checkedValues.includes(subItem.CId);
					});
				}
			});
			this.$emit('updateItems', newItems);
		},
		async openSkuPopup(product, img) {
			const res = await productDetail({ ProId: product.ProId });
			this.selectedSku = product?.popData?.selectedSku || {}
			this.$refs.addPopupRef.isAddCartShow = true;
			this.$refs.addPopupRef.productImg = img;
			this.$refs.addPopupRef.productDetailData = res;
			this.currentProductForSku = res;
			this.$refs.addPopupRef.numberValue = product.Qty || 1;
		},
		handleSkuSelect(item, mainIndex) {
			// 获取当前已选规格
			this.storeItemIndex = { mainIndex: mainIndex, subIndex: null };
			const img = item.PicPath || (item.pro && item.pro[0] && item.pro[0].PicPath_0) || '';
			this.openSkuPopup(item, img);

		},
		handleSubSkuSelect(subItem, mainIndex, subIndex) {
			console.log("🚀 ~ file: PackageBuy.vue:250 ~  mainIndex, subIndex:", mainIndex, subIndex)
			this.storeItemIndex = { mainIndex: mainIndex, subIndex: subIndex };
			console.log("🚀 ~ file: PackageBuy.vue:251 ~ this.storeItemIndex :", this.storeItemIndex)
			const img = subItem.PicPath || subItem.PicPath_0 || '';
			this.openSkuPopup(subItem, img);
		},

		handleConfigureChange(data) {
			let { mainIndex, subIndex } = this.storeItemIndex;
			// 深拷贝，避免直接改 props
			const newItems = JSON.parse(JSON.stringify(this.items));
			// 生成规格名
			if (subIndex == undefined) {
				// 主商品
				if (!newItems[mainIndex].Data) newItems[mainIndex].Data = {};
				newItems[mainIndex].popData = data

			} else {
				// 子商品
				newItems[mainIndex].pro[subIndex].popData = data
				// 只要有子商品选中，主商品也选中
				newItems[mainIndex].checked = true;
			}
			this.$emit('updateItems', newItems);
		},
		handleSkuName(item, attrId, vid) {
			const attrObj = item?.popData?.skuData?.attr;
			const attrGroup = attrObj?.[attrId];
			if (!attrGroup) return '';

			const matched = attrGroup.children.find(child => child.vid == vid);
			if (!matched) return '';

			return `${attrGroup.Name_en}: ${matched.name}`;
		},
		// 删除无用方法和冗余逻辑，只保留下标定位、item.popData 相关、单组添加购物车、规格校验、规格名值映射等必要逻辑
		// 已删除：syncSelectedSkuToRes、selectedSkuMap、updateProductAttrSelected、getSkuName、所有批量相关方法


		// 主商品选择状态改变
		checkboxChange(item, index) {
			console.log('主商品选择状态改变:', item, index);

			// 复制一份数据
			const newItems = JSON.parse(JSON.stringify(this.items));

			// 更新选中状态
			newItems[index].checked = !newItems[index].checked;

			// 组合促销：父项选中，所有子项都选中/取消
			if (this.packageType === 'combination-promotion' && newItems[index].pro) {
				newItems[index].pro.forEach((subItem, subIndex) => {
					newItems[index].pro[subIndex].checked = newItems[index].checked;
				});
			}
			// 其他类型保持原逻辑
			else if (!newItems[index].checked && newItems[index].pro) {
				newItems[index].pro.forEach((subItem, subIndex) => {
					newItems[index].pro[subIndex].checked = false;
				});
			}

			// 触发更新事件
			this.$emit('updateItems', newItems);
		},



		// 子商品选择状态改变
		subCheckboxChange(mainIndex, subIndex) {
			// 复制一份数据
			const newItems = JSON.parse(JSON.stringify(this.items));
			const subItem = newItems[mainIndex].pro[subIndex];
			newItems[mainIndex].pro[subIndex].checked = !subItem.checked;

			// 如果选中了子商品，确保主商品也被选中
			if (newItems[mainIndex].pro[subIndex].checked) {
				newItems[mainIndex].checked = true;
			} else {
				// 如果所有子商品都未选中，取消主商品选中
				const hasCheckedSubItems = newItems[mainIndex].pro.some(sub => sub.checked);
				if (!hasCheckedSubItems) {
					newItems[mainIndex].checked = false;
				}
			}

			this.$emit('updateItems', newItems);
		},

		// 回显规格名
		getSkuName(item, attrId) {
			return (item.Data && item.Data.sku_name && item.Data.sku_name[attrId]) || '-';
		},
		// 新增：只允许一组一组添加购物车，所有数据只用 item.popData
		/**
		 * 添加当前主商品或子商品到购物车
		 * @param {Object} item - 当前主商品或子商品对象（含 popData）
		 * @param {String} type - 'main' 或 'sub'，用于区分主/子商品
		 * @param {Number} mainIndex - 主商品下标
		 * @param {Number} [subIndex] - 子商品下标（可选）
		 */
		async addCurrentGroupToCart(item, type, mainIndex, subIndex) {
			if (!item || !item.popData) {
				this.$toast('数据异常，请刷新重试');
				return;
			}
			// 规格校验
			if (item.popData.skuData && item.popData.skuData.attr) {
				const attr = item.popData.skuData.attr;
				const selected = item.popData.Attr;
				const unselected = Object.keys(attr).filter(attrId => !selected[attrId]);
				if (unselected.length > 0) {
					this.$toast(`请先选择：${unselected.map(id => attr[id].Name_en || attr[id].Name).join('、')}`);
					return;
				}
			}
			// 参数组装（严格按接口文档）
			const params = {
				ProId: item.popData.ProId,
				PId: item.popData.PId,
				ExtAttr: item.popData.ExtAttr,
				products_type: item.popData.products_type,
				back: item.popData.back,
				Qty: item.popData.Qty || 1
			};
			try {
				// 只允许一组一组添加，直接调接口
				const { packageAddToCart } = await import('@/api/products.js');
				await packageAddToCart(params);
				this.$toast('加入购物车成功');
				// 可选：更新购物车数量、状态等
			} catch (e) {
				this.$toast('加入购物车失败');
			}
		},
	}
}
</script>

<style lang="scss" scoped>
.default-pad {
	padding: 38rpx 31rpx;
}

.checkbox-flg {
	align-self: center;

	::v-deep .uni-checkbox-input {
		border-radius: 50% !important;
		background: #F0F0F0;
		border-color: transparent !important;
	}

	::v-deep .uni-checkbox-input-checked {
		border-radius: 50% !important;
		background-color: #FF5A1E !important;
		border-color: transparent !important;
	}
}

.add-package-popup {
	::v-deep .Quantity-right {
		display: none;
	}
}

.cart-goods-box {
	border-radius: 31rpx;
	border: 1rpx solid rgba(0, 0, 0, 0.1);
	overflow: hidden;
	padding: 30rpx;

	.checkbox-group-box {
		display: flex;
		flex-direction: column;
	}

	.cart-item {
		background: #fff;

		.cart-item-box {
			display: flex;
			align-items: flex-start;

			.cart-item-left {
				display: flex;
				align-items: center;

				::v-deep .uni-checkbox-input {
					margin-right: 0;
					outline: none !important;
					border: 2rpx solid transparent !important;
					border-radius: 50%;
					width: 37rpx;
					height: 37rpx;
				}

				::v-deep .uni-checkbox-input-checked {
					border: 2rpx solid #FF5A1E !important;
					background-color: #FF5A1E !important;
				}
			}

			.cart-item-main {
				flex: 1;
				display: flex;
				flex-direction: column;

				.product-info {
					padding: 30rpx 0 0;
				}

				.sub-products {
					padding-left: 50rpx;
				}

				.product-info,
				.sub-products {
					display: flex;

					&-img {
						margin-left: 7rpx;
					}

					.sub-product-item-box {
						display: flex;
						flex-direction: column;
						// align-items: flex-end;
						justify-content: space-between;

					}

					::v-deep .u-image {
						height: auto !important;

						.u-image__image {
							margin: auto;
						}
					}

					.product-details,
					.sub-product-info {
						flex: 1;
						display: flex;
						flex-direction: column;
						// align-self: self-start;
						margin-left: 21rpx;

						.sku-selector-box {
							flex: 1;
							display: flex;
							align-items: center;
						}

						.sku-selector {
							display: flex;
							align-items: center;
							justify-content: space-between;
							background: #F5F5F5;
							border: 1rpx solid #E0E0E0;
							cursor: pointer;
							transition: all 0.2s;
							border-radius: 12rpx;
							width: 262rpx;
							padding: 0 11rpx;
							box-sizing: border-box;
							min-height: 54rpx;
							padding: 9rpx;

							.sku-content {
								flex: 1;

								.sku-attr-row {
									margin-bottom: 2rpx;

									&:last-child {
										margin-bottom: 0;
									}
								}
							}
						}

						.sku-selector--unselected {
							border: 2rpx solid #FF5A1E !important;
							background: #fff !important;
							color: #FF5A1E !important;

							u--text {
								white-space: nowrap;
								overflow: hidden;
								text-overflow: ellipsis;
								max-width: 100%; // 防止超出
								display: block; // 或 inline-block
							}
						}

						.sku-selector--selected {
							border: 1rpx solid #E0E0E0 !important;
							background: #F0F0F0 !important;
							color: #8C8C8C !important;
						}

						.sku-info {
							margin-top: 10rpx;

							view {
								margin: 4rpx 0;
							}
						}
					}
				}

				.sub-products {
					display: flex;
					flex-direction: column;

					.sub-product-item {
						position: relative;
						display: flex;

						padding: 34rpx 0rpx 0;
						border-radius: 10rpx;
						border: 1rpx solid transparent;
						transition: border 0.2s, background 0.2s;

						.checkbox-flg {
							::v-deep .uni-checkbox-input {
								width: 27rpx;
								height: 27rpx;
							}

						}

						.circle-plus {
							width: 36rpx;
							height: 36rpx;
							display: flex;
							align-items: center;
							justify-content: center;
						}

						.circle-checked {
							width: 36rpx;
							height: 36rpx;
							border-radius: 50%;
							background: #FF5A1E;
							display: flex;
							align-items: center;
							justify-content: center;
						}

						.sub-product-info {
							flex: 1;
							display: flex;
							flex-direction: column;
							margin-left: 26rpx;
						}


					}

					.sub-product-price {
						display: flex;
						flex: 1;
						justify-content: flex-end;

						.u-text {
							justify-content: flex-end !important;
						}
					}
				}
			}

			.price-section {
				display: flex;
				flex-direction: row;
				align-items: center;
				align-self: flex-end; // 只占内容高度
				// min-width: 120rpx; // 去掉撑满宽度
				flex: 1;

				.u-text {
					justify-content: flex-end !important;
				}
			}
		}
	}
}
</style>