<template>
	<u-count-down :time="time" format="DD:HH:mm:ss" @change="onChange">
		<view class="time">
			<u--text :text="countDownTime" size="29rpx" bold color="#fff"></u--text>
		</view>
	</u-count-down>
</template>

<script>
	export default {
		name: 'count-down',
		props: {
			time: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				timeData: {}
			}
		},
		computed: {
			countDownTime() {
				let time = this.timeData;
				const days = time?.days;
				const hours = time?.hours > 10 ? time?.hours : '0' + time?.hours;
				const minutes = time?.minutes > 10 ? time?.minutes : '0' + time?.minutes;
				const seconds = time?.seconds > 10 ? time?.seconds : '0' + time?.seconds;
				return `${days}days  ${hours}:${minutes}:${seconds}`;
			}
		},
		methods: {
			onChange(e) {
				this.timeData = e
			}
		}
	}
</script>

<style>
</style>