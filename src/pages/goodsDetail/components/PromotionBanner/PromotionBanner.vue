<template>
	<view class="goods-type-box" v-if="(productsType == 2 || productsType == 6) && !is3DViewActive">
		<view class="favorite-box" @click="handleAddFavorite">
			<img v-if="isFavorite != 1" src="@/static/assets/common/<EMAIL>" alt="">
			<img v-if="isFavorite == 1" src="@/static/assets/common/<EMAIL>" alt="">
		</view>
		<view class="share-box" @click="handleShare">
			<img src="@/static/assets/common/<EMAIL>" alt="">
		</view>

		<view class="content-box">
			<!-- 限时秒杀 -->
			<view class="time-kill" v-if="productsType == 2">
				<view class="time-kill-top">
					<view class="limit-time">
						<u--image src="/static/assets/mine/countdown.png" width="33rpx" height="33rpx"
							style="margin-right: 4rpx;"></u--image>
						<count-down :time="discountEndtime"></count-down>
					</view>
				</view>

				<view class="time-kill-bottom">
					<u-parse :content="killContent"></u-parse>
				</view>

				<u--image class="flash_sale" src="/static/assets/mine/flash_sale.png" mode="aspectFit"
					width="130rpx" height="130rpx"></u--image>
			</view>

			<!-- 限时促销 -->
			<view class="limit-time-promotion" v-if="productsType == 6">
				<view class="time-kill-top">
					<view class="limit-time">
						<u--image src="/static/assets/mine/countdown.png" width="33rpx" height="33rpx"
							style="margin-right: 4rpx;"></u--image>
						<count-down :time="discountEndtime"></count-down>
					</view>
				</view>

				<view class="time-kill-bottom">
					<u-parse :content="killContent"></u-parse>
				</view>

				<u--image class="coupon_limit_bg" src="/static/assets/mine/coupon_limit_bg.png" mode="aspectFit"
					width="130rpx" height="130rpx"></u--image>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: "PromotionBanner",
	components: {
		CountDown: () => import('./CountDown.vue'),
	},
	props: {
		productsType: {
			type: Number,
			default: 0
		},
		is3DViewActive: {
			type: Boolean,
			default: false
		},
		isFavorite: {
			type: [Number, String],
			default: 0
		},
		discountEndtime: {
			type: Number,
			default: 0
		},
		killContent: {
			type: String,
			default: ""
		}
	},
	methods: {
		handleAddFavorite() {
			this.$emit('add-favorite');
		},
		handleShare() {
			this.$emit('share');
		}
	}
}
</script>

<style lang="scss" scoped>
.goods-type-box {
	position: relative;
	height: 244rpx;
	margin-bottom: -40rpx;
	background: #FF5A1E;
	border-radius: 31rpx 31rpx 0 0;
	box-sizing: border-box;
	padding: 15rpx 31rpx 0;

	.favorite-box {
		position: absolute;
		right: 112rpx;
		top: -20rpx;
		width: 60rpx;
		height: 60rpx;
		background: #FFFFFF;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;

		img {
			width: 130%;
		}

		.iconfont {
			font-size: 40rpx;
		}

		.icon-aixin {
			color: #B5B5B5;
		}

		.icon-aixin_shixin {
			color: #FF4949;
		}
	}

	.share-box {
		position: absolute;
		right: 31rpx;
		top: -20rpx;
		width: 60rpx;
		height: 60rpx;
		background: #FFFFFF;
		box-shadow: 0px 8rpx 8rpx 0px rgba(0, 0, 0, 0.15);
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;

		.icon-fenxiang {
			font-size: 36rpx;
			color: #FFC149;
		}
	}

	.content-box {
		width: 100%;
		height: 179rpx;
		background: linear-gradient(180deg, #f95911 0%, #ff824b 100%);
		border-radius: 0 0 31rpx 31rpx;

		.time-kill,
		.limit-time-promotion {
			width: 100%;
			height: 100%;
			padding: 23rpx 67rpx 17rpx 52rpx;
			box-sizing: border-box;
			position: relative;
			display: flex;
			flex-direction: column;
			justify-content: space-between;

			.time-kill-top {
				width: 100%;
				display: inline-flex;
				justify-content: space-between;
				align-items: center;
  @include flex-gap(0, 38rpx); // 替换了 column-gap
				box-sizing: border-box;

				.limit-time {
					width: fit-content;
					height: 48rpx;
					border-radius: 38rpx;
					border: 2rpx solid #FFFFFF;
					display: flex;
					align-items: center;
					padding: 0 14rpx;
				}

				.extra-price {
					display: inline-flex;
					align-items: flex-end;
				}
			}

			.time-kill-bottom {
				display: flex;
				color: #fff;
			}

			.flash_sale {
				position: absolute;
				right: 67rpx;
				bottom: 23rpx;
			}

			.coupon_limit_bg {
				position: absolute;
				right: 77rpx;
				bottom: 29rpx;
			}
		}
	}
}
</style> 