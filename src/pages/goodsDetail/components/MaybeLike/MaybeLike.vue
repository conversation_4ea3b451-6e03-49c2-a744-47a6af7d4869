<template>
	<view class="maybeLike-box" v-if="maybeLikeList.length > 0">
		<view class="flex align-center justify-between maybeLikeTitle">
			<u--text text="You may also like" color="#262626" size="33rpx" bold></u--text>
			<!-- <image src="@/static/assets/common/arrow_next.png" style="width: 31rpx; height: 31rpx;">
			</image> -->
		</view>

		<scroll-view scroll-x>
			<view class="maybe-like-list">
				<view v-for="(item, index) in maybeLikeList" :key="index" class="maybe-like-item"
					@click="handleItemClick(item)">
					<u--image :src="item.PicPath_0" width="173rpx" height="173rpx" radius="8rpx"
						duration="0"></u--image>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	name: "Maybe<PERSON><PERSON>",
	props: {
		maybeLikeList: {
			type: Array,
			default: () => []
		}
	},
	methods: {
		handleItemClick(item) {
			this.$emit('item-click', item);
		}
	}
}
</script>

<style lang="scss" scoped>
.maybeLike-box {
	padding: 38rpx 31rpx;

	.maybeLikeTitle {
		margin-bottom: 21rpx;
	}

	.maybe-like-list {
		display: flex;
		@include flex-gap(15rpx); // 替换了 gap 单值
		padding-bottom: 10rpx;

		.maybe-like-item {
			flex-shrink: 0;
			cursor: pointer;

			&:hover {
				opacity: 0.8;
			}
		}
	}
}
</style>