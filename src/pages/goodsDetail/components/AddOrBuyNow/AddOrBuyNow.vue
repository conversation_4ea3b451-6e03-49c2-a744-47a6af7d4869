<template>
	<view class="payment-button">
		<!-- <u-button class="amount" text="+2Add" :loading="isAddLoading" @click="$emit('handleAddToCart')"></u-button> -->
		<view class="amount" :loading="isAddLoading" @click="$emit('handleAddToCart')">
			<!-- <u-icon name="plus" color="#FF5A1E" bold></u-icon> -->
			<img :src="plusIcon" alt="">
			Add
		</view>

		<view class="payment-btn" :loading="isPaymentLoading" @click="$emit('handlePayment')">{{ subText }}</view>
	</view>
</template>

<script>
import {
	mapGetters
} from 'vuex'
import plusIcon from "../../../../static/assets/common/increase.png";
export default {
	name: "AddOrBuyNow",
	props: {
		Price: {
			type: [String, Number],
			default: "0.00"
		},
		isPaymentLoading: {
			type: <PERSON>olean,
			default: false
		},
		isAddLoading: {
			type: Boolean,
			default: false
		},
		subText: {
			type: String,
			default: "Payment"
		}
	},
	data() {
		return {
			plusIcon
		};
	},
	computed: {
		...mapGetters(['priceSymbol'])
	},
	methods: {

	}
}
</script>

<style lang="scss" scoped>
.payment-button {
	position: relative;
	width: 477rpx;
	height: 85rpx;
	max-width: 477rpx;
	display: flex;
	align-items: center;
	background: #FF5A1E;
	border-radius: 69rpx 69rpx 69rpx 69rpx;
	overflow: hidden;

	&::before {
		position: absolute;
		top: 0;
		left: 0;
		border: 2rpx solid #FF5A1E;
		width: 100%;
		height: 100%;
		box-sizing: border-box;
		content: '';
		border-radius: 69rpx 69rpx 69rpx 69rpx;
		pointer-events: none;

	}

	.amount {
		width: 194rpx;
		height: 85rpx;
		background: #FBDED1;
		border-radius: 0 88rpx 4rpx 0rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-weight: bold;
		font-size: 38rpx;
		color: #FF5A1E;

		img {
			width: 37rpx;
			height: 37rpx;
			max-width: 37rpx;
			margin: 0;
			vertical-align: middle;
		}

	}

	.payment-btn {
		background: transparent;
		border: none;
		font-weight: 600;
		font-size: 38rpx;
		color: #FFFFFF;

		height: 85rpx;
		border-radius: 69rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		flex: 1;
	}
}
</style>