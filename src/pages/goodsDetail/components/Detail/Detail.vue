<template>
	<view>
		<!-- Detail -->
		<view id="Detail" class="detail-section default-pad" v-if="prodDetailInfo">
			<view class="flex align-center justify-between" style="margin-bottom: 21rpx;">
				<u--text text="Detail" color="#262626" size="33rpx" bold></u--text>
			</view>
		</view>
		<view v-else style="height:150rpx"></view>
		<!-- 这里可以根据需要添加详情内容的展示 -->
		<!-- <view v-html="prodDetailInfo"></view> -->
	</view>
</template>

<script>
export default {
	name: "Detail",
	props: {
		prodDetailInfo: {
			type: String,
			default: ""
		}
	}
}
</script>

<style lang="scss" scoped>
.detail-section {
	padding: 38rpx 31rpx;
}

.default-pad {
	padding: 38rpx 31rpx;
}
</style> 