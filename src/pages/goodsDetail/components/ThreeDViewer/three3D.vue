<template>
	<view class="three-container">
		<!-- 使用新的3D组件，传递所有必要的参数 -->
		<three3D ref="three3DComponent" 
			 :modelUrl="currentModelUrl"
			:loadKTX2Texture="loadKTX2Texture"
			:threeInitEnvUrl="modeResult.threerglbUrl"
			:backgroundIndex="currentBackgroundIndex" :colorConfig="currentColorConfig" :autoLoad="true"
			@load-success="handleLoadSuccess" @load-error="handleLoadError"
			@background-changed="handleBackgroundChanged" @background-error="handleBackgroundError"
			@custom-background-loaded="handleCustomBackgroundLoaded"
			@custom-background-error="handleCustomBackgroundError" />
	</view>
</template>

<script>
import three3D from './3d.vue'
import { KTX2Loader } from 'three/examples/jsm/loaders/KTX2Loader.js';

export default {
	name: 'ThreeDViewer',
	components: {
		three3D
	},
	props: {
		// 模型文件URL
		modeFile: {
			type: String,
			default: ''
		},
		modeResult: {
			type: Object,
			default: () => ({})
		},
		// 背景索引
		backgroundIndex: {
			type: Number,
			default: 2
		},
		// 颜色配置对象
		colorConfig: {
			type: Object,
			default: () => ({})
		},
		// 是否自动加载
		autoLoad: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			// 当前模型URL
			currentModelUrl: this.modeFile || 'https://binyo.net/u_file/2502/glb/06/039347d599.glb',
			// 当前背景索引
			currentBackgroundIndex: this.backgroundIndex,
			// 当前颜色配置
			currentColorConfig: { ...this.colorConfig },
			// 3D组件是否准备好
			isViewerReady: false,
			// 存储当前截图的base64
			currentScreenshot: ''
		}
	},
	watch: {
		// 监听模型文件变化
		modeFile: {
			handler(newUrl) {
				if (newUrl) {
					this.currentModelUrl = newUrl;
				}
			},
			immediate: true
		},

		// 监听背景索引变化
		backgroundIndex: {
			handler(newIndex) {
				this.currentBackgroundIndex = newIndex;
			},
			immediate: true
		},

		// 监听颜色配置变化
		colorConfig: {
			handler(newConfig) {
				this.currentColorConfig = { ...newConfig };
			},
			deep: true,
			immediate: true
		}
	},
	mounted() {

		// 测试KTX2Loader
		this.testKTX2Loader();

		// 初始化状态
		this.isViewerReady = false;
		this.currentModelUrl = this.modeFile || 'https://binyo.net/u_file/2502/glb/06/039347d599.glb';
		this.currentBackgroundIndex = this.backgroundIndex;
		this.currentColorConfig = { ...this.colorConfig };

		console.log('ThreeDViewer 初始化完成');
	},

	beforeDestroy() {
		// 清理工作会由3D组件自己处理
		console.log('ThreeDViewer destroyed');
	},
	methods: {
		// 添加KTX2纹理加载方法，供外部调用
		loadKTX2Texture(ktx2Url, renderer, callback) {
			try {
				// 如果未初始化，则初始化
				if (!this.ktx2Loader) {
					const loader = new KTX2Loader();

					// 设置transcoder路径
					loader.setTranscoderPath('http://api.binyo.net/u_file/3d/libs/basis/');

					// 检测WebGL支持
					if (renderer) {
						console.log("🚀 ~ file: three3D.vue:119 ~ renderer:", renderer)
						loader.detectSupport(renderer);
					} else {
						console.warn('未传入 renderer，KTX2Loader 可能无法正确识别支持情况');
					}

					// 设置worker限制
					loader.setWorkerLimit(2);

					this.ktx2Loader = loader;
					console.log('KTX2Loader 初始化完成');
				}

				// 加载纹理
				this.ktx2Loader.load(
					ktx2Url,
					(texture) => {
						console.log('KTX2纹理加载成功:', ktx2Url);
						// 设置基本纹理属性
						texture.flipY = false;
						callback && callback(texture, null);
					},
					(progress) => {
						console.log('KTX2加载进度:', progress);
					},
					(error) => {
						console.error('KTX2纹理加载失败:', error);
						callback && callback(null, error);
					}
				);

			} catch (error) {
				console.error('KTX2加载器调用失败:', error);
				this.ktx2Loader = null;
				callback && callback(null, error);
			}
		},

		// 测试KTX2Loader是否可用
		testKTX2Loader() {
			console.log('测试KTX2Loader...');
			try {
				if (typeof KTX2Loader !== 'undefined') {
					console.log('KTX2Loader 可用');
					return true;
				} else {
					console.warn('KTX2Loader 不可用');
					return false;
				}
			} catch (error) {
				console.error('KTX2Loader 测试失败:', error);
				return false;
			}
		},
		// 改变部件颜色 - 父组件调用的公共方法
		changePartColor(componentName, optionData) {
			if (!this.isViewerReady) {
				console.warn('3D Viewer not ready, cannot change color.');
				return;
			}

			// 更新颜色配置
			this.currentColorConfig = {
				...this.currentColorConfig,
				[componentName]: optionData
			};

			console.log('Changing part color:', componentName, optionData);
		},

		// 切换背景（已禁用，只使用GLB环境）
		changeBackground(backgroundIndex) {
			console.log('原有背景功能已禁用，只使用GLB环境背景');
			// this.currentBackgroundIndex = backgroundIndex;
			// console.log('Changing background to index:', backgroundIndex);
		},

		// 加载新模型
		loadModel(modelUrl) {
			this.currentModelUrl = modelUrl;
			console.log('Loading new model:', modelUrl);
		},

		// 获取3D组件引用（用于直接调用3D组件方法）
		get3DComponent() {
			return this.$refs.three3DComponent;
		},

		// 3D组件事件处理方法
		handleLoadSuccess(data) {
			console.log('3D Model loaded successfully:', data);
			this.isViewerReady = true;
			this.$emit('load-success', data);
			this.$emit('viewer-ready'); // 通知父组件3D查看器已准备好
		},

		handleLoadError(error) {
			console.error('Failed to load 3D model:', error);
			this.isViewerReady = false;
			this.$emit('load-error', error);
		},

		handleBackgroundChanged(background) {
			console.log('Background changed to:', background.name);
			this.$emit('background-changed', background);
		},

		handleBackgroundError(data) {
			console.error('Background loading failed:', data);
			this.$emit('background-error', data);
		},

		// 处理自定义背景加载成功事件
		handleCustomBackgroundLoaded(data) {
			console.log('Custom background loaded successfully:', data);
			this.$emit('custom-background-loaded', data);
		},

		// 处理自定义背景加载失败事件
		handleCustomBackgroundError(data) {
			console.error('Custom background loading failed:', data);
			this.$emit('custom-background-error', data);
		},

		// 切换到GLB+KTX2背景模式
		switchToGLBBackground() {
			const three3DComponent = this.$refs.three3DComponent;
			if (three3DComponent && three3DComponent.switchToGLBBackground) {
				three3DComponent.switchToGLBBackground();
			}
		},

		// 移除GLB环境
		removeGLBEnvironment() {
			const three3DComponent = this.$refs.three3DComponent;
			if (three3DComponent && three3DComponent.removeGLBEnvironment) {
				three3DComponent.removeGLBEnvironment();
			}
		},

		// 固定侧面截图（主要功能）
		captureScreenshot(options = {}) {
			console.log("🚀 开始截图，传入的选项:", options);
			return new Promise((resolve, reject) => {
				if (!this.isViewerReady) {
					console.warn('3D Viewer not ready, cannot capture screenshot.');
					reject(new Error('3D viewer not ready'));
					return;
				}

				const three3DComponent = this.$refs.three3DComponent;
				if (!three3DComponent) {
					reject(new Error('3D component reference not found'));
					return;
				}

				// 使用固定的左侧面角度，WebP格式
				const screenshotOptions = {
					...options,
					format: 'image/webp',
					quality: options.quality || 0.8,
					// 添加水印元素配置
					logo: typeof options.logo === 'string' ? {
						url: options.logo,
						position: 'top-left',  // 改为左上角
						size: 0.35,  // 增大尺寸
						opacity: 1
					} : options.logo ? {
						...options.logo,
						position: options.logo.position || 'top-left',  // 默认改为左上角
						size: options.logo.size || 0.25,  // 默认增大尺寸
						opacity: options.logo.opacity ||1
					} : null,
					qrCode: options.qrCode || null,
					title: options.title ? {
						text: options.title,
						fontSize: options.titleStyle?.fontSize || 24,
						color: options.titleStyle?.color || '#fff',
						opacity: options.titleStyle?.opacity || 1,
						shadow: options.titleStyle?.shadow !== false
					} : null
				};

				console.log("🚀 处理后的截图选项:", screenshotOptions);
				console.log("🎨 Logo配置:", screenshotOptions.logo);

				three3DComponent.captureScreenshotWithElements(screenshotOptions)
					.then((result) => {
						console.log("✅ 截图成功:", {
							width: result.width,
							height: result.height,
							hasLogo: !!screenshotOptions.logo
						});
						this.currentScreenshot = result.dataURL;
						resolve(result);
					})
					.catch((error) => {
						console.error("❌ 截图失败:", error);
						reject(error);
					});
			});
		},

		// 获取当前截图
		async getCurrentScreenshot(params) {
			return await this.$refs.three3DComponent.takeScreenshot(true, params)
		},

		// 下载截图
		downloadScreenshot(filename = 'screenshot.webp') {
			if (!this.currentScreenshot) {
				console.warn('No screenshot available to download');
				return false;
			}

			try {
				// 创建下载链接
				const link = document.createElement('a');
				link.download = filename;
				link.href = this.currentScreenshot;

				// 触发下载
				document.body.appendChild(link);
				link.click();
				document.body.removeChild(link);

				console.log('Screenshot download initiated');
				return true;
			} catch (error) {
				console.error('Screenshot download failed:', error);
				return false;
			}
		}
	}
}
</script>

<style scoped>
.three-container {
	width: 100%;
	height: 100vw;
}
</style>
