<template>
	<!-- #ifdef APP-PLUS || H5 -->
	<view class="three-viewer-container">
		<view id="threeView" class="three-canvas"></view>
		
		<!-- Loading Progress Bar -->
		<view v-if="showLoading" class="loading-container">
			<view class="loading-bar-container">
				<view class="loading-bar" :style="{ width: loadingProgress + '%' }"></view>
				<view class="loading-text">{{ loadingText }}</view>
			</view>
		</view>
	</view>
	<!-- #endif -->
	<!-- #ifndef APP-PLUS || H5 -->
	<view>非 APP、H5 环境不支持</view>
	<!-- #endif -->
</template>

<script module="three" lang="renderjs">
	import * as THREE from 'static/three';
	
	import {
		OrbitControls
	} from 'static/three/examples/jsm/controls/OrbitControls.js'

	import {
		GLTFLoader
	} from 'static/three/examples/jsm/loaders/GLTFLoader.js'
	
	import {
		DRACOLoader
	} from 'static/three/examples/jsm/loaders/DRACOLoader.js'
	
	import {
		RGBELoader
	} from 'static/three/examples/jsm/loaders/RGBELoader.js'

	var renderer;
	var scene;
	var camera;
	var controls;
	var model;
	var gltfLoader;

	export default {
		props: {
			modelUrl: {
				type: String,
				default: 'https://binyo.net/u_file/2502/glb/06/039347d599.glb'
			},
			backgroundIndex: {
				type: Number,
				default: 2
			},
			colorConfig: {
				type: Object,
				default: () => ({})
			},
			autoLoad: {
				type: Boolean,
				default: true
			}
		},
		
		data() {
			return {
				showLoading: false,
				loadingProgress: 0,
				loadingText: 'Loading 0%',
				// 按需渲染相关状态
				needsRender: false,
				isRendering: false,
				renderTimeout: null,
				lastRenderTime: 0,
				frameRate: 60,
				isUserInteracting: false,
				interactionTimeout: null,
				backgrounds: [
					{
						id: 1,
						name: "Studio",
						url: "https://dl.polyhaven.org/file/ph-assets/HDRIs/hdr/1k/studio_small_09_1k.hdr"
					},
					{
						id: 2,
						name: "Garden", 
						url: "https://dl.polyhaven.org/file/ph-assets/HDRIs/hdr/1k/pretoria_gardens_1k.hdr"
					},
					{
						id: 3,
						name: "Sunset",
						url: "https://dl.polyhaven.org/file/ph-assets/HDRIs/hdr/1k/sunset_fairway_1k.hdr"
					}
				]
		}
	},

	mounted() {
			this.initThree();
			this.setupLoaders();
			this.createControls();
			this.loadInitialBackground();
			
			if (this.autoLoad) {
				this.loadModel(this.modelUrl);
			}
		},

		watch: {
			modelUrl(newUrl) {
				if (newUrl) {
					this.loadModel(newUrl);
				}
			},
			
			backgroundIndex(newIndex) {
				this.changeBackground(newIndex);
			},
			
			colorConfig: {
				handler(newConfig) {
					if (newConfig && Object.keys(newConfig).length > 0) {
						this.applyColorConfig(newConfig);
					}
				},
				deep: true
			}
		},

		methods: {
			initThree() {
				console.log('初始化Three.js场景');

				scene = new THREE.Scene();

				const container = document.getElementById('threeView');
				const width = container.clientWidth || window.innerWidth;
				const height = container.clientHeight || window.innerHeight;

				camera = new THREE.PerspectiveCamera(60, width / height, 0.1, 1000);
				camera.position.set(5, 5, 5);
				camera.lookAt(0, 1, 0);

				renderer = new THREE.WebGLRenderer({ 
					antialias: true,
					alpha: true,
					preserveDrawingBuffer: true
				});
				renderer.setPixelRatio(window.devicePixelRatio);
				renderer.setSize(width, height);
				renderer.outputEncoding = THREE.sRGBEncoding;
				renderer.toneMapping = THREE.ACESFilmicToneMapping;
				renderer.toneMappingExposure = 1.2;

				container.appendChild(renderer.domElement);

				const ambientLight = new THREE.AmbientLight(0xffffff, 1.5);
				scene.add(ambientLight);

				const pointLight = new THREE.PointLight(0xffffff, 1.2);
				pointLight.position.set(5, 10, 5);
				scene.add(pointLight);

				// 添加定向光增加整体亮度
				const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
				directionalLight.position.set(-5, 10, 5);
				scene.add(directionalLight);

				this.markNeedsRender();

				window.addEventListener('resize', this.onWindowResize, false);
			},

			setupLoaders() {
				const dracoLoader = new DRACOLoader();
				dracoLoader.setDecoderPath('static/three/examples/jsm/libs/draco/');
				dracoLoader.preload();
				
				gltfLoader = new GLTFLoader();
				gltfLoader.setDRACOLoader(dracoLoader);
				
				console.log('DRACO加载器配置完成');
			},

			createControls() {
				controls = new OrbitControls(camera, renderer.domElement);
				controls.target.set(0, 1, 0);
				controls.enableDamping = true;
				controls.dampingFactor = 0.08;
				controls.screenSpacePanning = false;
				controls.minDistance = 2;
				controls.maxDistance = 50;
				controls.minPolarAngle = 0;
				controls.maxPolarAngle = Math.PI * 0.45;
				
				// 添加控制器事件监听以支持按需渲染
				controls.addEventListener('start', () => {
					this.startInteractiveRender();
				});
				
				controls.addEventListener('change', () => {
					if (!this.isUserInteracting) {
						this.markNeedsRender();
					}
				});
				
				controls.addEventListener('end', () => {
					this.stopInteractiveRender();
				});
				
				controls.update();
			},

			loadInitialBackground() {
				// 不加载原来的HDR背景，只加载GLB环境背景
				// this.changeBackground(this.backgroundIndex);
				this.loadGLBEnvironment();
			},

			// 加载GLB环境背景
			loadGLBEnvironment() {
				console.log('开始加载GLB环境背景...');
				
				// 清除原有的背景和环境
				scene.background = null;
				scene.environment = null;
				
				// 加载环境GLB文件
				gltfLoader.load('https://binyo.net/static/themes/default/products/3d/assets/init_env.glb', (gltf) => {
					console.log('环境GLB加载成功');
					
					// 将环境GLB添加到场景
					const envScene = gltf.scene;
					envScene.name = 'environment';
					
					// 设置环境模型的材质属性
					envScene.traverse((child) => {
						if (child.isMesh) {
							// 设置为不投射阴影，不接收阴影
							child.castShadow = false;
							child.receiveShadow = false;
							
							// 如果有材质，设置为环境材质
							if (child.material) {
								child.material.transparent = true;
								child.material.opacity = 1.0; // 设置为完全不透明
							}
						}
					});
					
					// 将环境添加到场景
					scene.add(envScene);
					
					console.log('GLB环境背景设置完成，已移除原有场景');
					this.$emit('custom-background-loaded', {
						type: 'glb-env',
						envModel: envScene
					});
					
					// 环境加载完成后需要重新渲染
					this.markNeedsRender();
					
				}, (progress) => {
					if (progress.lengthComputable) {
						const percentComplete = (progress.loaded / progress.total * 100);
						console.log('环境GLB加载进度:', percentComplete.toFixed(1) + '%');
					}
				}, (error) => {
					console.error('环境GLB加载失败:', error);
					this.$emit('custom-background-error', { type: 'glb-env', error });
				});
			},

			// 移除GLB环境
			removeGLBEnvironment() {
				const envObject = scene.getObjectByName('environment');
				if (envObject) {
					scene.remove(envObject);
					console.log('GLB环境已移除');
					return true;
				}
				return false;
			},

			// 切换到GLB背景模式
			switchToGLBBackground() {
				// 移除现有环境
				this.removeGLBEnvironment();
				
				// 重新加载GLB环境
				this.loadGLBEnvironment();
			},

			loadModel(url = this.modelUrl) {
				console.log('开始加载模型:', url);
				
				if (model) {
					scene.remove(model);
					this.disposeModel(model);
					model = null;
				}

				this.showLoading = true;
				this.loadingProgress = 0;
				this.loadingText = 'Loading 0%';

				gltfLoader.load(
					url,
					(gltf) => {
						console.log('模型加载成功');
						model = gltf.scene;
					scene.add(model);

					this.adjustModelTransform(model);
					
					// 模型加载完成后需要重新渲染
					this.markNeedsRender();

					this.loadingProgress = 100;
					this.loadingText = 'Load complete';
					
					setTimeout(() => {
						this.showLoading = false;
						this.$emit('load-success', { model: gltf });
					}, 1000);
					},
					(xhr) => {
						if (xhr.lengthComputable) {
							const percentComplete = (xhr.loaded / xhr.total) * 100;
							this.loadingProgress = percentComplete;
							this.loadingText = `Loading ${percentComplete.toFixed(0)}%`;
						}
					},
					(error) => {
						console.error('模型加载失败:', error);
						
						if (error.message && error.message.includes('DRACO')) {
							console.log('DRACO解码失败，尝试不使用DRACO重新加载...');
							this.loadModelWithoutDraco(url);
							return;
						}
						
						this.loadingText = 'Load failed';
						setTimeout(() => {
							this.showLoading = false;
							this.$emit('load-error', error);
						}, 1500);
					}
				);
			},

			loadModelWithoutDraco(url) {
				console.log('使用标准GLTF加载器重新加载模型...');
				
				const standardLoader = new GLTFLoader();
				
				standardLoader.load(
					url,
					(gltf) => {
						console.log('标准模型加载成功');
					model = gltf.scene;
					scene.add(model);

					this.adjustModelTransform(model);
					
					// 模型加载完成后需要重新渲染
					this.markNeedsRender();

					this.loadingProgress = 100;
					this.loadingText = 'Load complete';
					
					setTimeout(() => {
						this.showLoading = false;
						this.$emit('load-success', { model: gltf });
					}, 1000);
					},
					(xhr) => {
						if (xhr.lengthComputable) {
							const percentComplete = (xhr.loaded / xhr.total) * 100;
							this.loadingProgress = percentComplete;
							this.loadingText = `Loading ${percentComplete.toFixed(0)}%`;
						}
					},
					(error) => {
						console.error('标准模型加载也失败:', error);
						this.loadingText = 'Load failed';
						setTimeout(() => {
							this.showLoading = false;
							this.$emit('load-error', error);
						}, 1500);
					}
				);
			},

			adjustModelTransform(model) {
				const box = new THREE.Box3().setFromObject(model);
				const size = box.getSize(new THREE.Vector3());
				const center = box.getCenter(new THREE.Vector3());
				const maxDim = Math.max(size.x, size.y, size.z);
				
				const scale = 10 / maxDim;
				model.scale.set(scale, scale, scale);
				
				model.position.sub(center.multiplyScalar(scale));
				model.position.y += (size.y / 2) * scale;

				const finalBox = new THREE.Box3().setFromObject(model);
				const finalCenter = finalBox.getCenter(new THREE.Vector3());
				const finalSize = finalBox.getSize(new THREE.Vector3());
				const newMaxDim = Math.max(finalSize.x, finalSize.y, finalSize.z);

				controls.target.copy(finalCenter);

				const cameraDistance = newMaxDim * 1.2;
				camera.position.set(
					finalCenter.x,
					finalCenter.y,
					finalCenter.z + cameraDistance * 0.8
				);
				camera.lookAt(finalCenter);
				controls.update();
				
				// 模型变换调整后需要重新渲染
				this.markNeedsRender();
			},

			changeBackground(backgroundIndex) {
				const background = this.backgrounds.find(bg => bg.id === backgroundIndex);
				if (!background) {
					console.warn(`背景未找到: ${backgroundIndex}`);
					return;
				}

				console.log(`切换背景到: ${background.name}`);
				
				const rgbeLoader = new RGBELoader();
				rgbeLoader.load(
					background.url,
					(texture) => {
						texture.mapping = THREE.EquirectangularReflectionMapping;
						scene.environment = texture;
						scene.background = texture;
						console.log(`背景切换成功: ${background.name}`);
						// 背景切换后需要重新渲染
						this.markNeedsRender();
						this.$emit('background-changed', background);
					},
					undefined,
					(error) => {
						console.error(`背景加载失败: ${background.name}`, error);
						this.$emit('background-error', { background, error });
					}
				);
			},

			applyColorConfig(config) {
				if (!model) {
					console.warn('模型未加载，无法应用颜色配置');
					return;
				}

				Object.keys(config).forEach(componentName => {
					const optionData = config[componentName];
					this.setMaterialColor(componentName, optionData);
				});
				
				// 颜色配置应用后需要重新渲染
				this.markNeedsRender();
			},

			setMaterialColor(componentName, optionData) {
				if (!model) return;

				let found = false;
				model.traverse((child) => {
					if (child.isMesh && child.name === componentName) {
						found = true;
						
						if (!child.material.isMeshStandardMaterial && !child.material.isMeshPhysicalMaterial) {
							return;
						}

						if (!child.material.userData._independent) {
							child.material = child.material.clone();
							child.material.userData._independent = true;
						}

						this.autoGenerateUV(child);

						if (optionData.ColorChartlet || optionData.PicPath) {
							this.applyTexture(child, optionData.ColorChartlet || optionData.PicPath);
						} else if (optionData.optionColor) {
							this.applySolidColor(child, optionData.optionColor);
						}
					}
				});

				if (!found) {
					console.warn(`组件未找到: ${componentName}`);
				}
			},

			applyTexture(mesh, textureUrl) {
				const loader = new THREE.TextureLoader();
				loader.load(textureUrl, (texture) => {
					texture.wrapS = THREE.ClampToEdgeWrapping;
					texture.wrapT = THREE.ClampToEdgeWrapping;
					texture.repeat.set(1, 1);
					texture.offset.set(0, 0);
					texture.flipY = false;
					texture.encoding = THREE.sRGBEncoding;
					texture.needsUpdate = true;

					if (mesh.material.map && mesh.material.map.dispose) {
						mesh.material.map.dispose();
					}

					mesh.material.map = texture;
					mesh.material.color.set(0xffffff);
					mesh.material.roughness = 0.6;
					mesh.material.metalness = 0.1;
					mesh.material.needsUpdate = true;
					
					// 纹理应用后需要重新渲染
					this.markNeedsRender();
				});
			},

			applySolidColor(mesh, colorValue) {
				if (mesh.material.map && mesh.material.map.dispose) {
					mesh.material.map.dispose();
				}
				
				mesh.material.map = null;
				mesh.material.color.set(colorValue);
				mesh.material.roughness = 0.4;
				mesh.material.metalness = 0.7;
				mesh.material.needsUpdate = true;
				
				// 颜色应用后需要重新渲染
				this.markNeedsRender();
			},

			autoGenerateUV(mesh, type = "auto") {
				if (mesh.geometry && !mesh.geometry.attributes.uv) {
					const pos = mesh.geometry.attributes.position.array;
					const uv = [];
					
					for (let i = 0; i < pos.length; i += 3) {
						const x = pos[i], y = pos[i + 1], z = pos[i + 2];
						let u = 0, v = 0;
						
						if (type === "tire" || (type === "auto" && mesh.name.includes("tire"))) {
							const theta = Math.atan2(z, x);
							u = (theta + Math.PI) / (2 * Math.PI);
							v = y * 0.5 + 0.5;
						} else if (type === "frame" || (type === "auto" && mesh.name.includes("frame"))) {
							u = x * 0.5 + 0.5;
							v = z * 0.5 + 0.5;
						} else if (type === "seat" || (type === "auto" && mesh.name.includes("seat"))) {
							u = x * 0.5 + 0.5;
							v = y * 0.5 + 0.5;
						} else {
							u = y * 0.5 + 0.5;
							v = z * 0.5 + 0.5;
						}
						uv.push(u, v);
					}
					
					mesh.geometry.setAttribute('uv', new THREE.BufferAttribute(new Float32Array(uv), 2));
					mesh.geometry.attributes.uv.needsUpdate = true;
				}
			},

			disposeModel(model) {
				model.traverse((child) => {
					if (child.geometry) {
						child.geometry.dispose();
					}
					if (child.material) {
						if (Array.isArray(child.material)) {
							child.material.forEach((m) => m.dispose());
						} else {
							child.material.dispose();
						}
					}
				});
			},

			onWindowResize() {
				const container = document.getElementById('threeView');
				const width = container.clientWidth || window.innerWidth;
				const height = container.clientHeight || window.innerHeight;
				
				camera.aspect = width / height;
				camera.updateProjectionMatrix();
				renderer.setSize(width, height);
				
				// 窗口大小改变后需要重新渲染
				this.markNeedsRender();
			},

			// 按需渲染系统
			requestRender() {
				if (!this.needsRender && !this.isRendering) {
					this.needsRender = true;
					requestAnimationFrame(() => this.render());
				}
			},

			render() {
				if (!this.needsRender) return;
				
				this.isRendering = true;
				this.needsRender = false;
				
				const now = performance.now();
				const deltaTime = now - this.lastRenderTime;
				const targetFrameTime = 1000 / this.frameRate;
				
				if (deltaTime >= targetFrameTime) {
					if (controls) {
						controls.update();
					}
					
					if (renderer && scene && camera) {
						renderer.render(scene, camera);
					}
					
					this.lastRenderTime = now;
				}
				
				this.isRendering = false;
			},

			markNeedsRender() {
				this.requestRender();
			},

			startInteractiveRender() {
				this.isUserInteracting = true;
				this.frameRate = 60; // 交互时使用高帧率
				this.interactiveRenderLoop();
			},

			interactiveRenderLoop() {
				if (!this.isUserInteracting) return;
				
				this.markNeedsRender();
				requestAnimationFrame(() => this.interactiveRenderLoop());
			},

			stopInteractiveRender() {
				this.isUserInteracting = false;
				this.frameRate = 30; // 非交互时使用低帧率
				
				// 延迟停止渲染，确保最后的状态被渲染
				if (this.interactionTimeout) {
					clearTimeout(this.interactionTimeout);
				}
				this.interactionTimeout = setTimeout(() => {
					this.markNeedsRender(); // 最后渲染一次
				}, 100);
			},

			// 兼容旧的animate方法调用
			animate() {
				console.warn('animate()方法已被按需渲染系统替代，请使用markNeedsRender()');
				this.markNeedsRender();
			},

			loadNewModel(url) {
				this.loadModel(url);
			},

			switchBackground(index) {
				this.changeBackground(index);
			},

			applyColor(componentName, optionData) {
				this.setMaterialColor(componentName, optionData);
			},

			// 截图功能（支持添加标题到右下角）
			captureScreenshot(options = {}) {
				return new Promise((resolve, reject) => {
					try {
						if (!renderer || !scene || !camera) {
							reject(new Error('3D场景未初始化'));
							return;
						}

						// 截图选项
						const {
							width = renderer.domElement.width,
							height = renderer.domElement.height,
							format = 'image/webp',
							quality = 0.9,
							preserveDrawingBuffer = false,
							title = '', // 标题文字
							titleStyle = {} // 标题样式
						} = options;

						// 如果需要保持绘图缓冲区，需要重新创建渲染器
						if (preserveDrawingBuffer && !renderer.preserveDrawingBuffer) {
							console.warn('当前渲染器不支持preserveDrawingBuffer，截图可能为空');
						}

						// 确保场景已渲染
						renderer.render(scene, camera);

						// 获取canvas并转换为base64
						const canvas = renderer.domElement;
						
						// 如果有标题，在canvas上添加标题
						let finalDataURL;
						if (title && title.trim()) {
							finalDataURL = this.addTitleToCanvas(canvas, title, titleStyle, format, quality);
						} else {
							finalDataURL = canvas.toDataURL(format, quality);
						}

						if (finalDataURL === 'data:,') {
							reject(new Error('截图失败：canvas为空'));
							return;
						}

						resolve({
							dataURL: finalDataURL,
							width: canvas.width,
							height: canvas.height,
							format: format,
							timestamp: Date.now(),
							title: title || null
						});

					} catch (error) {
						console.error('截图过程中发生错误:', error);
						reject(error);
					}
				});
			},

			// 在canvas上添加标题到右下角
			addTitleToCanvas(sourceCanvas, title, titleStyle = {}, format = 'image/webp', quality = 0.9) {
				// 创建新的canvas用于绘制标题
				const newCanvas = document.createElement('canvas');
				const ctx = newCanvas.getContext('2d');
				
				// 设置新canvas尺寸与原canvas相同
				newCanvas.width = sourceCanvas.width;
				newCanvas.height = sourceCanvas.height;
				
				// 先绘制原始图片
				ctx.drawImage(sourceCanvas, 0, 0);
				
				// 设置标题样式
				const style = {
					fontSize: titleStyle.fontSize || Math.max(16, Math.floor(sourceCanvas.width / 40)),
					fontFamily: titleStyle.fontFamily || 'Arial, sans-serif',
					color: titleStyle.color || '#ffffff',
					backgroundColor: titleStyle.backgroundColor || 'rgba(0, 0, 0, 0.6)',
					padding: titleStyle.padding || 12,
					borderRadius: titleStyle.borderRadius || 8,
					margin: titleStyle.margin || 20
				};
				
				// 设置字体
				ctx.font = `${style.fontSize}px ${style.fontFamily}`;
				ctx.textAlign = 'right';
				ctx.textBaseline = 'bottom';
				
				// 测量文字尺寸
				const textMetrics = ctx.measureText(title);
				const textWidth = textMetrics.width;
				const textHeight = style.fontSize;
				
				// 计算背景框的位置和尺寸
				const bgWidth = textWidth + style.padding * 2;
				const bgHeight = textHeight + style.padding * 2;
				const bgX = newCanvas.width - bgWidth - style.margin;
				const bgY = newCanvas.height - bgHeight - style.margin;
				
				// 绘制背景框
				if (style.backgroundColor !== 'transparent') {
					ctx.fillStyle = style.backgroundColor;
					if (style.borderRadius > 0) {
						// 绘制圆角矩形
						this.drawRoundedRect(ctx, bgX, bgY, bgWidth, bgHeight, style.borderRadius);
					} else {
						ctx.fillRect(bgX, bgY, bgWidth, bgHeight);
					}
				}
				
				// 绘制文字
				ctx.fillStyle = style.color;
				ctx.fillText(
					title, 
					newCanvas.width - style.margin - style.padding, 
					newCanvas.height - style.margin - style.padding
				);
				
				// 返回带标题的图片数据
				return newCanvas.toDataURL(format, quality);
			},

			// 绘制圆角矩形
			drawRoundedRect(ctx, x, y, width, height, radius) {
				ctx.beginPath();
				ctx.moveTo(x + radius, y);
				ctx.lineTo(x + width - radius, y);
				ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
				ctx.lineTo(x + width, y + height - radius);
				ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
				ctx.lineTo(x + radius, y + height);
				ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
				ctx.lineTo(x, y + radius);
				ctx.quadraticCurveTo(x, y, x + radius, y);
				ctx.closePath();
				ctx.fill();
			},

			// 高质量截图（重新渲染指定尺寸）
			captureHighQualityScreenshot(options = {}) {
				return new Promise((resolve, reject) => {
					try {
						if (!scene || !camera) {
							reject(new Error('3D场景未初始化'));
							return;
						}

						const {
							width = 1920,
							height = 1080,
							format = 'image/webp',
							quality = 1.0,
							antialias = true
						} = options;

						// 创建临时渲染器用于高质量截图
						const tempRenderer = new THREE.WebGLRenderer({
							antialias: antialias,
							preserveDrawingBuffer: true,
							alpha: true
						});

						tempRenderer.setSize(width, height);
						tempRenderer.setPixelRatio(1); // 使用标准像素比
						tempRenderer.outputEncoding = THREE.sRGBEncoding;
						tempRenderer.toneMapping = THREE.ACESFilmicToneMapping;
						tempRenderer.toneMappingExposure = 1.2;

						// 临时调整相机宽高比
						const originalAspect = camera.aspect;
						camera.aspect = width / height;
						camera.updateProjectionMatrix();

						// 渲染到临时canvas
						tempRenderer.render(scene, camera);

						// 获取截图数据
						const canvas = tempRenderer.domElement;
						const dataURL = canvas.toDataURL(format, quality);

						// 恢复相机原始宽高比
						camera.aspect = originalAspect;
						camera.updateProjectionMatrix();

						// 清理临时渲染器
						tempRenderer.dispose();

						if (dataURL === 'data:,') {
							reject(new Error('高质量截图失败：canvas为空'));
							return;
						}

						resolve({
							dataURL: dataURL,
							width: width,
							height: height,
							format: format,
							timestamp: Date.now(),
							isHighQuality: true
						});

					} catch (error) {
						console.error('高质量截图过程中发生错误:', error);
						reject(error);
					}
				});
			},

			// 从不同角度截图
			captureFromAngle(angle = 'front', options = {}) {
				return new Promise((resolve, reject) => {
					try {
						if (!camera || !controls || !model) {
							reject(new Error('3D场景或模型未准备好'));
							return;
						}

						// 保存当前相机状态
						const originalPosition = camera.position.clone();
						const originalTarget = controls.target.clone();

						// 计算模型中心和边界
						const box = new THREE.Box3().setFromObject(model);
						const center = box.getCenter(new THREE.Vector3());
						const size = box.getSize(new THREE.Vector3());
						const maxDim = Math.max(size.x, size.y, size.z);
						
						// 根据模型大小调整距离，确保模型完整显示
						const distance = maxDim * 1.2;
						
						// 设置相机高度为模型中心高度，确保水平视角
						const cameraHeight = center.y;

						// 设置不同角度的相机位置（固定角度）
						let newPosition;
						switch (angle) {
							case 'front':
								// 正面：从Z轴正方向看
								newPosition = new THREE.Vector3(center.x, cameraHeight, center.z + distance);
								break;
							case 'back':
								// 背面：从Z轴负方向看
								newPosition = new THREE.Vector3(center.x, cameraHeight, center.z - distance);
								break;
							case 'left':
								// 左侧面：从X轴负方向看（模型的左侧）
								newPosition = new THREE.Vector3(center.x - distance, cameraHeight, center.z);
								break;
							case 'right':
								// 右侧面：从X轴正方向看（模型的右侧）
								newPosition = new THREE.Vector3(center.x + distance, cameraHeight, center.z);
								break;
							case 'left-side':
								// 标准左侧面截图：确保完全侧面视角
								newPosition = new THREE.Vector3(center.x - distance, cameraHeight, center.z);
								break;
							case 'right-side':
								// 标准右侧面截图：确保完全侧面视角
								newPosition = new THREE.Vector3(center.x + distance, cameraHeight, center.z);
								break;
							case 'top':
								// 顶部：从Y轴正方向看
								newPosition = new THREE.Vector3(center.x, center.y + distance, center.z);
								break;
							case 'bottom':
								// 底部：从Y轴负方向看
								newPosition = new THREE.Vector3(center.x, center.y - distance, center.z);
								break;
							case 'front-right':
								// 右前方：45度角
								newPosition = new THREE.Vector3(
									center.x + distance * 0,
									cameraHeight + distance * 0.2,
									center.z + distance * 0.707
								);
								break;
							case 'front-left':
								// 左前方：45度角
								newPosition = new THREE.Vector3(
									center.x - distance * 0.707,
									cameraHeight + distance * 0.2,
									center.z + distance * 0.707
								);
								break;
							case 'profile-left':
								// 标准左侧轮廓截图（完全侧面，稍微从上方）
								newPosition = new THREE.Vector3(
									center.x - distance,
									cameraHeight + distance * 0.1,
									center.z
								);
								break;
							case 'profile-right':
								// 标准右侧轮廓截图（完全侧面，稍微从上方）
								newPosition = new THREE.Vector3(
									center.x + distance,
									cameraHeight + distance * 0.1,
									center.z
								);
								break;
							default:
								newPosition = originalPosition.clone();
						}

						// 设置相机位置和目标
						camera.position.copy(newPosition);
						camera.lookAt(center);
						controls.target.copy(center);
						controls.update();
						
						// 触发渲染以确保新视角被渲染
						this.markNeedsRender();

						// 等待一帧确保渲染完成
						requestAnimationFrame(() => {
							// 进行截图
							this.captureScreenshot(options).then((result) => {
								// 恢复原始相机状态
								camera.position.copy(originalPosition);
								controls.target.copy(originalTarget);
								camera.lookAt(originalTarget);
								controls.update();
								
								// 恢复相机状态后需要重新渲染
								this.markNeedsRender();

								resolve({
									...result,
									angle: angle
								});
							}).catch((error) => {
								// 恢复原始相机状态
								camera.position.copy(originalPosition);
								controls.target.copy(originalTarget);
								camera.lookAt(originalTarget);
								controls.update();
								
								// 恢复相机状态后需要重新渲染
								this.markNeedsRender();

								reject(error);
							});
						});

					} catch (error) {
						console.error('角度截图过程中发生错误:', error);
						reject(error);
					}
				});
			}
		},

		beforeDestroy() {
			window.removeEventListener('resize', this.onWindowResize);
			
			// 清理按需渲染相关的定时器
			if (this.renderTimeout) {
				clearTimeout(this.renderTimeout);
				this.renderTimeout = null;
			}
			
			if (this.interactionTimeout) {
				clearTimeout(this.interactionTimeout);
				this.interactionTimeout = null;
			}
			
			// 停止交互式渲染
			this.stopInteractiveRender();
			
			if (model) {
				this.disposeModel(model);
			}
			
			if (renderer) {
				renderer.dispose();
			}
		}
	}
</script>

<style scoped>
.three-viewer-container {
	position: relative;
	width: 100%;
	height: 100%;
}

.three-canvas {
	width: 100%;
	height: 100%;
}

.loading-container {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: rgba(240, 240, 240, 0.8);
	z-index: 10;
}

.loading-bar-container {
	position: relative;
	width: 300px;
	height: 20px;
	background: #ddd;
	border-radius: 10px;
	overflow: hidden;
}

.loading-bar {
	height: 100%;
	background: #4caf50;
	transition: width 0.3s ease;
}

.loading-text {
	position: absolute;
	width: 100%;
	text-align: center;
	top: 0;
	line-height: 20px;
	font-size: 14px;
	color: #333;
	user-select: none;
}
</style>