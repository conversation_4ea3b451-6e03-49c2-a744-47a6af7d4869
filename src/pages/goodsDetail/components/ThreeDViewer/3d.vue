<!--
3D模型查看器组件
功能：
- 加载和显示3D模型
- 支持模型旋转、缩放、平移
- 支持多种模型格式（GLB、GLTF等）
- 支持材质兼容性处理
- 支持移动设备优化
- 支持错误处理和降级
- 支持截图功能（基础截图和带水印元素截图）

截图功能使用方法（截图时会自动重置到初始化位置）：
1. 基础截图：
   this.$refs.threeDViewer.takeScreenshot(false, {
     width: 1024,
     height: 1024,
     format: 'image/webp',
     quality: 0.9
   })

2. 带水印元素截图：
   this.$refs.threeDViewer.takeScreenshot(true, {
     width: 1024,
     height: 1024,
     format: 'image/webp',
     quality: 0.9,
     logo: { url: '/static/assets/common/logo.png', size: 0.15, opacity: 0.8 }, // 左上角
     qrCode: { url: 'path/to/qrcode.png', size: 0.12, opacity: 0.8 }, // 右上角
     title: { text: 'GRANDTAN 2025', fontSize: 24, color: '#000', opacity: 0.8 } // 右下角
   })

3. 直接下载截图：
   this.$refs.threeDViewer.downloadScreenshot(true, 'my-model', {
     width: 1024,
     height: 1024,
     format: 'image/webp',
     quality: 0.9,
     logo: { url: '/static/assets/common/logo.png' },
     qrCode: { url: 'path/to/qrcode.png' },
     title: { text: 'GRANDTAN 2025' }
   })

水印元素配置说明：
- logo: 左上角显示，支持 url、size（相对大小）、opacity（透明度）
- qrCode: 右上角显示，支持 url、size（相对大小）、opacity（透明度）
- title: 右下角显示，支持 text、fontSize、color、opacity、shadow（阴影效果）
- padding: 全局边距比例，默认 0.03（3%）
-->
<template>
	<!-- #ifdef APP-PLUS || H5 -->
	<view class="three-viewer-container">
		<view id="threeView" class="three-canvas"></view>

		<!-- Loading Progress Bar -->
		<view v-if="showLoading" class="loading-container">
			<view class="loading-bar-container">
				<view class="loading-bar" :style="{ width: loadingProgress + '%' }"></view>
				<view class="loading-text">{{ loadingText }}</view>
			</view>
		</view>
	</view>
	<!-- #endif -->
	<!-- #ifndef APP-PLUS || H5 -->
	<view>非 APP、H5 环境不支持</view>
	<!-- #endif -->
</template>

<script module="three" lang="renderjs" src="@/renderjs/ktx-loader.render.js">
import config from '@/config';
 
// #ifdef H5
	import * as THREE from 'three';
	import {
		OrbitControls
	} from 'three/examples/jsm/controls/OrbitControls.js'

	import {
		GLTFLoader
	} from 'three/examples/jsm/loaders/GLTFLoader.js'
	
	import {
		DRACOLoader
	} from 'three/examples/jsm/loaders/DRACOLoader.js'
	
	import {
		RGBELoader
	} from 'three/examples/jsm/loaders/RGBELoader.js'
	
	// // 导入KTX2Loader并确保它在renderjs环境中可用
	// import { KTX2Loader } from 'three/examples/jsm/loaders/KTX2Loader.js';
	
	// 确保_defineProperty2在当前作用域可用
	if (typeof Object.defineProperty !== 'function' ) {
		Object.defineProperty = function (obj, prop, descriptor ) {
			obj[prop] = descriptor.value ;
		};
	}

	let _OPTIONS = {};
	if  (!_OPTIONS) {
		_OPTIONS = {};
	}

	const BasisFormat  = {
		ETC1S: 'ETC1S'
	};

	const FORMAT_OPTIONS  = [
		{ basisFormat: ['ETC1S'], transcoderFormat: ['format1'], engineFormat: ['format2'], engineType: ['type1'], priorityUASTC: 1  }
	];

	_OPTIONS[BasisFormat.ETC1S] = FORMAT_OPTIONS.filter(function(opt ) {
		return opt.basisFormat.includes(BasisFormat.ETC1S );
	}).sort(function(a, b ) {
		return a.priorityUASTC - b.priorityUASTC ;
	});
	
	var renderer;
	var scene;
	var camera;
	var controls;
	var model;
	var gltfLoader;
	// var ktx2Loader = KTX2Loader;
	var ktx2Worker;

	export default {
		props: {
			modelUrl: {
				type: String,
				default: 'https://binyo.net/u_file/2502/glb/06/039347d599.glb'
			},
			threeKTX2Url: {
				type: String,
				default: ''
			},
			threeInitEnvUrl: {
				type: String,
				default: ''
			},
			backgroundIndex: {
				type: Number,
				default: 2
			},
			colorConfig: {
				type: Object,
				default: () => ({})
			},
			autoLoad: {
				type: Boolean,
				default: true
			},
			loadKTX2Texture: {
				type: Function,
				default: () => {}
			}
		},
		
		data() {
			return {
				showLoading: false,
				loadingProgress: 0,
				loadingText: 'Loading 0%',
				backgrounds: [
					{
						id: 1,
						name: "Studio",
						url: "https://dl.polyhaven.org/file/ph-assets/HDRIs/hdr/1k/studio_small_09_1k.hdr"
					},
					{
						id: 2,
						name: "Garden", 
						url: "https://dl.polyhaven.org/file/ph-assets/HDRIs/hdr/1k/pretoria_gardens_1k.hdr"
					},
					{
						id: 3,
						name: "Sunset",
						url: "https://dl.polyhaven.org/file/ph-assets/HDRIs/hdr/1k/sunset_fairway_1k.hdr"
					}
				],
				// 按需渲染相关状态
				needsRender: false,
				isRendering: false,
				renderTimeout: null,
				lastRenderTime: 0,
				frameRate: 60, // 目标帧率
				isUserInteracting: false,
				interactionTimeout: null,
				renderThrottleId: null, // 防抖渲染ID
				// iOS 15.1 兼容性相关
				contextLostCount: 0, // WebGL上下文丢失计数
				// 模型加载状态
				modelLoaded: false, // 标记模型是否已首次加载
				// 安卓兼容性相关
				forceBasicMaterials: false // 强制使用基础材质
			}
		},

		mounted() {
			try {
				// 设置全局引用以便着色器错误处理访问
				window.vue3DViewer = this;
				
				// 初始化Three.js场景
				this.initThree();
				
				// 初始化加载器
				this.setupLoaders();
				
				// 创建控制器
				this.createControls();
				
				// 加载背景
				this.loadInitialBackground();
				
				// 如果设置了自动加载，加载模型
				if (this.autoLoad) {
					this.loadModel(this.modelUrl);
				}
			} catch (error) {
				console.error('Three.js 初始化失败:', error);
				this.handleInitError(error);
			}
		},

		watch: {
			modelUrl(newUrl) {
				if (newUrl) {
					this.loadModel(newUrl);
				}
			},
			
			backgroundIndex(newIndex) {
				this.changeBackground(newIndex);
			},
			
			colorConfig: {
				handler(newConfig) {
					if (newConfig && Object.keys(newConfig).length > 0) {
						this.applyColorConfig(newConfig);
					}
				},
				deep: true
			}
		},

		methods: {
			initThree() {
				console.log('初始化Three.js场景');

				// 检查THREE是否可用
				if (typeof THREE === 'undefined') {
					throw new Error('THREE.js库未正确加载');
				}

				scene = new THREE.Scene();

				// 在创建场景后设置Three.js兼容性处理
				this.setupThreeJSCompatibility();

				const container = document.getElementById('threeView');
				if (!container) {
					throw new Error('找不到3D容器元素 #threeView');
				}
				
				const width = container.clientWidth || window.innerWidth;
				const height = container.clientHeight || window.innerHeight;
				
				console.log('容器尺寸:', { width, height });

				camera = new THREE.PerspectiveCamera(60, width / height, 0.1, 1000);
				camera.position.set(5, 5, 5); // 增加初始距离，从(5,5,5)改为(8,8,8)
				camera.lookAt(0, 1, 0);

				// 检测移动设备和iOS版本
				const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
				const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
				const isAndroid = /Android/i.test(navigator.userAgent);
				
				// 检测安卓版本
				let androidVersion = null;
				let isProblematicAndroid = false;
				if (isAndroid) {
					const match = navigator.userAgent.match(/Android (\d+)/);
					if (match) {
						androidVersion = parseInt(match[1]);
						// 安卓10及以下版本在某些设备上有WebGL兼容性问题
						isProblematicAndroid = androidVersion <= 10;
						console.log('检测到安卓版本:', androidVersion, isProblematicAndroid ? '(可能有兼容性问题)' : '');
					}
				}
				
				// 检测iOS版本，特别是15.1的问题版本
				let iosVersion = null;
				let isProblematicIOS = false;
				if (isIOS) {
					const match = navigator.userAgent.match(/OS (\d+)_(\d+)/);
					if (match) {
						iosVersion = `${match[1]}.${match[2]}`;
						// iOS 15.1有已知的WebGL2问题
						isProblematicIOS = iosVersion === '15.1' || iosVersion === '15.0';
						console.log('检测到iOS版本:', iosVersion, isProblematicIOS ? '(问题版本)' : '');
					}
				}
				
				console.log('设备类型:', isMobile ? '移动设备' : '桌面设备', isIOS ? `iOS ${iosVersion}` : '');
				
				// 检测WebGL支持 - 对iOS 15.1和安卓10强制使用WebGL1
				const canvas = document.createElement('canvas');
				let gl;
				
				if (isProblematicIOS || isProblematicAndroid) {
					// iOS 15.1和安卓10强制使用WebGL1
					console.log('检测到问题版本，强制使用WebGL1');
					gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
				} else {
					// 其他设备优先尝试WebGL2
					gl = canvas.getContext('webgl2') || canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
				}
				
				if (!gl) {
					throw new Error('您的设备不支持WebGL，无法显示3D内容');
				}
				
				// 检查WebGL版本和扩展支持
				const isWebGL2 = gl instanceof WebGL2RenderingContext;
				console.log('WebGL版本:', isWebGL2 ? 'WebGL2' : 'WebGL1', isProblematicIOS ? '(强制降级)' : '');
				
				// 检查关键扩展
				const requiredExtensions = ['OES_standard_derivatives'];
				const missingExtensions = [];
				
				requiredExtensions.forEach(ext => {
					if (!gl.getExtension(ext)) {
						missingExtensions.push(ext);
					}
				});
				
				if (missingExtensions.length > 0) {
					console.warn('缺少WebGL扩展:', missingExtensions);
				}
				
				console.log('WebGL支持检测通过');
				
				// 根据设备能力调整渲染器配置
				const rendererConfig = {
					antialias: !isMobile && isWebGL2 && !isProblematicIOS && !isProblematicAndroid, // 问题设备禁用抗锯齿
					alpha: true,
					preserveDrawingBuffer: true,
					powerPreference: (isMobile || isProblematicIOS || isProblematicAndroid) ? "low-power" : "high-performance",
					failIfMajorPerformanceCaveat: false, // 允许在性能较差的设备上运行
					logarithmicDepthBuffer: false, // 在移动设备上禁用对数深度缓冲
					precision: (isMobile || isProblematicIOS || isProblematicAndroid) ? 'mediump' : 'highp', // 问题设备使用中等精度
					stencil: !isProblematicIOS && !isProblematicAndroid, // 问题设备禁用模板缓冲
					depth: true
				};
				
				if (isProblematicIOS) {
					console.log('应用iOS 15.1兼容性配置');
				}
				
				if (isProblematicAndroid) {
					console.log('应用安卓10兼容性配置');
					// 对安卓10设备强制启用基础材质模式
					this.forceBasicMaterials = true;
				}
				
				try {
					renderer = new THREE.WebGLRenderer(rendererConfig);
				} catch (error) {
					console.warn('高级渲染器创建失败，尝试基础配置:', error);
					// 降级到最基础的配置
					renderer = new THREE.WebGLRenderer({
						antialias: false,
						alpha: true,
						preserveDrawingBuffer: true,
						powerPreference: "low-power",
						failIfMajorPerformanceCaveat: false
					});
				}
				
				console.log('WebGL渲染器创建成功');
				
				// 设置材质错误处理
				this.setupMaterialErrorHandling();
				
				// 移动设备限制像素比，iOS 15.1进一步限制
				let pixelRatio;
				if (isProblematicIOS) {
					pixelRatio = 1; // iOS 15.1强制使用1倍像素比
				} else if (isMobile) {
					pixelRatio = Math.min(window.devicePixelRatio, 2);
				} else {
					pixelRatio = window.devicePixelRatio;
				}
				
				renderer.setPixelRatio(pixelRatio);
				renderer.setSize(width, height);
				
				// 设备兼容性配置
				if (!isProblematicIOS) {
					renderer.outputColorSpace = THREE.SRGBColorSpace;
					renderer.toneMapping = THREE.ACESFilmicToneMapping;
					renderer.toneMappingExposure = 1.2;
				} else {
					// iOS 15.1使用最基础的设置
					console.log('iOS 15.1: 使用基础渲染设置');
					renderer.outputColorSpace = THREE.SRGBColorSpace;
					renderer.toneMapping = THREE.NoToneMapping;
					renderer.toneMappingExposure = 1.0;
				}

				container.appendChild(renderer.domElement);

				const ambientLight = new THREE.AmbientLight(0xffffff, isProblematicIOS ? 2.0 : 1.5);
				scene.add(ambientLight);

				const pointLight = new THREE.PointLight(0xffffff, isProblematicIOS ? 1.5 : 1.2);
				pointLight.position.set(5, 10, 5);
				scene.add(pointLight);

				// 阴影设置 - iOS 15.1禁用阴影
				if (!isProblematicIOS) {
					// 1. 渲染器开启阴影
					renderer.shadowMap.enabled = true;
					renderer.shadowMap.type = THREE.PCFSoftShadowMap;

					// 2. 主光源开启阴影
					const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
					directionalLight.position.set(-5, 10, 5);
					directionalLight.castShadow = true;
					directionalLight.shadow.mapSize.width = 2048;
					directionalLight.shadow.mapSize.height = 2048;
					directionalLight.shadow.camera.near = 1;
					directionalLight.shadow.camera.far = 50;
					directionalLight.shadow.camera.left = -10;
					directionalLight.shadow.camera.right = 10;
					directionalLight.shadow.camera.top = 10;
					directionalLight.shadow.camera.bottom = -10;
					scene.add(directionalLight);
				} else {
					// iOS 15.1: 禁用阴影，使用简单光照
					console.log('iOS 15.1: 禁用阴影，使用简单光照');
					renderer.shadowMap.enabled = false;
					
					const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
					directionalLight.position.set(-5, 10, 5);
					directionalLight.castShadow = false;
					scene.add(directionalLight);
				}

				// 3. 加载真实地面贴图
				const groundTextureLoader = new THREE.TextureLoader();
				// 移除外部贴图加载，避免CORS问题
				// const groundTextureUrl = 'http://api.binyo.net/u_file/3d/bj.png';
				// let groundTexture = null;
				// groundTextureLoader.load(groundTextureUrl, (texture) => {
				// 	groundTexture = texture;
				// });

				// 添加WebGL错误监听和着色器错误处理
				try {
					const gl = renderer.getContext();
					
					// 设置着色器编译错误处理
					this.setupShaderErrorHandling(gl);
					
					// 检测iOS版本
				const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
				let isProblematicIOS = false;
				if (isIOS) {
					const match = navigator.userAgent.match(/OS (\d+)_(\d+)/);
					if (match) {
						const iosVersion = `${match[1]}.${match[2]}`;
						isProblematicIOS = iosVersion === '15.1' || iosVersion === '15.0';
					}
				}
				
				if (gl && typeof gl.addEventListener === 'function') {
					gl.addEventListener('webglcontextlost', (event) => {
						console.error('WebGL context lost:', event);
						event.preventDefault();
						
						// iOS 15.1 特殊处理
						if (isProblematicIOS) {
							console.warn('iOS 15.1 WebGL上下文丢失，准备恢复');
							this.contextLostCount = (this.contextLostCount || 0) + 1;
							
							// 如果上下文丢失次数过多，显示错误提示
							if (this.contextLostCount > 3) {
								console.error('iOS 15.1 WebGL上下文频繁丢失，显示错误提示');
								this.handleInitError(new Error('iOS 15.1 WebGL兼容性问题'));
								return;
							}
						}
					});
					
					gl.addEventListener('webglcontextrestored', () => {
						console.log('WebGL context restored');
						
						// iOS 15.1 特殊恢复处理
						if (isProblematicIOS) {
							console.log('iOS 15.1 WebGL上下文恢复，重新初始化');
							
							// 延迟重新初始化，给系统时间稳定
							setTimeout(() => {
								try {
									// 重新设置着色器错误处理
									this.setupShaderErrorHandling(gl);
									
									// 重新加载模型（如果存在）
									if (this.modelUrl && !model) {
										this.loadModel(this.modelUrl);
									}
									
									// 重新渲染
									this.markNeedsRender();
								} catch (error) {
									console.error('iOS 15.1 上下文恢复失败:', error);
									this.handleInitError(error);
								}
							}, 1000);
						} else {
							// 标准恢复处理
							this.setupShaderErrorHandling(gl);
						}
					});
				} else {
					// 使用canvas的事件监听作为后备方案
					const canvas = renderer.domElement;
					if (canvas && typeof canvas.addEventListener === 'function') {
						canvas.addEventListener('webglcontextlost', (event) => {
							console.error('WebGL context lost:', event);
							event.preventDefault();
							
							if (isProblematicIOS) {
								this.contextLostCount = (this.contextLostCount || 0) + 1;
								if (this.contextLostCount > 3) {
									this.handleInitError(new Error('iOS 15.1 WebGL兼容性问题'));
									return;
								}
							}
						});
						
						canvas.addEventListener('webglcontextrestored', () => {
							console.log('WebGL context restored');
							
							if (isProblematicIOS) {
								setTimeout(() => {
									this.setupShaderErrorHandling(gl);
									if (this.modelUrl && !model) {
										this.loadModel(this.modelUrl);
									}
									this.markNeedsRender();
								}, 1000);
							} else {
								this.setupShaderErrorHandling(gl);
							}
						});
					}
				}
				} catch (error) {
					console.warn('无法添加WebGL事件监听器:', error);
				}

				// 初始渲染
				this.markNeedsRender();

				window.addEventListener('resize', this.onWindowResize, false);
			},

			setupLoaders() {
				// 设置DRACO加载器
				const dracoLoader = new DRACOLoader();
				dracoLoader.setDecoderPath("https://www.gstatic.com/draco/versioned/decoders/1.5.6/");
				dracoLoader.preload();
				
				// 设置GLTF加载器
				gltfLoader = new GLTFLoader();
				gltfLoader.setDRACOLoader(dracoLoader);
				
			},

			createControls() {
				controls = new OrbitControls(camera, renderer.domElement);
				controls.target.set(0, 1, 0);
				controls.enableDamping = true;
				controls.dampingFactor = 0.08;
				controls.screenSpacePanning = false;
				const currentDistance = camera.position.distanceTo(controls.target);
				controls.minDistance = currentDistance*0.5;
				controls.maxDistance = currentDistance;
				controls.minPolarAngle = 0.1;
				controls.maxPolarAngle = Math.PI * 0.45;
				
				// 使用Three.js推荐的事件监听模式
				controls.addEventListener('start', () => {
					this.isUserInteracting = true;
				});
				
				controls.addEventListener('change', () => {
					// 使用Three.js推荐的按需渲染模式
					this.requestRenderIfNotRequested();
				});
				
				controls.addEventListener('end', () => {
					this.isUserInteracting = false;
					// 如果启用了阻尼，需要继续渲染直到阻尼停止
					if (controls.enableDamping) {
						this.handleDampingEnd();
					} else {
						// 确保交互结束后渲染最终状态
						this.requestRenderIfNotRequested();
					}
				});
				
				controls.update();
			},

			loadInitialBackground() {
				// 加载GLB环境背景
				this.loadGLBEnvironment();
			},

			// 加载程序化真实地面
			loadGLBEnvironment() {
				console.log('开始加载GLB环境背景...');
				// 清除原有的背景和环境
				scene.background = null;
				scene.environment = null;

				// 加载GLB环境文件
				const glbUrl =this.threeInitEnvUrl;
				
				gltfLoader.load(
					glbUrl,
					(gltf) => {
						console.log('GLB环境背景加载成功');
						
						// 将GLB环境添加到场景
						const envScene = gltf.scene;
						scene.add(envScene);
						
						// 调整环境位置和大小
						envScene.position.set(0, 0, 0);
						envScene.scale.set(1, 1, 1);
						
						// 加载KTX2贴图并应用材质
						this.loadKTX2Texture(config.threeKTX2Url, renderer, (texture, error) => {
							if (error) return;
							
							// 1. 用作环境贴图（关键！）
							texture.mapping = THREE.EquirectangularReflectionMapping;
							scene.environment = texture;
							scene.background = texture;
							
							// 2. 如果要给地面用材质，改成这样：
							const material = new THREE.MeshStandardMaterial({
								metalness: 0.1,  // 不要用1
								roughness: 0.7,  // 不要用1
								envMapIntensity: 1.0  // 让物体反射环境
							});
							material.map = texture;
							// 应用材质到GLB场景的所有网格
							gltf.scene.traverse((child) => {
								if (child.isMesh) {
									// child.material = material.clone(); // 克隆材质避免共享
									child.material = material;
									child.material.needsUpdate = true;
								}
							});
							
							console.log('KTX2贴图应用完成');
						});
						
						// 创建程序化真实地面
						this.createProceduralGround();
						
						this.$emit('glb-environment-loaded', { scene: envScene });
					},
					(xhr) => {
						console.log('GLB环境加载进度:', (xhr.loaded / xhr.total * 100) + '%');
					},
					(error) => {
						console.warn('GLB环境加载失败，使用默认背景', error);
						// 使用纯色背景作为后备方案
						scene.background = new THREE.Color(0x87CEEB); // 天蓝色
						this.createProceduralGround();
					}
				);
			},

			// 创建程序化真实地面
			createProceduralGround() {
				const textureLoader = new THREE.TextureLoader();
				// 创建地形几何体（带细分）
				const terrainGeometry = new THREE.PlaneGeometry(30, 30, 128, 128);
				
				// 添加高度变化（模拟真实地形）
				const positions = terrainGeometry.attributes.position.array;
				for (let i = 0; i < positions.length; i += 3) {
					const x = positions[i];
					const z = positions[i + 2];
					// 使用噪声函数生成高度
					const height = Math.sin(x * 0.1) * Math.cos(z * 0.1) * 0.1;
					positions[i + 1] = height;
				}
				terrainGeometry.attributes.position.needsUpdate = true;
				terrainGeometry.computeVertexNormals();

				// 创建PBR材质
				const groundMaterial = new THREE.MeshStandardMaterial({
					color: 0x8B7355, // 基础颜色
					roughness: 0.8,
					metalness: 0.1,
					envMapIntensity: 0.3
				});

				// 加载贴图，添加错误处理
				const loadTexture = (url, textureType) => {
					textureLoader.load(
						url,
						(texture) => {
							texture.wrapS = THREE.RepeatWrapping;
							texture.wrapT = THREE.RepeatWrapping;
							texture.repeat.set(4, 4);
							
							switch(textureType) {
								case 'color':
									groundMaterial.map = texture;
									break;
								case 'normal':
									groundMaterial.normalMap = texture;
									break;
								case 'roughness':
									groundMaterial.roughnessMap = texture;
									break;
								case 'ao':
									groundMaterial.aoMap = texture;
									break;
							}
							groundMaterial.needsUpdate = true;
						},
						undefined,
						(error) => {
							console.warn(`地面贴图加载失败: ${textureType}`, error);
							// 贴图加载失败时继续使用基础材质
						}
					);
				};

 
				// 创建地面mesh
				const groundMesh = new THREE.Mesh(terrainGeometry, groundMaterial);
				groundMesh.name = 'procedural_ground';
				groundMesh.rotation.x = -Math.PI / 2; // 水平放置
				groundMesh.position.y = -0.5; // 稍微下沉
				groundMesh.receiveShadow = true; // 接收阴影
				
				scene.add(groundMesh);
				
				console.log('程序化真实地面创建完成');
				this.$emit('custom-background-loaded', {
					type: 'procedural-ground',
					groundMesh: groundMesh
				});
			},

			// 移除程序化地面
			removeGLBEnvironment() {
				const groundObject = scene.getObjectByName('procedural_ground');
				if (groundObject) {
					scene.remove(groundObject);
					console.log('程序化地面已移除');
					return true;
				}
				return false;
			},

			// 设置着色器错误处理
			setupShaderErrorHandling(gl) {
				if (!gl) return;
				
				// 检测设备类型
				const isAndroid = /Android/i.test(navigator.userAgent);
				const isAdreno = this.isAdrenoGPU(gl);
				
				// 重写着色器编译函数以捕获错误
				const originalCompileShader = gl.compileShader;
				gl.compileShader = function(shader) {
					originalCompileShader.call(this, shader);
					
					// 检查编译状态
					if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
						const error = gl.getShaderInfoLog(shader);
						const shaderType = gl.getShaderParameter(shader, gl.SHADER_TYPE);
						const typeName = shaderType === gl.VERTEX_SHADER ? 'Vertex' : 'Fragment';
						
						console.error(`${typeName} Shader编译失败:`, error);
						
						// 针对安卓Adreno GPU的特殊处理
						if (isAndroid && isAdreno) {
							console.warn('检测到安卓Adreno GPU着色器错误，强制使用基础材质');
							// 触发材质降级
							this.forceBasicMaterials = true;
							// 如果模型已加载，重新处理材质
							if (model) {
								this.replaceProblemMaterials(scene);
								this.markNeedsRender();
							}
						}
						
						// 尝试修复常见的着色器语法错误
						if (error && error.includes('syntax error')) {
							console.warn('检测到着色器语法错误，可能是精度限定符问题');
							// 这里可以添加自动修复逻辑
						}
					}
				};
				
				// 重写程序链接函数以捕获链接错误
				const originalLinkProgram = gl.linkProgram;
				gl.linkProgram = function(program) {
					originalLinkProgram.call(this, program);
					
					// 检查链接状态
					if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
						const error = gl.getProgramInfoLog(program);
						console.error('Shader Program链接失败:', error);
						
						// 针对安卓设备的特殊处理
						if (isAndroid && isAdreno) {
							console.warn('安卓Adreno GPU程序链接失败，强制使用基础材质');
							this.forceBasicMaterials = true;
							// 如果模型已加载，重新处理材质
							if (model) {
								this.replaceProblemMaterials(scene);
								this.markNeedsRender();
							}
						}
					}
					
					// 验证程序
					gl.validateProgram(program);
					if (!gl.getProgramParameter(program, gl.VALIDATE_STATUS)) {
						const error = gl.getProgramInfoLog(program);
						console.error('Shader Program验证失败:', error);
					}
				};
				
				console.log('着色器错误处理已设置', isAndroid && isAdreno ? '(安卓Adreno模式)' : '');
			},

			// 设置Three.js材质兼容性处理
			setupThreeJSCompatibility() {
				// 检查THREE是否可用
				if (typeof THREE === 'undefined' || !THREE.WebGLProgram) {
					console.warn('THREE.js未正确加载，跳过兼容性设置');
					return;
				}
				
				try {
					// 重写THREE.WebGLProgram以处理着色器编译错误
					const originalWebGLProgram = THREE.WebGLProgram;
					
					THREE.WebGLProgram = function(renderer, cacheKey, parameters, bindingStates) {
						try {
							return originalWebGLProgram.call(this, renderer, cacheKey, parameters, bindingStates);
						} catch (error) {
							console.error('WebGLProgram创建失败:', error);
							
							// 如果是着色器编译错误，尝试使用基础材质
							if (error.message && error.message.includes('shader')) {
								console.warn('着色器编译失败，将使用基础材质');
								// 抛出错误让上层处理，而不是尝试创建fallback程序
								throw new Error('SHADER_COMPILATION_FAILED: ' + error.message);
							}
							
							throw error;
						}
					};
					
					// 继承原型
					if (originalWebGLProgram.prototype) {
						THREE.WebGLProgram.prototype = originalWebGLProgram.prototype;
					}
					
					console.log('Three.js兼容性处理已设置');
				} catch (error) {
					console.error('设置Three.js兼容性处理失败:', error);
				}
			},

			// 材质降级处理 - 当着色器编译失败时的后备方案
			materialFallback(originalMaterial) {
				console.warn('材质降级处理:', originalMaterial.name || 'unnamed');
				
				// 创建最基础的材质作为后备
				const fallbackMaterial = new THREE.MeshBasicMaterial({
					color: originalMaterial.color || new THREE.Color(0xcccccc),
					map: originalMaterial.map || null,
					transparent: originalMaterial.transparent || false,
					opacity: originalMaterial.opacity || 1.0,
					side: originalMaterial.side || THREE.FrontSide,
					visible: originalMaterial.visible !== undefined ? originalMaterial.visible : true
				});
				
				fallbackMaterial.name = (originalMaterial.name || 'material') + '_fallback';
				
				return fallbackMaterial;
			},

			// 全局材质错误处理
			setupMaterialErrorHandling() {
				// 监听渲染器错误
				if (renderer && renderer.domElement) {
					const originalRender = renderer.render;
					renderer.render = (scene, camera) => {
						try {
							originalRender.call(renderer, scene, camera);
						} catch (error) {
							if (error.message && error.message.includes('shader')) {
								console.error('渲染时着色器错误:', error);
								// 尝试替换有问题的材质
								this.replaceProblemMaterials(scene);
								// 重试渲染
								try {
									originalRender.call(renderer, scene, camera);
								} catch (retryError) {
									console.error('重试渲染仍然失败:', retryError);
								}
							} else {
								throw error;
							}
						}
					};
				}
			},

			// 替换有问题的材质
			replaceProblemMaterials(scene) {
				scene.traverse((child) => {
					if (child.isMesh && child.material) {
						// 检查材质是否可能有问题
						if (child.material.isMeshStandardMaterial || 
							child.material.isMeshPhysicalMaterial ||
							child.material.isMeshLambertMaterial) {
							
							console.log('替换可能有问题的材质:', child.material.name);
							child.material = this.materialFallback(child.material);
						}
					}
				});
			},

			// 切换到程序化地面模式
			switchToGLBBackground() {
				// 移除现有地面
				this.removeGLBEnvironment();
				
				// 重新创建程序化地面
				this.createProceduralGround();
			},

			loadModel(url = this.modelUrl) {
				console.log('开始加载模型:', url);
				
				if (model) {
					scene.remove(model);
					this.disposeModel(model);
					model = null;
				}
				
				// 重置模型加载状态，确保新模型会重新调整相机位置
				this.modelLoaded = false;

				this.showLoading = true;
				this.loadingProgress = 0;
				this.loadingText = 'Loading 0%';

				gltfLoader.load(
					url,
					(gltf) => {
						console.log('模型加载成功');
						model = gltf.scene;
					
					// iOS 15.1 内存检查
					const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
					if (isIOS) {
						const match = navigator.userAgent.match(/OS (\d+)_(\d+)/);
						if (match) {
							const iosVersion = `${match[1]}.${match[2]}`;
							if (iosVersion === '15.1' || iosVersion === '15.0') {
								console.log('iOS 15.1 模型加载前内存检查');
								// 检查内存使用情况
								if (performance.memory) {
									const memInfo = performance.memory;
									const usedMB = Math.round(memInfo.usedJSHeapSize / 1024 / 1024);
									const limitMB = Math.round(memInfo.jsHeapSizeLimit / 1024 / 1024);
									console.log(`内存使用: ${usedMB}MB / ${limitMB}MB`);
									
									// 如果内存使用超过70%，进行额外优化
									if (usedMB / limitMB > 0.7) {
										console.warn('iOS 15.1 内存使用过高，启用激进优化');
										// 进一步简化材质
										this.aggressiveOptimization(model);
									}
								}
							}
						}
					}
					
					// 处理材质兼容性
					this.processMaterialCompatibility(model);
					
					// 如果是安卓10设备，额外进行材质降级处理
					const isAndroid = /Android/i.test(navigator.userAgent);
					if (isAndroid) {
						const match = navigator.userAgent.match(/Android (\d+)/);
						if (match && parseInt(match[1]) <= 10) {
							console.log('安卓10设备：强制使用基础材质');
							this.forceBasicMaterials = true;
							this.replaceProblemMaterials(model);
						}
					}
					
					scene.add(model);

					this.adjustModelTransform(model);
					
					// 模型加载完成后需要重新渲染
					this.markNeedsRender();

					this.loadingProgress = 100;
					this.loadingText = 'Load complete';
					
					setTimeout(() => {
						this.showLoading = false;
						this.$emit('load-success', { model: gltf });
					}, 1000);
					},
					(xhr) => {
						if (xhr.lengthComputable) {
							const percentComplete = (xhr.loaded / xhr.total) * 100;
							this.loadingProgress = percentComplete;
							this.loadingText = `Loading ${percentComplete.toFixed(0)}%`;
						}
					},
					(error) => {
						console.error('模型加载失败:', error);
						
						if (error.message && error.message.includes('DRACO')) {
							console.log('DRACO解码失败，尝试不使用DRACO重新加载...');
							this.loadModelWithoutDraco(url);
							return;
						}
						
						this.loadingText = 'Load failed';
						setTimeout(() => {
							this.showLoading = false;
							this.$emit('load-error', error);
						}, 1500);
					}
				);
			},

			loadModelWithoutDraco(url) {
				console.log('使用标准GLTF加载器重新加载模型...');
				
				const standardLoader = new GLTFLoader();
				
				standardLoader.load(
					url,
					(gltf) => {
						console.log('标准模型加载成功');
				model = gltf.scene;
				
				// 处理材质兼容性
				this.processMaterialCompatibility(model);
				
				// 如果是安卓10设备，额外进行材质降级处理
				const isAndroid = /Android/i.test(navigator.userAgent);
				if (isAndroid) {
					const match = navigator.userAgent.match(/Android (\d+)/);
					if (match && parseInt(match[1]) <= 10) {
						console.log('安卓10设备：强制使用基础材质');
						this.forceBasicMaterials = true;
						this.replaceProblemMaterials(model);
					}
				}
				
				scene.add(model);

					this.adjustModelTransform(model);
					
					// 模型加载完成后需要重新渲染
					this.markNeedsRender();

					this.loadingProgress = 100;
					this.loadingText = 'Load complete';
					
					setTimeout(() => {
						this.showLoading = false;
						this.$emit('load-success', { model: gltf });
					}, 1000);
					},
					(xhr) => {
						if (xhr.lengthComputable) {
							const percentComplete = (xhr.loaded / xhr.total) * 100;
							this.loadingProgress = percentComplete;
							this.loadingText = `Loading ${percentComplete.toFixed(0)}%`;
						}
					},
					(error) => {
						console.error('标准模型加载也失败:', error);
						this.loadingText = 'Load failed';
						setTimeout(() => {
							this.showLoading = false;
							this.$emit('load-error', error);
						}, 1500);
					}
				);
			},

			adjustModelTransform(model) {
				const box = new THREE.Box3().setFromObject(model);
				const size = box.getSize(new THREE.Vector3());
				const center = box.getCenter(new THREE.Vector3());
				const maxDim = Math.max(size.x, size.y, size.z);
				
				const scale = 10 / maxDim;
				model.scale.set(scale, scale, scale);
				
				// 设置模型旋转，让车子面向正确的方向（前方朝向Z轴正方向）
				model.rotation.set(0, Math.PI, 0); // 绕Y轴旋转180度
				
				model.position.sub(center.multiplyScalar(scale));
				model.position.y += (size.y / 2) * scale;

				// 只在首次加载模型时调整相机位置，避免颜色配置时的意外旋转
				if (!this.modelLoaded) {
					const finalBox = new THREE.Box3().setFromObject(model);
					const finalCenter = finalBox.getCenter(new THREE.Vector3());
					const finalSize = finalBox.getSize(new THREE.Vector3());
					const newMaxDim = Math.max(finalSize.x, finalSize.y, finalSize.z);

					controls.target.copy(finalCenter);

					const cameraDistance = newMaxDim * 1.2; // 增加距离系数，从1.2改为1.8
					camera.position.set(
						finalCenter.x,
						finalCenter.y,
						finalCenter.z + cameraDistance * 0.8 // 增加Z轴偏移，从0.8改为1.0
					);
					camera.lookAt(finalCenter);
					controls.update();
					
					this.modelLoaded = true;
					console.log('首次模型加载，相机位置已调整');
				} else {
					console.log('模型重新加载，保持当前相机位置');
				}
			},

			changeBackground(backgroundIndex) {
				const background = this.backgrounds.find(bg => bg.id === backgroundIndex);
				if (!background) {
					console.warn(`背景未找到: ${backgroundIndex}`);
					return;
				}

				console.log(`切换背景到: ${background.name}`);
				
				const rgbeLoader = new RGBELoader();
				rgbeLoader.load(
					background.url,
					(texture) => {
						texture.mapping = THREE.EquirectangularReflectionMapping;
						scene.environment = texture;
						scene.background = texture;
						console.log(`背景切换成功: ${background.name}`);
						// 背景切换后需要重新渲染
						this.markNeedsRender();
						this.$emit('background-changed', background);
					},
					undefined,
					(error) => {
						console.error(`背景加载失败: ${background.name}`, error);
						this.$emit('background-error', { background, error });
					}
				);
			},

			applyColorConfig(config) {
				if (!model) {
					console.warn('模型未加载，无法应用颜色配置');
					return;
				}

				Object.keys(config).forEach(componentName => {
					const optionData = config[componentName];
					this.setMaterialColor(componentName, optionData);
				});
			},

			// 处理材质兼容性 - 解决移动设备着色器错误
			processMaterialCompatibility(model) {
				const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
				const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
				
				// 检测iOS版本
				let isProblematicIOS = false;
				if (isIOS) {
					const match = navigator.userAgent.match(/OS (\d+)_(\d+)/);
					if (match) {
						const iosVersion = `${match[1]}.${match[2]}`;
						isProblematicIOS = iosVersion === '15.1' || iosVersion === '15.0';
					}
				}
				
				if (!isMobile && !isProblematicIOS) {
					return; // 桌面设备且非问题iOS版本不需要处理
				}

				console.log('处理设备材质兼容性...', isProblematicIOS ? '(iOS 15.1强制模式)' : '(移动设备模式)');
				
				model.traverse((child) => {
					if (child.isMesh && child.material) {
						try {
							if (Array.isArray(child.material)) {
								// 处理多材质
								child.material = child.material.map(material => {
									return this.createCompatibleMaterial(material);
								});
							} else {
								// 处理单材质
								child.material = this.createCompatibleMaterial(child.material);
							}
						} catch (error) {
							console.warn('材质兼容性处理失败:', child.name, error);
							// 使用最基础的材质作为后备方案
							child.material = new THREE.MeshBasicMaterial({
								color: 0x888888
							});
						}
					}
				});
				
				console.log('移动设备材质兼容性处理完成');
			},

			// 创建兼容的材质
			createCompatibleMaterial(originalMaterial) {
				const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
				const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
				const isAndroid = /Android/i.test(navigator.userAgent);
				
				// 检测iOS版本
				let isProblematicIOS = false;
				if (isIOS) {
					const match = navigator.userAgent.match(/OS (\d+)_(\d+)/);
					if (match) {
						const iosVersion = `${match[1]}.${match[2]}`;
						isProblematicIOS = iosVersion === '15.1' || iosVersion === '15.0';
					}
				}
				
				// 检测安卓版本和GPU
				let isProblematicAndroid = false;
				if (isAndroid) {
					const gl = renderer.getContext();
					if (gl) {
						const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
						if (debugInfo) {
							const renderer_info = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
							// 检测Adreno GPU（高通骁龙处理器）- 增强检测
				const adrenoKeywords = ['adreno', 'qualcomm', 'snapdragon'];
				if (renderer_info) {
					const lowerRenderer = renderer_info.toLowerCase();
					const hasAdreno = adrenoKeywords.some(keyword => lowerRenderer.includes(keyword));
					if (hasAdreno) {
						console.log('检测到Adreno/高通GPU:', renderer_info);
						isProblematicAndroid = true;
					}
				}
						}
					}
					
					// 检测安卓10及以下版本
					const androidMatch = navigator.userAgent.match(/Android (\d+)/);
					if (androidMatch) {
						const androidVersion = parseInt(androidMatch[1]);
						if (androidVersion <= 10) {
							console.log('检测到安卓10及以下版本:', androidVersion);
							isProblematicAndroid = true;
						}
					}
				}
				
				if (!isMobile && !isProblematicIOS && !isProblematicAndroid) {
					return originalMaterial;
				}

				// 移动设备、iOS 15.1或问题安卓设备：将复杂材质转换为简单材质
				if (originalMaterial.isMeshPhysicalMaterial || originalMaterial.isMeshStandardMaterial) {
					let reason = '';
					if (isProblematicIOS) reason = '(iOS 15.1)';
					else if (isProblematicAndroid) reason = '(安卓兼容模式)';
					else reason = '(移动设备)';
					
					console.log('转换复杂材质为兼容材质:', originalMaterial.name, reason);
					
					// 检查WebGL上下文和着色器支持
					const gl = renderer.getContext();
					const isWebGL2 = gl instanceof WebGL2RenderingContext;
					
					// iOS 15.1、问题安卓设备或强制基础材质模式
			if (isProblematicIOS || isProblematicAndroid || this.forceBasicMaterials || !isWebGL2 || this.isLowEndDevice()) {
				let forceReason = reason;
				if (this.forceBasicMaterials) {
					forceReason += ' (着色器错误强制模式)';
				}
				if (isProblematicAndroid) {
					forceReason += ' (安卓10兼容性)';
				}
				console.log('使用基础材质 (MeshBasicMaterial) 以避免着色器错误', forceReason);
					const basicMaterial = new THREE.MeshBasicMaterial({
						color: originalMaterial.color ? originalMaterial.color.clone() : new THREE.Color(0xffffff),
						map: originalMaterial.map,
						transparent: originalMaterial.transparent,
						opacity: originalMaterial.opacity,
						side: originalMaterial.side,
						alphaTest: originalMaterial.alphaTest,
						visible: originalMaterial.visible
					});
					
					let suffix = '_basic_compatible';
					if (isProblematicIOS) suffix = '_ios151_compatible';
					else if (isProblematicAndroid) suffix = '_android_compatible';
					else if (this.forceBasicMaterials) suffix = '_shader_error_fallback';
					
					basicMaterial.name = originalMaterial.name + suffix;
					return basicMaterial;
				}
					
					// 使用 MeshLambertMaterial 替代复杂材质
					const compatibleMaterial = new THREE.MeshLambertMaterial({
						color: originalMaterial.color ? originalMaterial.color.clone() : new THREE.Color(0xffffff),
						map: originalMaterial.map,
						transparent: originalMaterial.transparent,
						opacity: originalMaterial.opacity,
						side: originalMaterial.side,
						alphaTest: originalMaterial.alphaTest,
						visible: originalMaterial.visible
					});
					
					// 保持原材质名称用于调试
					compatibleMaterial.name = originalMaterial.name + '_compatible';
					
					return compatibleMaterial;
				}
				
				return originalMaterial;
			},

			// 检测是否为Adreno GPU
			isAdrenoGPU(gl) {
				if (!gl) return false;
				
				const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
				if (debugInfo) {
					const renderer_info = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
					if (renderer_info && renderer_info.toLowerCase().includes('adreno')) {
						return true;
					}
				}
				return false;
			},

			// 检测是否为低端设备
			isLowEndDevice() {
				const gl = renderer.getContext();
				if (!gl) return true;
				
				// 检查GPU信息
				const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
				if (debugInfo) {
					const renderer_info = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
					const vendor_info = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
					
					// 检测低端GPU
					const lowEndGPUs = [
						'Adreno 3', 'Adreno 4', 'Adreno 5',
						'Mali-4', 'Mali-T6', 'Mali-T7', 'Mali-T8',
						'PowerVR SGX', 'PowerVR Rogue G6',
						'Intel HD Graphics 3000', 'Intel HD Graphics 4000'
					];
					
					const isLowEndGPU = lowEndGPUs.some(gpu => 
						renderer_info.toLowerCase().includes(gpu.toLowerCase())
					);
					
					if (isLowEndGPU) {
						console.log('检测到低端GPU:', renderer_info);
						return true;
					}
				}
				
				// 检查最大纹理大小
				const maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE);
				if (maxTextureSize < 2048) {
					console.log('检测到低端设备 - 最大纹理大小:', maxTextureSize);
					return true;
				}
				
				// 检查设备内存（如果可用）
				if (navigator.deviceMemory && navigator.deviceMemory < 4) {
					console.log('检测到低内存设备:', navigator.deviceMemory, 'GB');
					return true;
				}
				
				return false;
			},

			setMaterialColor(componentName, optionData) {
				if (!model) return;

				let found = false;
				model.traverse((child) => {
					if (child.isMesh && child.name === componentName) {
						found = true;
						
						// 检查材质是否支持颜色切换（移除限制，支持所有材质类型）
						if (!child.material) {
							console.warn(`组件 ${componentName} 没有材质`);
							return;
						}

						if (!child.material.userData._independent) {
							child.material = child.material.clone();
							child.material.userData._independent = true;
							
							// 确保克隆的材质也是兼容的
							child.material = this.createCompatibleMaterial(child.material);
						}

						this.autoGenerateUV(child);

						if (optionData.ColorChartlet || optionData.PicPath) {
							this.applyTexture(child, optionData.ColorChartlet || optionData.PicPath);
						} else if (optionData.optionColor) {
							this.applySolidColor(child, optionData.optionColor);
						}
					}
				});

				if (!found) {
					console.warn(`组件未找到: ${componentName}`);
				} else {
					// 材质颜色改变后需要重新渲染
					this.markNeedsRender();
				}
			},

			applyTexture(mesh, textureUrl) {
				const loader = new THREE.TextureLoader();
				loader.load(textureUrl, (texture) => {
					texture.wrapS = THREE.ClampToEdgeWrapping;
					texture.wrapT = THREE.ClampToEdgeWrapping;
					texture.repeat.set(1, 1);
					texture.offset.set(0, 0);
					texture.flipY = false;
					texture.colorSpace = THREE.SRGBColorSpace;
					texture.needsUpdate = true;

					if (mesh.material.map && mesh.material.map.dispose) {
						mesh.material.map.dispose();
					}

					mesh.material.map = texture;
					mesh.material.color.set(0xffffff);
					
					// 根据材质类型设置属性
					if (mesh.material.isMeshStandardMaterial || mesh.material.isMeshPhysicalMaterial) {
						mesh.material.roughness = 0.6;
						mesh.material.metalness = 0.1;
					} else if (mesh.material.isMeshLambertMaterial) {
						// MeshLambertMaterial 不支持 roughness 和 metalness
						// 只设置基本属性
					}
					
					mesh.material.needsUpdate = true;
					
					// 纹理应用后需要重新渲染
					this.markNeedsRender();
				});
			},

			applySolidColor(mesh, colorValue) {
				if (mesh.material.map && mesh.material.map.dispose) {
					mesh.material.map.dispose();
				}
				
				mesh.material.map = null;
				mesh.material.color.set(colorValue);
				
				// 根据材质类型设置属性
				if (mesh.material.isMeshStandardMaterial || mesh.material.isMeshPhysicalMaterial) {
					mesh.material.roughness = 0.4;
					mesh.material.metalness = 0.7;
				} else if (mesh.material.isMeshLambertMaterial) {
					// MeshLambertMaterial 不支持 roughness 和 metalness
					// 只设置基本属性
				}
				
				mesh.material.needsUpdate = true;
				
				// 颜色应用后需要重新渲染
				this.markNeedsRender();
			},

			autoGenerateUV(mesh, type = "auto") {
				if (mesh.geometry && !mesh.geometry.attributes.uv) {
					const pos = mesh.geometry.attributes.position.array;
					const uv = [];
					
					for (let i = 0; i < pos.length; i += 3) {
						const x = pos[i], y = pos[i + 1], z = pos[i + 2];
						let u = 0, v = 0;
						
						if (type === "tire" || (type === "auto" && mesh.name.includes("tire"))) {
							const theta = Math.atan2(z, x);
							u = (theta + Math.PI) / (2 * Math.PI);
							v = y * 0.5 + 0.5;
						} else if (type === "frame" || (type === "auto" && mesh.name.includes("frame"))) {
							u = x * 0.5 + 0.5;
							v = z * 0.5 + 0.5;
						} else if (type === "seat" || (type === "auto" && mesh.name.includes("seat"))) {
							u = x * 0.5 + 0.5;
							v = y * 0.5 + 0.5;
						} else {
							u = y * 0.5 + 0.5;
							v = z * 0.5 + 0.5;
						}
						uv.push(u, v);
					}
					
					mesh.geometry.setAttribute('uv', new THREE.BufferAttribute(new Float32Array(uv), 2));
					mesh.geometry.attributes.uv.needsUpdate = true;
				}
			},

			disposeModel(model) {
				model.traverse((child) => {
					if (child.geometry) {
						child.geometry.dispose();
					}
					if (child.material) {
						if (Array.isArray(child.material)) {
							child.material.forEach((m) => {
								// 清理材质贴图
								if (m.map) m.map.dispose();
								if (m.normalMap) m.normalMap.dispose();
								if (m.roughnessMap) m.roughnessMap.dispose();
								if (m.metalnessMap) m.metalnessMap.dispose();
								m.dispose();
							});
						} else {
							// 清理材质贴图
							if (child.material.map) child.material.map.dispose();
							if (child.material.normalMap) child.material.normalMap.dispose();
							if (child.material.roughnessMap) child.material.roughnessMap.dispose();
							if (child.material.metalnessMap) child.material.metalnessMap.dispose();
							child.material.dispose();
						}
					}
				});
				
				// iOS 15.1 特殊内存清理
				const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
				if (isIOS) {
					const match = navigator.userAgent.match(/OS (\d+)_(\d+)/);
					if (match) {
						const iosVersion = `${match[1]}.${match[2]}`;
						if (iosVersion === '15.1' || iosVersion === '15.0') {
							console.log('iOS 15.1 执行特殊内存清理');
							// 强制垃圾回收
							if (window.gc) {
								window.gc();
							}
							// 清理WebGL缓存
							if (renderer && renderer.getContext) {
								const gl = renderer.getContext();
								if (gl && gl.flush) {
									gl.flush();
								}
							}
						}
					}
				}
			},

			// iOS 15.1 激进优化 - 最大程度简化模型
			aggressiveOptimization(model) {
				console.log('执行iOS 15.1激进优化');
				
				model.traverse((child) => {
					if (child.isMesh) {
						// 简化几何体
						if (child.geometry) {
							// 移除不必要的属性
							if (child.geometry.attributes.uv2) {
								delete child.geometry.attributes.uv2;
							}
							if (child.geometry.attributes.tangent) {
								delete child.geometry.attributes.tangent;
							}
						}
						
						// 强制使用最简单的材质
						if (child.material) {
							const originalColor = child.material.color || new THREE.Color(0x888888);
							
							// 创建最基础的材质
							const basicMaterial = new THREE.MeshBasicMaterial({
								color: originalColor,
								side: THREE.DoubleSide,
								transparent: false,
								fog: false
							});
							
							// 清理旧材质
							if (Array.isArray(child.material)) {
								child.material.forEach(m => m.dispose());
							} else {
								child.material.dispose();
							}
							
							child.material = basicMaterial;
							child.castShadow = false;
							child.receiveShadow = false;
						}
					}
				});
			},

			onWindowResize() {
				const container = document.getElementById('threeView');
				const width = container.clientWidth || window.innerWidth;
				const height = container.clientHeight || window.innerHeight;
				
				camera.aspect = width / height;
				camera.updateProjectionMatrix();
				renderer.setSize(width, height);
				
				// 窗口大小改变后需要重新渲染
				this.markNeedsRender();
			},

			// 按需渲染系统 - 替代持续的animate循环
			requestRender() {
				if (!this.needsRender || this.isRendering) {
					return;
				}
				
				this.needsRender = false;
				this.isRendering = true;
				
				requestAnimationFrame(() => {
					this.render();
					this.isRendering = false;
				});
			},
			
			// 优化的防抖渲染方法，基于Three.js最佳实践
			renderWithThrottle() {
				if (this.renderThrottleId) {
					return;
				}
				
				this.renderThrottleId = requestAnimationFrame(() => {
					this.renderThrottleId = null;
					this.render();
				});
			},
			
			// Three.js推荐的按需渲染模式
			requestRenderIfNotRequested() {
				if (!this.needsRender) {
					this.needsRender = true;
					requestAnimationFrame(() => {
						this.needsRender = false;
						this.render();
					});
				}
			},
			
			// 实际渲染方法
			render() {
				if (!renderer || !scene || !camera) {
					return;
				}
				
				// 渲染场景
				renderer.render(scene, camera);
				
				// 记录渲染时间
				this.lastRenderTime = performance.now();
			},
			
			// 标记需要重新渲染 - 使用Three.js推荐模式
			markNeedsRender() {
				this.requestRenderIfNotRequested();
			},
			
			// 处理阻尼结束后的渲染
			handleDampingEnd() {
				const dampingRender = () => {
					if (!controls || !controls.enableDamping) {
						return;
					}
					
					// 更新控制器并检查是否还在运动
					const needsUpdate = controls.update();
					renderer.render(scene, camera);
					
					// 如果控制器还在运动，继续渲染
					if (needsUpdate) {
						requestAnimationFrame(dampingRender);
					}
				};
				
				dampingRender();
			},
			
			// 用户交互时的连续渲染
			startInteractiveRender() {
				if (this.isUserInteracting) {
					return;
				}
				
				this.isUserInteracting = true;
				// 不再启动连续渲染循环，因为change事件已经处理渲染
			},
			
			// 交互式渲染循环（已废弃，由change事件直接处理）
			interactiveRenderLoop() {
				// 此方法已不再使用，保留以防兼容性问题
				console.warn('interactiveRenderLoop已废弃，现在由change事件直接处理渲染');
			},
			
			// 停止交互式渲染
			stopInteractiveRender() {
				this.isUserInteracting = false;
				
				// 清除交互超时
				if (this.interactionTimeout) {
					clearTimeout(this.interactionTimeout);
				}
				
				// 交互结束后立即渲染最终状态，无需延迟
				this.render();
			},
			
			// 兼容旧的animate方法调用
			animate() {
				console.warn('animate()方法已被按需渲染系统替代，请使用markNeedsRender()');
				this.markNeedsRender();
			},

			loadNewModel(url) {
				this.loadModel(url);
			},

			switchBackground(index) {
				this.changeBackground(index);
			},

			applyColor(componentName, optionData) {
				this.setMaterialColor(componentName, optionData);
			},

			// 截图功能（支持添加标题到右下角）
			captureScreenshot(options = {}) {
				return new Promise((resolve, reject) => {
					try {
						if (!renderer || !scene || !camera || !controls || !model) {
							reject(new Error('3D场景未初始化'));
							return;
						}

						const {
							width = 800,
							height = 574,
							format = 'image/jpeg',
							quality = 0.7
						} = options;

						// 保存当前相机状态
						const originalPosition = camera.position.clone();
						const originalTarget = controls.target.clone();
						const originalAspect = camera.aspect;
						
						// 重置到初始化位置
						const box = new THREE.Box3().setFromObject(model);
						const center = box.getCenter(new THREE.Vector3());
						const size = box.getSize(new THREE.Vector3());
						const maxDim = Math.max(size.x, size.y, size.z);
						
						controls.target.copy(center);
						const cameraDistance = maxDim * 1.8; // 与主相机保持一致的距离系数

						// 设置相机宽高比为目标比例
						const targetAspect = width / height;
						camera.aspect = targetAspect;
						camera.updateProjectionMatrix();

						// 调整相机位置以确保模型完全可见
						const fov = camera.fov * Math.PI / 180;
						const cameraHeight = Math.abs(maxDim / Math.sin(fov / 2));
						const targetDistance = cameraHeight / 2;

						camera.position.set(
							center.x,
							center.y,
							center.z + targetDistance
						);
						camera.lookAt(center);
						controls.update();

						// 保存当前渲染器尺寸
						const originalSize = renderer.getSize(new THREE.Vector2());
						const originalPixelRatio = renderer.getPixelRatio();

						// 设置截图尺寸
						renderer.setSize(width, height);
						renderer.setPixelRatio(1);

						// 渲染一帧
						renderer.render(scene, camera);

						// 获取canvas数据
						const canvas = renderer.domElement;
						const dataURL = canvas.toDataURL(format, quality);

						// 恢复原始相机状态
						camera.position.copy(originalPosition);
						camera.aspect = originalAspect;
						camera.updateProjectionMatrix();
						controls.target.copy(originalTarget);
						camera.lookAt(originalTarget);
						controls.update();

						// 恢复原始尺寸
						renderer.setSize(originalSize.x, originalSize.y);
						renderer.setPixelRatio(originalPixelRatio);

						// 重新渲染以恢复显示
						this.markNeedsRender();

						resolve({
							dataURL,
							width,
							height,
							format
						});
					} catch (error) {
						console.error('截图失败:', error);
						reject(error);
					}
				});
			},

			// 在canvas上添加标题到右下角
			addTitleToCanvas(sourceCanvas, title, titleStyle = {}, format = 'image/webp', quality = 0.9) {
				// 创建新的canvas用于绘制标题
				const newCanvas = document.createElement('canvas');
				const ctx = newCanvas.getContext('2d');
				
				// 设置新canvas尺寸与原canvas相同
				newCanvas.width = sourceCanvas.width;
				newCanvas.height = sourceCanvas.height;
				
				// 先绘制原始图片
				ctx.drawImage(sourceCanvas, 0, 0);
				
				// 设置标题样式
				const style = {
					fontSize: titleStyle.fontSize || Math.max(16, Math.floor(sourceCanvas.width / 40)),
					fontFamily: titleStyle.fontFamily || 'Arial, sans-serif',
					color: titleStyle.color || '#ffffff',
					backgroundColor: titleStyle.backgroundColor || 'rgba(0, 0, 0, 0.6)',
					padding: titleStyle.padding || 12,
					borderRadius: titleStyle.borderRadius || 8,
					margin: titleStyle.margin || 20
				};
				
				// 设置字体
				ctx.font = `${style.fontSize}px ${style.fontFamily}`;
				ctx.textAlign = 'right';
				ctx.textBaseline = 'bottom';
				
				// 测量文字尺寸
				const textMetrics = ctx.measureText(title);
				const textWidth = textMetrics.width;
				const textHeight = style.fontSize;
				
				// 计算背景框的位置和尺寸
				const bgWidth = textWidth + style.padding * 2;
				const bgHeight = textHeight + style.padding * 2;
				const bgX = newCanvas.width - bgWidth - style.margin;
				const bgY = newCanvas.height - bgHeight - style.margin;
				
				// 绘制背景框
				if (style.backgroundColor !== 'transparent') {
					ctx.fillStyle = style.backgroundColor;
					if (style.borderRadius > 0) {
						// 绘制圆角矩形
						this.drawRoundedRect(ctx, bgX, bgY, bgWidth, bgHeight, style.borderRadius);
					} else {
						ctx.fillRect(bgX, bgY, bgWidth, bgHeight);
					}
				}
				
				// 绘制文字
				ctx.fillStyle = style.color;
				ctx.fillText(
					title, 
					newCanvas.width - style.margin - style.padding, 
					newCanvas.height - style.margin - style.padding
				);
				
				// 返回带标题的图片数据
				return newCanvas.toDataURL(format, quality);
			},

			// 绘制圆角矩形
			drawRoundedRect(ctx, x, y, width, height, radius) {
				ctx.beginPath();
				ctx.moveTo(x + radius, y);
				ctx.lineTo(x + width - radius, y);
				ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
				ctx.lineTo(x + width, y + height - radius);
				ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
				ctx.lineTo(x + radius, y + height);
				ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
				ctx.lineTo(x, y + radius);
				ctx.quadraticCurveTo(x, y, x + radius, y);
				ctx.closePath();
				ctx.fill();
			},

			// 高质量截图（重新渲染指定尺寸）
			captureHighQualityScreenshot(options = {}) {
				return new Promise((resolve, reject) => {
					try {
						if (!scene || !camera) {
							reject(new Error('3D场景未初始化'));
							return;
						}

						const {
							width = 1920,
							height = 1080,
							format = 'image/webp',
							quality = 1.0,
							antialias = true
						} = options;

						// 创建临时渲染器用于高质量截图
						const tempRenderer = new THREE.WebGLRenderer({
							antialias: antialias,
							preserveDrawingBuffer: true,
							alpha: true
						});

						tempRenderer.setSize(width, height);
						tempRenderer.setPixelRatio(1); // 使用标准像素比
						tempRenderer.outputColorSpace = THREE.SRGBColorSpace;
						tempRenderer.toneMapping = THREE.ACESFilmicToneMapping;
						tempRenderer.toneMappingExposure = 1.2;

						// 临时调整相机宽高比
						const originalAspect = camera.aspect;
						camera.aspect = width / height;
						camera.updateProjectionMatrix();

						// 渲染到临时canvas
						tempRenderer.render(scene, camera);

						// 获取截图数据
						const canvas = tempRenderer.domElement;
						const dataURL = canvas.toDataURL(format, quality);

						// 恢复相机原始宽高比
						camera.aspect = originalAspect;
						camera.updateProjectionMatrix();

						// 清理临时渲染器
						tempRenderer.dispose();

						if (dataURL === 'data:,') {
							reject(new Error('高质量截图失败：canvas为空'));
							return;
						}

						resolve({
							dataURL: dataURL,
							width: width,
							height: height,
							format: format,
							timestamp: Date.now(),
							isHighQuality: true
						});

					} catch (error) {
						console.error('高质量截图过程中发生错误:', error);
						reject(error);
					}
				});
			},

			// 从不同角度截图
			captureFromAngle(angle = 'front', options = {}) {
				return new Promise((resolve, reject) => {
					try {
						if (!camera || !controls || !model) {
							reject(new Error('3D场景或模型未准备好'));
							return;
						}

						// 保存当前相机状态
						const originalPosition = camera.position.clone();
						const originalTarget = controls.target.clone();

						// 计算模型中心和边界
						const box = new THREE.Box3().setFromObject(model);
						const center = box.getCenter(new THREE.Vector3());
						const size = box.getSize(new THREE.Vector3());
						const maxDim = Math.max(size.x, size.y, size.z);
						
						// 根据模型大小调整距离，确保模型完整显示
						const distance = maxDim * 1.8; // 与主相机保持一致的距离系数
						
						// 设置相机高度为模型中心高度，确保水平视角
						const cameraHeight = center.y;

						// 设置不同角度的相机位置（固定角度）
						let newPosition;
						switch (angle) {
							case 'front':
								// 正面：从Z轴正方向看
								newPosition = new THREE.Vector3(center.x, cameraHeight, center.z + distance);
								break;
							case 'back':
								// 背面：从Z轴负方向看
								newPosition = new THREE.Vector3(center.x, cameraHeight, center.z - distance);
								break;
							case 'left':
								// 左侧面：从X轴负方向看（模型的左侧）
								newPosition = new THREE.Vector3(center.x - distance, cameraHeight, center.z);
								break;
							case 'right':
								// 右侧面：从X轴正方向看（模型的右侧）
								newPosition = new THREE.Vector3(center.x + distance, cameraHeight, center.z);
								break;
							case 'left-side':
								// 标准左侧面截图：确保完全侧面视角
								newPosition = new THREE.Vector3(center.x - distance, cameraHeight, center.z);
								break;
							case 'right-side':
								// 标准右侧面截图：确保完全侧面视角
								newPosition = new THREE.Vector3(center.x + distance, cameraHeight, center.z);
								break;
							case 'top':
								// 顶部：从Y轴正方向看
								newPosition = new THREE.Vector3(center.x, center.y + distance, center.z);
								break;
							case 'bottom':
								// 底部：从Y轴负方向看
								newPosition = new THREE.Vector3(center.x, center.y - distance, center.z);
								break;
							case 'front-right':
								// 右前方：45度角
								newPosition = new THREE.Vector3(
									center.x + distance * 0,
									cameraHeight + distance * 0.2,
									center.z + distance * 0.707
								);
								break;
							case 'front-left':
								// 左前方：45度角
								newPosition = new THREE.Vector3(
									center.x - distance * 0.707,
									cameraHeight + distance * 0.2,
									center.z + distance * 0.707
								);
								break;
							case 'profile-left':
								// 标准左侧轮廓截图（完全侧面，稍微从上方）
								newPosition = new THREE.Vector3(
									center.x - distance,
									cameraHeight + distance * 0.1,
									center.z
								);
								break;
							case 'profile-right':
								// 标准右侧轮廓截图（完全侧面，稍微从上方）
								newPosition = new THREE.Vector3(
									center.x + distance,
									cameraHeight + distance * 0.1,
									center.z
								);
								break;
							default:
								newPosition = originalPosition.clone();
						}

						// 设置相机位置和目标
						camera.position.copy(newPosition);
						camera.lookAt(center);
						controls.target.copy(center);
						controls.update();

						// 等待一帧确保渲染完成
						requestAnimationFrame(() => {
							// 进行截图
							this.captureScreenshot(options).then((result) => {
								// 恢复原始相机状态
								camera.position.copy(originalPosition);
								controls.target.copy(originalTarget);
								camera.lookAt(originalTarget);
								controls.update();

								resolve({
									...result,
									angle: angle
								});
							}).catch((error) => {
								// 恢复原始相机状态
								camera.position.copy(originalPosition);
								controls.target.copy(originalTarget);
								camera.lookAt(originalTarget);
								controls.update();

								reject(error);
							});
						});

					} catch (error) {
						console.error('角度截图过程中发生错误:', error);
						reject(error);
					}
				});
			},

			// 带水印元素的截图功能（logo、二维码、标题）
			captureScreenshotWithElements(options = {}) {
				return new Promise((resolve, reject) => {
					try {
						const {
							width = 800,
							height = 574,
							format = 'image/webp',
							quality = 0.9,
							// 水印元素配置
							logo = null, // { url: '', size: 0.15, opacity: 0.8 }
							qrCode = null, // { url: '', size: 0.12, opacity: 0.8 }
							title = null, // { text: '', fontSize: 24, color: '#000', opacity: 0.8 }
							// 全局配置
							padding = 0.05, // 边距比例，增加边距避免截断
							defaultOpacity = 1
						} = options;

						// 如果没有任何水印元素，直接返回基础截图
						if (!logo && !qrCode && !title) {
							return this.captureScreenshot({ width, height, format, quality });
						}

						// 首先获取基础截图
						this.captureScreenshot({ width: 1600, height: 1200, format, quality }) // 使用较大的尺寸以保证质量
							.then(result => {
								// 创建主图片对象
								const mainImage = new Image();
								mainImage.crossOrigin = 'anonymous';
								mainImage.onload = () => {
									const originalWidth = mainImage.width;
									const originalHeight = mainImage.height;
									const targetRatio = width / height;
									const originalRatio = originalWidth / originalHeight;

									let sx, sy, sw, sh;

									if (originalRatio > targetRatio) {
										// 原图太宽 → 裁掉左右多余
										sh = originalHeight;
										sw = originalHeight * targetRatio;
										sx = (originalWidth - sw) / 2;
										sy = 0;
									} else {
										// 原图太高 → 裁掉上下多余
										sw = originalWidth;
										sh = originalWidth / targetRatio;
										sx = 0;
										sy = (originalHeight - sh) / 2;
									}

									// 创建canvas用于合成
									const canvas = document.createElement('canvas');
									canvas.width = width;
									canvas.height = height;
									const ctx = canvas.getContext('2d');

									// ✨ 裁剪并等比绘制
									ctx.drawImage(mainImage, sx, sy, sw, sh, 0, 0, width, height);

									let loadedCount = 0;
									const totalElements = (logo ? 1 : 0) + (qrCode ? 1 : 0) + (title ? 1 : 0);
									
									const checkComplete = () => {
										loadedCount++;
										if (loadedCount >= totalElements) {
											// 所有元素处理完成，生成最终图片
											const finalDataURL = canvas.toDataURL(format, quality);
											resolve({
												dataURL: finalDataURL,
												width,
												height,
												format,
												hasElements: true
											});
										}
									};

									// 处理logo（左上角）
									if (logo && logo.url) {
										console.log('开始加载logo:', logo);
										const logoImage = new Image();
										logoImage.crossOrigin = 'anonymous';
										logoImage.onload = () => {
											console.log('Logo图片加载成功:', {
												width: logoImage.width,
												height: logoImage.height,
												url: logo.url
											});
											
											const logoSize = Math.min(width, height) * (logo.size || 0.25);
											// 根据position属性决定logo位置
											let logoX, logoY;
											switch (logo.position) {
												case 'top-right':
													logoX = width - logoSize - (width * padding);
													logoY = height * padding;
													break;
												case 'bottom-left':
													logoX = width * padding;
													logoY = height - logoSize - (height * padding);
													break;
												case 'bottom-right':
													logoX = width - logoSize - (width * padding);
													logoY = height - logoSize - (height * padding);
													break;
												case 'top-left':
												default:
													logoX = width * padding;
													logoY = height * padding;
													break;
											}
											
											console.log('Logo渲染参数:', {
												position: logo.position,
												x: logoX,
												y: logoY,
												size: logoSize,
												opacity: logo.opacity || defaultOpacity
											});

											// 保存当前上下文状态
											ctx.save();
											
											// 设置透明度
											ctx.globalAlpha = logo.opacity || defaultOpacity;
											
											// 绘制logo
											try {
												ctx.drawImage(logoImage, logoX, logoY, logoSize, logoSize * (logoImage.height / logoImage.width));
												console.log('Logo绘制完成');
											} catch (error) {
												console.error('Logo绘制失败:', error);
											}
											
											// 恢复上下文状态
											ctx.restore();
											
											checkComplete();
										};
										logoImage.onerror = (error) => {
											console.error('Logo加载失败:', {
												url: logo.url,
												error: error
											});
											checkComplete();
										};
										console.log('设置logo图片源:', logo.url);
										logoImage.src = logo.url;
									} else if (logo) {
										console.warn('Logo配置无效:', logo);
										checkComplete();
									} else {
										console.log('没有logo配置');
										checkComplete();
									}

									// 处理二维码（右上角）
									if (qrCode && qrCode.url) {
										const qrImage = new Image();
										qrImage.crossOrigin = 'anonymous';
										qrImage.onload = () => {
											const qrSize = Math.min(width, height) * (qrCode.size || 0.12);
											const qrX = width - qrSize - (width * padding);
											const qrY = height * padding;
											
											ctx.globalAlpha = qrCode.opacity || defaultOpacity;
											ctx.drawImage(qrImage, qrX, qrY, qrSize, qrSize);
											ctx.globalAlpha = 1.0;
											
											checkComplete();
										};
										qrImage.onerror = () => {
											console.warn('二维码图片加载失败，跳过二维码');
											checkComplete();
										};
										qrImage.src = qrCode.url;
									} else if (qrCode) {
										checkComplete();
									}

									// 处理标题（右下角）
									if (title && title.text) {
										const fontSize = title.fontSize || Math.min(width, height) * 0.03;
										const titleX = width - (width * padding);
										const titleY = height - (height * padding) - fontSize * 0.3;
										
										ctx.globalAlpha = title.opacity || defaultOpacity;
										ctx.fillStyle = title.color || '#000';
										console.log("🚀 ~ file: 3d.vue:2361 ~ title.color:", title.color)
										ctx.font = `${fontSize}px Arial`;
										ctx.textAlign = 'right';
										ctx.textBaseline = 'bottom';
										
										// 添加文字阴影效果
										if (title.shadow !== false) {
											ctx.shadowColor = 'rgba(0,0,0,0.5)';
											ctx.shadowBlur = 2;
											ctx.shadowOffsetX = 1;
											ctx.shadowOffsetY = 1;
										}
										
										ctx.fillText(title.text, titleX, titleY);
										
										// 重置阴影
										ctx.shadowColor = 'transparent';
										ctx.shadowBlur = 0;
										ctx.shadowOffsetX = 0;
										ctx.shadowOffsetY = 0;
										ctx.globalAlpha = 1.0;
										
										checkComplete();
									} else if (title) {
										checkComplete();
									}
								};
								
								mainImage.onerror = () => {
									reject(new Error('主图片加载失败'));
								};
								
								mainImage.src = result.dataURL;
							})
							.catch(reject);
					} catch (error) {
						console.error('带水印元素截图失败:', error);
						reject(error);
					}
				});
			},

			// 兼容旧接口的二维码截图方法
			captureScreenshotWithQR(options = {}) {
				const { qrCodeUrl, qrCodeData, ...otherOptions } = options;
				
				// 转换为新的配置格式
				const newOptions = {
					...otherOptions,
					qrCode: qrCodeUrl ? { url: qrCodeUrl } : null
				};
				
				return this.captureScreenshotWithElements(newOptions);
			},

			// 便捷截图方法 - 供外部调用
			takeScreenshot(withElements = false, options = {}) {
				if (withElements) {
					return this.captureScreenshotWithElements(options);
				} else {
					return this.captureScreenshot(options);
				}
			},

			// 下载截图
			downloadScreenshot(withElements = false, filename = '3d-model-screenshot', options = {}) {
				return this.takeScreenshot(withElements, options)
					.then(result => {
						// 创建下载链接
						const link = document.createElement('a');
						link.download = `${filename}.${result.format.split('/')[1] || 'png'}`;
						link.href = result.dataURL;
						
						// 触发下载
						document.body.appendChild(link);
						link.click();
						document.body.removeChild(link);
						
						return result;
					})
					.catch(error => {
						console.error('下载截图失败:', error);
						throw error;
					});
			},

			// 处理初始化错误
			handleInitError(error) {
				console.error('3D初始化错误:', error);
				
				// 检测是否为iOS 15.1
				const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
				let isProblematicIOS = false;
				let iosVersion = null;
				
				if (isIOS) {
					const match = navigator.userAgent.match(/OS (\d+)_(\d+)/);
					if (match) {
						iosVersion = `${match[1]}.${match[2]}`;
						isProblematicIOS = iosVersion === '15.1' || iosVersion === '15.0';
					}
				}
				
				// 根据错误类型显示不同的提示
				let errorMessage = '3D显示功能暂不可用';
				let errorDetail = '未知错误';
				let suggestions = '建议使用Chrome、Safari或Firefox浏览器';
				
				if (isProblematicIOS) {
					errorMessage = `iOS ${iosVersion} 兼容性问题`;
					errorDetail = 'iOS 15.1版本的Safari存在WebGL兼容性问题';
					suggestions = '建议升级到iOS 15.2或更高版本，或使用Chrome浏览器';
				} else if (error.message.includes('WebGL')) {
					errorMessage = '设备不支持3D显示';
					errorDetail = '您的设备或浏览器不支持WebGL技术';
				} else if (error.message.includes('threeView')) {
					errorMessage = '3D容器加载失败';
					errorDetail = '页面元素未正确加载';
				} else {
					errorDetail = error.message || '初始化过程中发生未知错误';
				}
				
				const container = document.getElementById('threeView');
				if (container) {
					const iconEmoji = isProblematicIOS ? '⚠️' : '📱';
					const bgColor = isProblematicIOS ? 'linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%)' : 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)';
					
					container.innerHTML = `
						<div style="
							display: flex;
							align-items: center;
							justify-content: center;
							height: 100%;
							background: ${bgColor};
							color: #666;
							text-align: center;
							padding: 20px;
							flex-direction: column;
							font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
						">
							<div style="
								background: white;
								padding: 30px;
								border-radius: 12px;
								box-shadow: 0 4px 20px rgba(0,0,0,0.1);
								max-width: 320px;
								border: ${isProblematicIOS ? '2px solid #f39c12' : 'none'};
							">
								<div style="font-size: 48px; margin-bottom: 16px;">${iconEmoji}</div>
								<h3 style="margin: 0 0 12px 0; color: #333; font-size: 16px;">${errorMessage}</h3>
								<p style="margin: 0 0 16px 0; font-size: 12px; color: #666; line-height: 1.4;">${errorDetail}</p>
								<p style="margin: 0; font-size: 10px; color: ${isProblematicIOS ? '#e67e22' : '#999'}; font-weight: ${isProblematicIOS ? 'bold' : 'normal'};">${suggestions}</p>
							</div>
						</div>
					`;
				}
				
				// 通知父组件
				this.$emit('init-error', { error, message: errorMessage, detail: errorDetail });
			}
		},

		beforeDestroy() {
			// 清理全局引用
			if (window.vue3DViewer === this) {
				window.vue3DViewer = null;
			}
			
			window.removeEventListener('resize', this.onWindowResize);
			
			// 清理按需渲染相关的定时器
			if (this.renderTimeout) {
				clearTimeout(this.renderTimeout);
				this.renderTimeout = null;
			}
			
			if (this.interactionTimeout) {
				clearTimeout(this.interactionTimeout);
				this.interactionTimeout = null;
			}
			
			// 清理防抖渲染
			if (this.renderThrottleId) {
				cancelAnimationFrame(this.renderThrottleId);
				this.renderThrottleId = null;
			}
			
			// 停止交互式渲染
			this.stopInteractiveRender();
			
			if (model) {
				this.disposeModel(model);
			}
			
			if (renderer) {
				renderer.dispose();
				renderer.forceContextLoss();
				renderer.domElement = null;
			}
			
			if (ktx2Worker) {
				ktx2Worker.terminate();
			}
			
			scene = null;
			camera = null;
			controls = null;
			renderer = null;
			model = null;
			gltfLoader = null;
			ktx2Worker = null;
		}
	}
	// #endif
</script>

<style scoped>
.three-viewer-container {
	position: relative;
	width: 100%;
	height: 100%;
}

.three-canvas {
	width: 100%;
	height: 100%;
}

.loading-container {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: rgba(240, 240, 240, 0.8);
	z-index: 10;
}

.loading-bar-container {
	position: relative;
	width: 300px;
	height: 20px;
	background: #ddd;
	border-radius: 10px;
	overflow: hidden;
}

.loading-bar {
	height: 100%;
	background: #4caf50;
	transition: width 0.3s ease;
}

.loading-text {
	position: absolute;
	width: 100%;
	text-align: center;
	top: 0;
	line-height: 20px;
	font-size: 14px;
	color: #333;
	user-select: none;
}
</style>