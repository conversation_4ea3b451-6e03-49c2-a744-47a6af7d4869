<template>
	<view class="configurator">
		<div class="three-container">
			<!-- Loading 遮罩 -->
			<u-loading-page :loading="true" v-if="isScreenshotLoading" loading-text="Generating Screenshot..."
				loading-mode="circle" bg-color="rgba(0,0,0,0.7)" color="#ffffff"
				style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;z-index: 18;" />

			<three3D ref="threeDViewer" :modeResult="modeResult" :modeFile="modeFile" @iframe-ready="onIframeReady"
				:selectedOptions="selectedOptionsFor3D" @load-success="onLoadSuccess" />
			<div class="three-bg-btn">
				<img v-for="(bg, index) in backgroundOptions" :key="index" :src="bg.icon" :alt="bg.name"
					:class="{ active: currentBgIndex === index + 1 }" @click="changeBg(index + 1)">
			</div>
		</div>

		<!-- 二维码组件，隐藏但用于生成二维码，只在需要时渲染 -->
		<u-qrcode v-if="openQrCode" ref="qrcode" canvas-id="qrcode" :value="qrCodeUrl" :options="qrCodeOptions"
			:hide="true" @complete="onQrCodeComplete" />


		<!-- 截图结果弹窗 -->
		<uni-popup ref="screenshotPopup" type="center" :mask-click="false" :safe-area="true" style="z-index: 1001;">
			<view class="screenshot-popup">
				<view class="screenshot-header">
					<text class="screenshot-title">Screenshot Generated</text>
					<view class="close-btn" @click="closeScreenshotPopup">
						<text class="close-icon">×</text>
					</view>
				</view>
				<view class="screenshot-content">
					<image v-if="screenshotResult" :src="screenshotResult" class="screenshot-image" mode="aspectFill" />
				</view>
				<view class="screenshot-actions">
					<button class="action-btn save-btn" @click="saveScreenshot">Save to Album</button>
				</view>
			</view>
		</uni-popup>

		<view class="options-container" :class="{ 'disabled': !is3DViewerReady }">

			<!-- 产品标题 -->
			<view class="product-title">{{ productName }}</view>

			<!-- 可滚动内容区域 -->
			<view class="scrollable-content">
				<view class="section">
					<view class="section-title">Component</view>
					<scroll-view scroll-x="true" class="scroll-view">
						<view class="options-list-inline">
							<view v-for="attr in attributes" :key="attr.AttrId"
								:class="['option-chip', { 'selected': isAttributeSelected(attr) }]"
								@click="selectAttribute(attr)">
								{{ attr.Name_en }}
							</view>
						</view>
					</scroll-view>
				</view>

				<view v-if="selectedAttribute">
					<view v-if="selectedAttribute.childStatus === 1">
						<view class="section" v-for="subAttr in selectedAttribute.Options" :key="subAttr.CAId">
							<view class="section-title">{{ subAttr.SubcomponentTitle }}</view>
							<view class="options-list">
								<view v-for="option in subAttr.Options" :key="option.OptionId" class="option-item-image"
									@click="selectOption(option, subAttr, selectedAttribute)">
									<image :src="option.PicPath" class="option-image"
										:class="{ 'selected-border': isSubOptionSelected(option, subAttr) }" />
									<view class="option-price">{{ formatPrice(getOptionPrice(option.OptionId),
										'+' + priceSymbol) }}
									</view>
								</view>
							</view>
						</view>
					</view>
					<view v-else class="section">
						<view class="section-title">{{ selectedAttribute.Name_en }}</view>
						<view class="options-list">
							<view v-for="option in selectedAttribute.Options" :key="option.OptionId"
								class="option-item-color" @click="selectOption(option, selectedAttribute, null)">
								<view class="color-swatch" :style="{ backgroundColor: option.optionColor || '#fff' }"
									:class="{ 'selected-border': isOptionSelected(option, selectedAttribute) }">
									<image v-if="option.PicPath" :src="option.PicPath" class="texture-swatch" />
								</view>
								<view class="option-price">{{ formatPrice(getOptionPrice(option.OptionId), '+' +
									priceSymbol)
								}}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<footer-options-box v-if="SoldOut != 1" :showService="false" style="z-index: 1000;">
			<template slot="optionsRight">
				<threeOrBuyNow :Price="totalPrice.toFixed(2)" @handleAddToCart="handleAddToCart"
					@handlePayment="handleAddToCart"></threeOrBuyNow>
			</template>
		</footer-options-box>


	</view>
</template>

<script>
import three3D from './three3D.vue';
import { PriceCalculator } from '../../utils/skuManager.js';
import { mapGetters } from 'vuex';
import config from '@/config';
import uQrcode from '@/uni_modules/Sansnn-uQRCode/components/u-qrcode/u-qrcode.vue';
// 常量配置
const BACKGROUND_OPTIONS = [
	{ name: 'Studio', icon: require('@/static/assets/common/3dbg-1.png') },
	// { name: 'Garden', icon: require('@/static/assets/common/3dbg-2.png') },
	// { name: 'Sunset', icon: require('@/static/assets/common/3dbg-3.png') }
];

const DEFAULT_BG_INDEX = 2;
const RENDER_DELAY = 0;

export default {
	name: 'ThreeDViewer',
	components: {
		three3D,
		threeOrBuyNow: () => import('./buy.vue'),
		'u-qrcode': uQrcode,
	},
	props: {
		SoldOut: {
			type: String,
			default: '0'
		},
		productData: {
			type: Object,
			required: true,
			validator(value) {
				// 验证productData是否为有效对象，而不是错误字符串
				return value && typeof value === 'object' && !Array.isArray(value) && typeof value !== 'string';
			}
		},
		cartAttributes: {
			type: Object,
			default: () => ({})
		},
		quantity: {
			type: Number,
			default: 1
		},
		cartCId: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			attributes: [],
			modeResult: {},
			modeFile: '',
			is3DViewerReady: false,
			selectedOptionIdMap: {},
			currentBgIndex: DEFAULT_BG_INDEX,
			backgroundOptions: BACKGROUND_OPTIONS,
			optionPriceCache: {},
			selectedAttribute: {},
			basePrice: 0,
			// 二维码相关
			qrCodeUrl: '',
			qrCodeTempPath: '',
			isQrCodeReady: false,
			// 截图相关
			isScreenshotLoading: false,
			screenshotResult: '',
			openQrCode: false,
		};
	},
	computed: {
		...mapGetters(['priceSymbol']),

		productName() {
			return this.productData?.pro?.Name_en || 'Product';
		},
		totalPrice() {
			// 使用Decimal.js精确计算总价
			return this.calculateTotalPrice();
		},
		selectedOptionsFor3D() {
			return this.generateOptionsFor3D();
		},
		// 二维码配置选项
		qrCodeOptions() {
			const options = {
				margin: 10,
				size: 200,
				correctLevel: 'M',
				backgroundColor: '#ffffff',
				foregroundColor: '#000000'
			};

			// 如果有产品图标，添加到二维码中心
			if (this.productData?.ico_path) {
				options.foregroundImageSrc = this.productData.ico_path;
				options.foregroundImageWidth = 40;
				options.foregroundImageHeight = 40;
				options.foregroundImageBackgroundColor = '#ffffff';
				options.foregroundImageBorderRadius = 8;
				options.foregroundImagePadding = 2;
			}

			return options;
		}
	},
	watch: {
		productData: {
			immediate: true,
			handler: 'handleProductDataChange'
		}
	},
	created() {
		// 初始化APP回调监听
		this.initAppCallback();
	},
	methods: {
		/**
		 * 递归处理嵌套的Options结构
		 * @param {Array} options - 选项数组
		 * @param {Array} checkId - 需要检查的ID数组
		 */
		processOptionsRecursively(options, checkId) {
			if (!options || !Array.isArray(options)) {
				return;
			}

			for (let option of options) {
				// 处理当前层级的选项
				if (option.OptionId) {
					const optionIdStr = option.OptionId.toString();
					option.IsDefault = checkId.includes(optionIdStr) ? '1' : '0';
				}

				// 如果当前选项还有子Options，递归处理
				if (option.Options && Array.isArray(option.Options) && option.Options.length > 0) {
					this.processOptionsRecursively(option.Options, checkId);
				}
			}
		},

		// ==================== 生命周期相关 ====================
		onIframeReady() {
			this.is3DViewerReady = true;
			console.log('3D视图已准备好');
			// this.tryInitializeRender();
		},

		// 初始化APP回调监听
		initAppCallback() {
			// 设置全局回调函数，供APP调用
			window.receiveSaveImageCallback = (callbackId, result) => {
				console.log('🔄 Received saveImage callback:', callbackId, result);
				if (window[callbackId]) {
					window[callbackId](result);
				} else {
					console.warn('⚠️ Callback function not found:', callbackId);
				}
			};

			// 监听页面消息（用于uni-app plus环境）
			window.addEventListener('message', (event) => {
				if (event.data && event.data.type === 'saveImageCallback') {
					console.log('📬 Received message saveImage callback:', event.data);
					const { callbackId, result } = event.data;
					if (window[callbackId]) {
						window[callbackId](result);
					}
				}
			});

			// 专门为iOS调试添加的测试回调函数
			window.testIOSCallback = (callbackId, result = 1) => {
				console.log(`🧪 测试iOS回调: callbackId=${callbackId}, result=${result}`);
				if (window[callbackId]) {
					window[callbackId](result);
					console.log('✅ 测试回调执行成功');
				} else {
					console.warn('❌ 测试回调失败: callback function not found');
				}
			};

			console.log('🚀 APP callback listeners initialized');
		},

		onLoadSuccess() {
			this.is3DViewerReady = true;
			console.log('3D视图已准备好');
			this.tryInitializeRender();
		},

		handleProductDataChange(newData) {
			// 验证数据有效性
			if (!newData || typeof newData !== 'object' || Array.isArray(newData) || typeof newData === 'string') {
				console.warn('ThreeDViewer: 接收到无效的productData:', newData);
				return;
			}

			if (newData?.modeResult) {
				// 设置基础价格
				this.basePrice = parseFloat(newData?.pro?.Price || 0);
				if (JSON.stringify(this.cartAttributes) !== '{}') {
					for (let key of newData.modeResult.attributes) {
						for (let keys in this.cartAttributes) {
							if (key.AttrId === keys) {
								let checkId = this.cartAttributes[keys].split(',');

								if (key.Options) {
									// 使用递归函数处理嵌套的Options
									this.processOptionsRecursively(key.Options, checkId);
								}
							}
						}
					}
				}
				this.processAttributes(newData.modeResult.attributes);
				this.modeFile = newData.modeResult.ModeFile || '';
				this.modeResult = newData.modeResult;

				// 处理回显数据
				if (newData.cartSelectSkuData && newData.cartSelectSkuData.Attr) {
					console.log('处理回显数据:', newData.cartSelectSkuData.Attr);
					this.$nextTick(() => {
						this.handleEchoData(newData.cartSelectSkuData.Attr);
					});
				}
			}
		},

		// ==================== 数据处理 ====================
		processAttributes(attrs) {
			if (!attrs?.length) return;

			this.resetSelections();
			const processed = this.deepClone(attrs);

			processed.forEach(attr => {
				this.enhanceAttribute(attr);
				this.processAttributeOptions(attr);
			});

			this.attributes = processed;

			// 构建选项价格缓存
			this.buildOptionPriceCache(processed);

			this.setDefaultSelectedAttribute();
			this.logProcessingResults();
			this.tryInitializeRender();
		},

		resetSelections() {
			this.selectedOptionIdMap = {};
		},

		enhanceAttribute(attr) {
			attr.isMultiSelect = attr.OptType === '1';
			attr.isRequired = attr.IsRequired === '1';
		},

		processAttributeOptions(attr) {
			if (attr.childStatus === 1) {
				this.processComplexAttribute(attr);
			} else {
				this.processSimpleAttribute(attr);
			}
		},

		processComplexAttribute(attr) {
			attr.Options?.forEach(subAttr => {
				const componentKey = this.getComponentKey(subAttr, true);
				if (subAttr.IsRequired === '1' && subAttr.Options?.length) {
					this.processOptionGroup(subAttr.Options, componentKey, true, false);
				}
			});
		},

		processSimpleAttribute(attr) {
			if (attr.Options?.length) {
				const componentKey = this.getComponentKey(attr, false);
				this.processOptionGroup(attr.Options, componentKey, attr.isRequired, attr.isMultiSelect);
			}
		},

		processOptionGroup(options, componentKey, isRequired, isMultiSelect) {
			const hasDefaultSelected = this.setDefaultSelections(options, componentKey, isMultiSelect);

			if (!hasDefaultSelected && isRequired && options.length) {
				this.selectFirstOption(options[0], componentKey, isMultiSelect);
			}
		},

		setDefaultSelections(options, componentKey, isMultiSelect) {
			let hasDefault = false;
			options.forEach(option => {
				if (option.IsDefault === '1') {
					this.setSelectedOption(componentKey, option.OptionId, isMultiSelect);
					hasDefault = true;
				}
			});
			return hasDefault;
		},

		selectFirstOption(option, componentKey, isMultiSelect) {
			this.setSelectedOption(componentKey, option.OptionId, isMultiSelect);
		},

		setSelectedOption(componentKey, optionId, isMultiSelect) {
			if (isMultiSelect) {
				this.setMultiSelectOption(componentKey, optionId);
			} else {
				this.setSingleSelectOption(componentKey, optionId);
			}
		},

		setMultiSelectOption(componentKey, optionId) {
			if (!this.selectedOptionIdMap[componentKey]) {
				this.$set(this.selectedOptionIdMap, componentKey, []);
			}
			if (!this.selectedOptionIdMap[componentKey].includes(optionId)) {
				this.selectedOptionIdMap[componentKey].push(optionId);
			}
		},

		setSingleSelectOption(componentKey, optionId) {
			this.$set(this.selectedOptionIdMap, componentKey, optionId);
		},

		// ==================== 选择逻辑 ====================
		selectAttribute(attr) {
			this.selectedAttribute = attr;
		},

		selectOption(option, parentAttr, grandParentAttr) {
			if (!this.is3DViewerReady) {
				console.warn('3D viewer is not ready yet. Please wait.');
				return;
			}

			const selectionContext = this.createSelectionContext(option, parentAttr, grandParentAttr);
			this.handleOptionSelection(selectionContext);
			this.updateThreeD(selectionContext);
		},

		createSelectionContext(option, parentAttr, grandParentAttr) {
			const componentAttr = grandParentAttr || parentAttr;
			const componentKey = this.getComponentKey(grandParentAttr ? parentAttr : componentAttr, !!grandParentAttr);

			return {
				option,
				parentAttr,
				grandParentAttr,
				componentAttr,
				componentKey,
				isMultiSelect: componentAttr.isMultiSelect || componentAttr.OptType === '1',
				isRequired: componentAttr.isRequired || componentAttr.IsRequired === '1',
				currentSelection: this.selectedOptionIdMap[componentKey]
			};
		},

		handleOptionSelection(context) {
			const { option, componentKey, isMultiSelect, isRequired, currentSelection } = context;

			if (isMultiSelect) {
				this.handleMultiSelectOption(option, componentKey, currentSelection);
			} else {
				this.handleSingleSelectOption(option, componentKey, currentSelection, isRequired);
			}
		},

		handleMultiSelectOption(option, componentKey, currentSelection) {
			const currentArray = Array.isArray(currentSelection) ? [...currentSelection] : [];
			const optionIndex = currentArray.indexOf(option.OptionId);

			if (optionIndex > -1) {
				currentArray.splice(optionIndex, 1);
				console.log(`取消多选项: ${componentKey} -> ${option.Name_en}`);
			} else {
				currentArray.push(option.OptionId);
				console.log(`添加多选项: ${componentKey} -> ${option.Name_en}`);
			}

			this.$set(this.selectedOptionIdMap, componentKey, currentArray);
		},

		handleSingleSelectOption(option, componentKey, currentSelection, isRequired) {
			if (currentSelection === option.OptionId) {
				if (!isRequired) {
					this.$set(this.selectedOptionIdMap, componentKey, null);
					console.log(`取消选择非必选项: ${componentKey}`);
					return;
				} else {
					console.log(`必选项不能取消选择: ${componentKey}`);
					return;
				}
			}

			this.$set(this.selectedOptionIdMap, componentKey, option.OptionId);
			console.log(`选择单选项: ${componentKey} -> ${option.Name_en}`);
		},

		// ==================== 3D 渲染相关 ====================
		tryInitializeRender() {
			if (this.is3DViewerReady && this.attributes?.length && Object.keys(this.selectedOptionIdMap).length) {
				this.$nextTick(() => {
					setTimeout(() => {
						this.initializeRender();
					}, RENDER_DELAY);
				});
			}
		},

		initializeRender() {
			if (!this.canRender()) return;

			let renderCount = 0;
			this.attributes.forEach(attr => {
				renderCount += this.renderAttribute(attr);
			});

			console.log(`=== 初始化渲染完成，共渲染 ${renderCount} 个组件 ===`);
		},

		canRender() {
			return this.is3DViewerReady && this.$refs.threeDViewer;
		},

		renderAttribute(attr) {
			return attr.childStatus === 1 ? this.renderComplexAttribute(attr) : this.renderSimpleAttribute(attr);
		},

		renderComplexAttribute(attr) {
			let count = 0;
			attr.Options?.forEach(subAttr => {
				const componentKey = this.getComponentKey(subAttr, true);
				const selectedOptionId = this.selectedOptionIdMap[componentKey];

				if (selectedOptionId && subAttr.Options) {
					const selectedOption = subAttr.Options.find(opt => opt.OptionId === selectedOptionId);
					if (selectedOption) {
						this.renderToThreeD(selectedOption, subAttr.SubcomponentKey || subAttr.Components, subAttr.ComponentName);
						count++;
					}
				}
			});
			return count;
		},

		renderSimpleAttribute(attr) {
			const componentKey = this.getComponentKey(attr, false);
			const selectedValue = this.selectedOptionIdMap[componentKey];

			if (!selectedValue || !attr.Options) return 0;

			if (attr.isMultiSelect && Array.isArray(selectedValue)) {
				return this.renderMultiSelectOptions(selectedValue, attr);
			} else if (!attr.isMultiSelect && selectedValue) {
				return this.renderSingleSelectOption(selectedValue, attr);
			}

			return 0;
		},

		renderMultiSelectOptions(selectedValues, attr) {
			let count = 0;
			selectedValues.forEach(optionId => {
				const option = attr.Options.find(opt => opt.OptionId === optionId);
				if (option) {
					this.renderToThreeD(option, attr.Components, attr.ComponentName);
					count++;
				}
			});
			return count;
		},

		renderSingleSelectOption(selectedValue, attr) {
			const option = attr.Options.find(opt => opt.OptionId === selectedValue);
			if (option) {
				this.renderToThreeD(option, attr.Components, attr.ComponentName);
				return 1;
			}
			return 0;
		},

		renderToThreeD(option, componentToColor, componentName) {
			if (componentToColor && this.$refs.threeDViewer) {
				this.$refs.threeDViewer.changePartColor(componentToColor, option);
				this.sendOptionToThreeJS(option, componentName);
			}
		},

		updateThreeD(context) {
			const { option, parentAttr, grandParentAttr, componentAttr } = context;

			if (option.ModeFile) {
				this.modeFile = option.ModeFile;
			}

			const componentToColor = this.getComponentToColor(parentAttr, grandParentAttr, componentAttr);
			const isSelected = this.isOptionCurrentlySelected(option, context);

			if (componentToColor && isSelected) {
				this.renderToThreeD(option, componentToColor, componentAttr.ComponentName);
			}
		},

		getComponentToColor(parentAttr, grandParentAttr, componentAttr) {
			if (grandParentAttr) {
				return parentAttr.SubcomponentKey || parentAttr.Components;
			}
			return componentAttr.Components;
		},

		isOptionCurrentlySelected(option, context) {
			const { componentKey, isMultiSelect } = context;
			const selectedValue = this.selectedOptionIdMap[componentKey];

			if (isMultiSelect) {
				return Array.isArray(selectedValue) && selectedValue.includes(option.OptionId);
			}
			return selectedValue === option.OptionId;
		},

		// ==================== 背景切换 ====================
		async changeBg(index) {
			this.openQrCode = true;
			if (!this.is3DViewerReady) {
				console.warn('3D Viewer is not ready yet');
				return;
			}

			// 显示loading
			this.isScreenshotLoading = true;
			this.currentBgIndex = index;

			// 生成二维码URL
			this.qrCodeUrl = `${config.shareUrl}?type=1&id=${this.productData.modeResult.ProId}`;
			// 重置二维码状态
			this.isQrCodeReady = false;
			this.qrCodeTempPath = '';

			// 等待二维码生成完成后再截图
			this.$nextTick(() => {
				if (this.$refs.qrcode) {
					// 触发二维码重新生成
					this.$refs.qrcode.make();
				}
			});
		},

		// ==================== 二维码相关 ====================
		// 二维码生成完成回调
		onQrCodeComplete(res) {
			if (res.success) {
				console.log('二维码生成成功');
				this.isQrCodeReady = true;
				// 获取二维码临时文件路径
				this.$refs.qrcode.toTempFilePath({
					success: (tempRes) => {
						this.qrCodeTempPath = tempRes.tempFilePath;
						// 二维码生成完成，执行截图
						this.performScreenshot();
					},
					fail: (err) => {
						// 即使获取临时路径失败，也尝试截图
						this.performScreenshot();
					}
				});
			} else {
				console.error('二维码生成失败:', res);
				// 二维码生成失败，仍然执行截图（不带二维码）
				this.performScreenshot();
			}

		},

		// 执行截图
		async performScreenshot() {
			try {
				const screenshotOptions = {
					width: 800,
					height: 800,
					logo: { url: this.productData.logo_path, size: 0.35, opacity: 1 }, // 左上角
					title: { text: this.productData.modeResult.name, fontSize: 34, color: '#fff', opacity: 0.8 } // 左下角
				};

				// 如果二维码生成成功，添加二维码到截图选项
				if (this.qrCodeTempPath) {
					screenshotOptions.qrCode = { url: this.qrCodeTempPath, size: 0.15, opacity: 1 }; // 右上角，增大尺寸
				}

				const screenshot = await this.$refs.threeDViewer?.getCurrentScreenshot(screenshotOptions);
				console.log('截图完成:', screenshot.dataURL);

				// 隐藏loading
				this.isScreenshotLoading = false;

				// 保存截图结果并显示弹窗
				if (screenshot && screenshot.dataURL) {
					this.screenshotResult = screenshot.dataURL;
					this.showScreenshotPopup();
				} else {
					uni.showToast({
						title: 'Screenshot generation failed',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('Screenshot failed:', error);
				// 隐藏loading
				this.isScreenshotLoading = false;
				uni.showToast({
					title: 'Screenshot generation failed',
					icon: 'none'
				});
			}
		},

		// ==================== 截图弹窗相关 ====================
		// 显示截图弹窗
		showScreenshotPopup() {
			this.$refs.screenshotPopup.open();
		},

		// 关闭截图弹窗
		closeScreenshotPopup() {
			this.$refs.screenshotPopup.close();
			this.screenshotResult = ''; // 清空截图结果
		},

		// 保存截图到相册
		async saveScreenshot() {
			if (!this.screenshotResult) {
				uni.showToast({
					title: 'No screenshot to save',
					icon: 'none'
				});
				return;
			}

			try {
				// 获取base64数据（去掉data:image前缀）
				const base64Data = this.screenshotResult.replace(/^data:image\/\w+;base64,/, '');
				// 调用APP的saveImage函数，使用分段传入base64
				const result = await this.saveImageToApp(base64Data);
				console.log("🚀 ~ saveScreenshot ~ result:", result)
				if (result) {
					// 只有在非降级情况下才显示保存成功
					uni.showToast({
						title: 'Saved successfully',
						icon: 'success'
					});
					this.closeScreenshotPopup();
				} else {
					uni.showToast({
						title: 'Save failed',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('Save screenshot failed:', error);
				uni.showToast({
					title: 'Save failed',
					icon: 'none'
				});
			}
		},

		// 检查是否为APP环境
		isAppEnvironment() {
			return (
				(typeof window !== 'undefined' && window.plus) ||
				(typeof window !== 'undefined' && window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.saveImage) ||
				(typeof window !== 'undefined' && window.android && typeof window.android.saveImage === 'function') ||
				(typeof saveImage === 'function')
			);
		},

		// 分片提交base64到APP，安卓和iOS都支持，只有最后一片回调true才算成功
		async saveImageToApp(base64Data) {
			try {
				const chunkSize = 512 * 1024;
				const totalLength = base64Data.length;
				const chunks = Math.ceil(totalLength / chunkSize);

				for (let i = 0; i < chunks; i++) {
					const start = i * chunkSize;
					const end = Math.min(start + chunkSize, totalLength);
					const chunk = base64Data.substring(start, end);
					const isLastChunk = (i === chunks - 1);

					console.log(`开始发送分片 ${i + 1}/${chunks}, isLastChunk: ${isLastChunk}`);

					const success = await this.callAppSaveImage({
						chunkIndex: i,
						totalChunks: chunks,
						isLastChunk: isLastChunk,
						data: chunk
					});

					console.log(`分片 ${i + 1} 处理结果: ${success}`);

					if (isLastChunk && !success) {
						console.error(`Final chunk failed, saveImage failed`);
						return false;
					}
				}
				return true;
			} catch (error) {
				console.error('Error in saveImageToApp:', error);
				return false;
			}
		},

		// 安卓和iOS分片回调，支持callbackId
		callAppSaveImage(chunkInfo) {
			return new Promise((resolve) => {
				try {
					// Android (window.zlbridge.saveImage) - 保持兼容性
					if (typeof window !== 'undefined' && window.zlbridge && typeof window.zlbridge.saveImage === 'function') {
						// Android使用旧的参数格式保持兼容
						const type = chunkInfo.isLastChunk ? 1 : 0;
						const result = window.zlbridge.saveImage(type, chunkInfo.data);
						if (result == true || result === 'true') {
							resolve(true)
						} else {
							resolve(false)
						}
					}
					// iOS (window.webkit.messageHandlers.saveImage)
					else if (typeof window !== 'undefined' && window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.saveImage) {
						// 生成唯一的回调ID
						const callbackId = 'saveImageCallback_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

						console.log(`📱 发送iOS消息: callbackId=${callbackId}, chunkIndex=${chunkInfo.chunkIndex}, totalChunks=${chunkInfo.totalChunks}, isLastChunk=${chunkInfo.isLastChunk}, dataLength=${chunkInfo.data.length}`);

						// 创建全局回调函数
						window[callbackId] = (result) => {
							console.log(`✅ iOS回调被调用: callbackId=${callbackId}, result=${result}`);
							// 清理回调函数
							delete window[callbackId];
							resolve(result === 1 || result === true);
						};

						// 发送消息到iOS，包含分片信息
						window.webkit.messageHandlers.saveImage.postMessage({
							url: chunkInfo.data,  // base64数据
							callback: callbackId,  // 回调函数名
							chunkIndex: chunkInfo.chunkIndex,  // 当前分片索引
							totalChunks: chunkInfo.totalChunks,  // 总分片数
							isLastChunk: chunkInfo.isLastChunk  // 是否为最后一片
						});
					}
					// 其他环境降级
					else {
						resolve(false);
					}
				} catch (e) {
					console.error('callAppSaveImage error:', e);
					resolve(false);
				}
			});
		},
		// H5降级下载
		fallbackToH5Download(base64Data) {
			try {
				console.log('Falling back to H5 download');
				const dataUrl = 'data:image/png;base64,' + base64Data;
				const link = document.createElement('a');
				link.href = dataUrl;
				link.download = `screenshot_${Date.now()}.png`;
				document.body.appendChild(link);
				link.click();
				document.body.removeChild(link);

				uni.showToast({
					title: 'Image downloaded',
					icon: 'success'
				});
			} catch (error) {
				console.error('H5 download failed:', error);
				uni.showToast({
					title: 'Download failed',
					icon: 'none'
				});
			}
		},

		// 分享截图
		shareScreenshot() {
			if (!this.screenshotResult) {
				uni.showToast({
					title: '没有可分享的截图',
					icon: 'none'
				});
				return;
			}

			// 这里可以根据需要实现分享功能
			// 例如调用微信分享API或其他分享方式
			uni.showToast({
				title: '分享功能待实现',
				icon: 'none'
			});
		},

		// ==================== 价格计算 ====================
		// 构建选项价格缓存
		buildOptionPriceCache(attributes) {
			this.optionPriceCache = {};
			attributes.forEach(attr => {
				if (attr.childStatus === 1) {
					// 复合属性
					attr.Options?.forEach(subAttr => {
						subAttr.Options?.forEach(option => {
							this.optionPriceCache[option.OptionId] = parseFloat(option.Price || 0);
						});
					});
				} else {
					// 简单属性
					attr.Options?.forEach(option => {
						this.optionPriceCache[option.OptionId] = parseFloat(option.Price || 0);
					});
				}
			});
		},

		// 计算总价
		calculateTotalPrice() {
			let totalPrice = this.basePrice;

			Object.values(this.selectedOptionIdMap).forEach(selectedValue => {
				if (Array.isArray(selectedValue)) {
					// 多选
					selectedValue.forEach(optionId => {
						const optionPrice = this.optionPriceCache[optionId] || 0;
						totalPrice = PriceCalculator.add(totalPrice, optionPrice);
					});
				} else if (selectedValue) {
					// 单选
					const optionPrice = this.optionPriceCache[selectedValue] || 0;
					totalPrice = PriceCalculator.add(totalPrice, optionPrice);
				}
			});

			return totalPrice;
		},

		// 计算选中项价格
		calculateSelectedOptionsPrice() {
			let optionsPrice = 0;

			Object.values(this.selectedOptionIdMap).forEach(selectedValue => {
				if (Array.isArray(selectedValue)) {
					selectedValue.forEach(optionId => {
						const optionPrice = this.optionPriceCache[optionId] || 0;
						optionsPrice = PriceCalculator.add(optionsPrice, optionPrice);
					});
				} else if (selectedValue) {
					const optionPrice = this.optionPriceCache[selectedValue] || 0;
					optionsPrice = PriceCalculator.add(optionsPrice, optionPrice);
				}
			});

			return optionsPrice;
		},

		// 获取单个选项价格（用于显示）
		getOptionPrice(optionId) {
			return this.optionPriceCache[optionId] || 0;
		},

		// 计算价格差异（用于选择变更时的提示）
		calculatePriceDifference(oldSelection, newSelection) {
			const oldPrice = this.calculateSelectionPrice(oldSelection);
			const newPrice = this.calculateSelectionPrice(newSelection);
			return PriceCalculator.getPriceDifference(newPrice, oldPrice);
		},

		// 计算指定选择的价格
		calculateSelectionPrice(selection) {
			let price = 0;
			Object.values(selection).forEach(selectedValue => {
				if (Array.isArray(selectedValue)) {
					selectedValue.forEach(optionId => {
						const optionPrice = this.optionPriceCache[optionId] || 0;
						price = PriceCalculator.add(price, optionPrice);
					});
				} else if (selectedValue) {
					const optionPrice = this.optionPriceCache[selectedValue] || 0;
					price = PriceCalculator.add(price, optionPrice);
				}
			});
			return price;
		},

		// 格式化价格显示
		formatPrice(price, symbol = '$') {
			return PriceCalculator.formatPrice(price, symbol);
		},



		generateOptionsFor3D() {
			const options = {};

			Object.keys(this.selectedOptionIdMap).forEach(componentKey => {
				const selectedValue = this.selectedOptionIdMap[componentKey];

				if (Array.isArray(selectedValue)) {
					selectedValue.forEach(optionId => {
						const option3D = this.createOption3D(optionId, componentKey);
						if (option3D) {
							// 对于数组类型的选项，为每个组件创建单独的条目
							option3D.components.forEach(component => {
								options[component] = {
									optionColor: option3D.optionColor,
									ColorChartlet: option3D.colorChartlet
								};
							});
						}
					});
				} else if (selectedValue) {
					const option3D = this.createOption3D(selectedValue, componentKey);
					if (option3D) {
						// 为每个组件创建条目
						option3D.components.forEach(component => {
							options[component] = {
								optionColor: option3D.optionColor,
								ColorChartlet: option3D.colorChartlet
							};
						});
					}
				}
			});

			console.log('🎨 生成的3D选项:', options);
			return options;
		},

		createOption3D(optionId, componentKey) {
			const option = this.findOptionById(optionId);
			const componentInfo = this.findComponentInfoByKey(componentKey);

			if (option && componentInfo) {
				return {
					components: componentInfo.components,
					optionColor: option.optionColor,
					colorChartlet: option.ColorChartlet
				};
			}
			return null;
		},

		// ==================== 状态检查 ====================
		isAttributeSelected(attr) {
			return this.selectedAttribute?.AttrId === attr.AttrId;
		},

		isSubOptionSelected(option, subAttr) {
			const componentKey = this.getComponentKey(subAttr, true);
			return this.selectedOptionIdMap[componentKey] === option.OptionId;
		},

		isOptionSelected(option, attribute) {
			const componentKey = this.getComponentKey(attribute, false);
			const selectedValue = this.selectedOptionIdMap[componentKey];

			if (attribute.isMultiSelect || attribute.OptType === '1') {
				return Array.isArray(selectedValue) && selectedValue.includes(option.OptionId);
			}
			return selectedValue === option.OptionId;
		},

		// ==================== 辅助工具方法 ====================
		getComponentKey(attr, isSubComponent) {
			if (isSubComponent) {
				return attr.SubcomponentKey || attr.SubcomponentTitle;
			}
			return attr.Components || attr.Name_en;
		},

		findOptionById(optionId) {
			for (const attr of this.attributes) {
				if (attr.childStatus === 1) {
					for (const subAttr of attr.Options) {
						const option = subAttr.Options?.find(o => o.OptionId === optionId);
						if (option) return option;
					}
				} else {
					const option = attr.Options?.find(o => o.OptionId === optionId);
					if (option) return option;
				}
			}
			return null;
		},

		findComponentInfoByKey(componentKey) {
			for (const attr of this.attributes) {
				if (attr.childStatus === 1) {
					for (const subAttr of attr.Options) {
						const subComponentKey = this.getComponentKey(subAttr, true);
						if (subComponentKey === componentKey) {
							return {
								components: [subAttr.SubcomponentKey || subAttr.Components],
								isSubComponent: true,
								parent: attr
							};
						}
					}
				} else {
					const attrComponentKey = this.getComponentKey(attr, false);
					if (attrComponentKey === componentKey) {
						return {
							components: attr.Components ? attr.Components.split(',') : [attr.Name_en],
							isSubComponent: false,
							parent: null
						};
					}
				}
			}
			return null;
		},

		setDefaultSelectedAttribute() {
			if (this.attributes.length > 0) {
				this.selectedAttribute = this.attributes[0];
			}
		},

		logProcessingResults() {
			const requiredCount = this.countRequiredComponents();
			const processedCount = this.countProcessedComponents();
			console.log(`必选项总数: ${requiredCount}, 已处理: ${processedCount}`);
		},

		countRequiredComponents() {
			let count = 0;
			this.attributes.forEach(attr => {
				if (attr.IsRequired === '1') {
					if (attr.childStatus === 1) {
						count += attr.Options?.filter(subAttr => subAttr.IsRequired === '1').length || 0;
					} else {
						count++;
					}
				}
			});
			return count;
		},

		countProcessedComponents() {
			return Object.keys(this.selectedOptionIdMap).length;
		},

		deepClone(obj) {
			return JSON.parse(JSON.stringify(obj));
		},

		// ==================== 外部通信 ====================
		sendOptionToThreeJS(option, componentName) {
			const message = {
				type: 'changeColor',
				componentName: componentName,
				optionData: {
					PicPath: option.ColorChartlet || '',
					optionColor: option.optionColor || '',
				},
			};

			// 发送给父窗口
			window.parent.postMessage(message, '*');

			// 发送给uni-app
			if (window.uni?.postMessage) {
				window.uni.postMessage({ data: message });
			}
		},

		// ==================== 3D商品购物车逻辑 ====================
		/**
		 * 3D商品加入购物车
		 */
		async handleAddToCart() {
			// 验证数量
			if (this.quantity <= 0) {
				uni.showToast({
					title: 'Please select quantity',
					icon: 'none',
					duration: 2000
				});
				return;
			}

			// 验证必选项是否都已选择
			if (!this.validateRequiredSelections()) {
				return;
			}

			// 显示加载提示
			uni.showLoading({
				title: 'Capturing screenshot...'
			});

			try {
				// 构建3D商品的购物车参数
				const cartData = await this.build3DCartDataAsync();

				// 隐藏加载提示
				uni.hideLoading();
				// 发送事件给父组件处理
				this.$emit('add-to-cart-3d', cartData);
			} catch (error) {
				// 隐藏加载提示
				uni.hideLoading();

				console.error('加入购物车失败:', error);
				uni.showToast({
					title: 'Failed to add to cart',
					icon: 'error',
					duration: 2000
				});
			}
		},

		/**
		 * 3D商品立即购买 - 3D商品不支持立即购买，只能加入购物车
		 */
		handlePayment() {
			// 3D商品不支持立即购买，提示用户先加入购物车
			uni.showToast({
				title: 'Please add to cart first',
				icon: 'none',
				duration: 2000
			});
		},

		/**
		 * 验证必选项是否都已选择
		 */
		validateRequiredSelections() {
			const requiredComponents = [];

			// 检查所有必选项
			this.attributes.forEach(attr => {
				if (attr.IsRequired === '1') {
					if (attr.childStatus === 1) {
						// 复合属性
						attr.Options?.forEach(subAttr => {
							if (subAttr.IsRequired === '1') {
								const componentKey = this.getComponentKey(subAttr, true);
								if (!this.selectedOptionIdMap[componentKey]) {
									requiredComponents.push(subAttr.SubcomponentTitle || subAttr.Name_en);
								}
							}
						});
					} else {
						// 简单属性
						const componentKey = this.getComponentKey(attr, false);
						if (!this.selectedOptionIdMap[componentKey]) {
							requiredComponents.push(attr.Name_en);
						}
					}
				}
			});

			if (requiredComponents.length > 0) {
				uni.showToast({
					title: `Please select ${requiredComponents.join(', ')}`,
					icon: 'none',
					duration: 2000
				});
				return false;
			}

			return true;
		},
		/**
		 * 异步构建3D商品购物车数据（带截图功能）
		 */
		async build3DCartDataAsync() {
			const parentOpid = [];
			const parentOptext = [];

			// 遍历所有选择项构建参数
			Object.keys(this.selectedOptionIdMap).forEach(componentKey => {
				const selectedValue = this.selectedOptionIdMap[componentKey];

				if (Array.isArray(selectedValue)) {
					// 多选项
					selectedValue.forEach(optionId => {
						const optionInfo = this.getOptionInfo(optionId, componentKey);
						if (optionInfo) {
							parentOpid.push(`${optionInfo.parentId}_${optionId}`);
							parentOptext.push(`${optionInfo.parentName}_${optionInfo.optionName}`);
						}
					});
				} else if (selectedValue) {
					// 单选项
					const optionInfo = this.getOptionInfo(selectedValue, componentKey);
					if (optionInfo) {
						parentOpid.push(`${optionInfo.parentId}_${selectedValue}`);
						parentOptext.push(`${optionInfo.parentName}_${optionInfo.optionName}`);
					}
				}
			});

			// 异步获取截图（使用四分之三视角，展示效果更好）
			// 			自行车	three-quarter	展示整体造型和细节
			// 汽车	front-right	经典汽车展示角度
			// 家具	top	展示布局和尺寸
			// 电子产品	front	突出正面设计
			// 工具设备	side
			const screenshot = await this.captureScreenshotWithAngle('three-right');

			return {
				ProId: this.productData?.pro?.ProId || this.productData?.ProId,
				TemplateId: this.productData?.modeResult?.TemplateId || 1,
				'parentOpid[]': parentOpid,
				'parentOptext[]': parentOptext,
				ModePicParam: screenshot, // 异步获取的截图base64
				Qty: this.quantity, // 使用SkuSelector组件中的数量
				ItemPrice: this.totalPrice.toFixed(2),
				CId: this.cartCId ? this.cartCId : undefined
			};
		},

		/**
		 * 获取选项信息
		 */
		getOptionInfo(optionId, componentKey) {
			for (const attr of this.attributes) {
				if (attr.childStatus === 1) {
					// 复合属性
					for (const subAttr of attr.Options) {
						const subComponentKey = this.getComponentKey(subAttr, true);
						if (subComponentKey === componentKey) {
							const option = subAttr.Options?.find(o => o.OptionId === optionId);
							if (option) {
								return {
									parentId: attr.AttrId,
									parentName: attr.Name_en,
									optionId: optionId,
									optionName: option.Name_en
								};
							}
						}
					}
				} else {
					// 简单属性
					const attrComponentKey = this.getComponentKey(attr, false);
					if (attrComponentKey === componentKey) {
						const option = attr.Options?.find(o => o.OptionId === optionId);
						if (option) {
							return {
								parentId: attr.AttrId,
								parentName: attr.Name_en,
								optionId: optionId,
								optionName: option.Name_en
							};
						}
					}
				}
			}
			return null;
		},
		/**
		 * 异步截图功能（支持自定义角度）
		 */
		async captureScreenshotWithAngle(screenshotAngle = 'front-right') {
			try {
				if (this.$refs.threeDViewer && this.$refs.threeDViewer.captureScreenshot) {
					// 使用产品名称作为标题
					const productTitle = this.productName || 'Custom Product';
					const screenshot = await this.$refs.threeDViewer.captureScreenshot({
						title: productTitle,
						logo: this.productData.logo_path,
						titleStyle: {
							fontSize: 28,
							fontWeight: 'bold',
							color: '#fff',
							backgroundColor: 'transparent'
						}
					});
					return screenshot;
				}
			} catch (error) {
				console.error('异步截图失败:', error);
			}
			return '';
		},

		// 处理回显数据
		handleEchoData(selectSku) {
			if (!selectSku || !this.attributes) return;

			// 遍历所有需要回显的数据
			for (const attrId in selectSku) {
				if (attrId === "Overseas") continue;
				const selectedVid = selectSku[attrId];

				// 找到对应的属性
				const attribute = this.attributes.find(attr => attr.AttrId === attrId);
				if (!attribute) continue;

				// 选中这个属性
				this.selectAttribute(attribute);

				if (attribute.childStatus === 1) {
					// 处理子组件选项
					attribute.Options.forEach(subAttr => {
						subAttr.Options.forEach(option => {
							if (option.OptionId.toString() === selectedVid.toString()) {
								this.selectOption(option, subAttr, attribute);
							}
						});
					});
				} else {
					// 处理普通选项
					attribute.Options.forEach(option => {
						if (option.OptionId.toString() === selectedVid.toString()) {
							this.selectOption(option, attribute, null);
						}
					});
				}
			}
		}
	}
};
</script>

<style lang="scss" scoped>
/* ==================== 截图弹窗样式 ==================== */
.screenshot-popup {
	width: 680rpx;
	background: #fff;
	border-radius: 24rpx;
	overflow: hidden;
	margin-top: -106rpx;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
}

.screenshot-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 40rpx 40rpx 30rpx;
	background: linear-gradient(135deg, #ff5a1e 0%, #ff7a3e 100%);
}

.screenshot-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #fff;
	font-family: PingFang SC-Semibold;
}

.close-btn {
	width: 64rpx;
	height: 64rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(10rpx);
	transition: all 0.3s ease;
}

.close-btn:active {
	background: rgba(255, 255, 255, 0.3);
	transform: scale(0.95);
}

.close-icon {
	font-size: 44rpx;
	color: #fff;
	line-height: 1;
	font-weight: 300;
}

.screenshot-content {
	padding: 0;
	text-align: center;
	height: 680rpx;
	overflow: hidden;
	position: relative;
}

.screenshot-image {
	width: 100%;
	height: 100%;
	border-radius: 0;
	border: none;
	object-fit: cover;
}

.screenshot-actions {
	display: flex;
	gap: 24rpx;
	padding: 40rpx;
	background: #fafafa;
}

.action-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 44rpx;
	border: none;
	font-size: 32rpx;
	font-weight: 600;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	font-family: PingFang SC-Semibold;
}

.save-btn {
	background: linear-gradient(135deg, #ff5a1e 0%, #ff7a3e 100%);
	color: #fff;
	box-shadow: 0 8rpx 24rpx rgba(255, 90, 30, 0.3);
}

.save-btn:active {
	background: linear-gradient(135deg, #e54d1a 0%, #e56a35 100%);
	transform: translateY(2rpx);
	box-shadow: 0 4rpx 12rpx rgba(255, 90, 30, 0.4);
}

.share-btn {
	background: #f0f0f0;
	color: #333;
}

.share-btn:active {
	background: #e0e0e0;
	transform: translateY(2rpx);
}

/* ==================== 原有样式 ==================== */
.options-container.disabled {
	pointer-events: none;
	cursor: not-allowed;
	position: relative;

	&::after {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		height: 100%;
		background: rgba(255, 255, 255, 0.8);
		width: 100%;
	}
}

.configurator {
	display: flex;
	flex-direction: column;
	background-color: #ffffff;
	position: relative;
	min-height: 100vh;
	/* 确保有足够的高度触发sticky效果 */

	.three-container {
		position: sticky;
		/* 吸顶效果 */
		top: 0;
		width: 100%;
		// height: 919rpx;
		/* 固定3D容器高度 */
		overflow: hidden;
		z-index: 10;
		/* 确保在其他元素之上 */

		::v-deep .u-loading-page__warpper {
			margin-top: 0;
		}

		.three-bg-btn {
			position: absolute;
			bottom: 60rpx;
			/* 调整位置避免被options遮挡 */
			left: 50rpx;
			z-index: 200;

			img {
				width: 62rpx;
				height: 62rpx;
				margin-right: 20rpx;
				transition: all 0.3s ease;
				opacity: 0.7;

				&.active {
					opacity: 1;
					transform: scale(1.1);
				}

				&:hover {
					opacity: 1;
					transform: scale(1.05);
				}
			}
		}
	}
}

.options-container {
	flex: 1;
	overflow-y: auto;
	padding: 0 30rpx;
	position: relative;
	top: -40rpx;
	z-index: 20;
	background: #fff;
	border-radius: 31rpx 31rpx 0rpx 0rpx;
	min-height: calc(100vh - 919rpx + 40rpx);
	/* 确保至少占满剩余空间 */
}

.product-title {
	font-size: 36rpx;
	font-weight: bold;
	padding: 35rpx 0;
	line-height: 1;
	text-align: center;
	font-family: PingFang SC-Semibold;
}

.section {
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 30rpx;
	font-weight: 600;
	margin-bottom: 20rpx;
}

.scroll-view {
	width: 100%;
	white-space: nowrap;
}

.options-list-inline {
	display: inline-flex;
	flex-wrap: wrap;
	// gap: 20rpx;
	@include flex-gap(20rpx);
}

.option-chip {
	padding: 12rpx 24rpx;
	border: 2rpx solid #E2E2E2;
	border-radius: 12rpx;
	font-size: 28rpx;
	cursor: pointer;
	height: 60rpx;
	box-sizing: border-box;
	display: inline-flex;
	align-items: center;
	justify-content: center;

	&.selected {
		background-color: #ff5a1e;
		color: #ffffff;
		border-color: #ff5a1e;
	}

	&:hover {
		border-color: #ff5a1e;
	}
}

.options-list {
	display: flex;
	flex-wrap: wrap;
	@include flex-gap(35rpx); // 替换了 gap 单值
}

.option-item-color {
	width: 100rpx;
	text-align: center;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-wrap: wrap;
	flex-direction: column;
}

.color-swatch {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	border: 2px solid transparent;
	position: relative;
	overflow: hidden;


}

.texture-swatch {
	width: 100%;
	height: 100%;
}

.option-item-image {
	text-align: center;
}

.option-image {
	width: 80rpx;
	height: 80rpx;
	border-radius: 10rpx;
	border: 2px solid transparent;
	transition: all 0.3s ease;

}

.selected-border {
	border-color: #ff5a1e !important;
}

.option-price {
	font-size: 24rpx;
	color: #888;
	margin-top: 10rpx;
}

.footer {
	display: flex;
	align-items: center;
	padding: 20rpx;
	border-top: 1px solid #f0f0f0;
	background-color: #fff;
}

.price-display {
	flex-grow: 1;
	font-size: 44rpx;
	font-weight: bold;
	color: #ff5a1e;
}

.finish-button {
	background-color: #ff5a1e;
	color: #ffffff;
	border: none;
	border-radius: 40rpx;
	padding: 0 80rpx;
	height: 80rpx;
	line-height: 80rpx;
	font-size: 32rpx;
}

/* 价格动画效果优化 */
.option-price {
	transition: all 0.3s ease;
	font-weight: 500;
}

.option-item-color:hover .option-price,
.option-item-image:hover .option-price {
	// color: #ff5a1e;
	// font-weight: bold;
}
</style>