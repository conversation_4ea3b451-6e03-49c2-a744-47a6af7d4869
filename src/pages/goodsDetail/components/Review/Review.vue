<template>
	<view id="Review" class="review-section default-pad" @click="handleReviewClick">
		<view class="flex align-center justify-between" style="margin-bottom: 19rpx;">
			<u--text :text="`Review(${reviewTotal})`" color="#262626" size="33rpx" bold></u--text>
			<image src="@/static/assets/common/arrow_next.png" style="width: 31rpx; height: 31rpx;">
			</image>
		</view>

		<template v-if="reviewTotal > 0">
			<view class="flex align-center">
				<u-avatar :src="commentFirstList.Avatar" size="58rpx"></u-avatar>
				<u--text :text="commentFirstList.Name" color="#262626" size="29rpx" margin="0 0 0 12rpx"
					style="font-weight: 500;"></u--text>
			</view>
			<mote-lines-divide :line="2" :enableButton="false" class="review-content">
				<text :style="{ fontFamily: 'PingFang SC-Medium' }" v-html="commentFirstList.Content"
					style="color: #262626; font-size: 27rpx; font-weight: 500; line-height: 32rpx; margin: 15rpx 0 0 0;"></text>
			</mote-lines-divide>
		</template>

		<template v-else>
			<u-empty iconSize="45" textSize="12" text="No Data" marginTop="10"></u-empty>
		</template>
	</view>
</template>

<script>
export default {
	name: "Review",
	props: {
		reviewTotal: {
			type: Number,
			default: 0
		},
		commentFirstList: {
			type: Object,
			default: () => ({})
		},
		proId: {
			type: [String, Number],
			required: true
		}
	},
	methods: {
		handleReviewClick() {
			this.$emit('review-click', this.proId);
		}
	}
}
</script>

<style lang="scss" scoped>
.review-section {
	padding: 38rpx 31rpx;
	cursor: pointer;
}

.default-pad {
	padding: 38rpx 31rpx;
}

.review-content {
	::v-deep .dt-content {
		padding-right: 0;
	}
}
</style>