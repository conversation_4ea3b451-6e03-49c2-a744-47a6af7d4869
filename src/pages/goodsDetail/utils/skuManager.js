import { Decimal } from 'decimal.js';

/**
 * SKU管理工具类
 * 封装商品规格选择、库存检查、价格计算等相关逻辑
 */
export class SkuManager {
  constructor(options = {}) {
    this.skuData = null;           // SKU数据
    this.stockData = null;         // 库存数据
    this.selectedSku = {};         // 当前选择的SKU
    this.debug = options.debug || false;  // 是否开启调试模式
  }

  /**
   * 初始化SKU数据
   */
  init(skuData, stockData) {
    this.skuData = skuData;
    this.stockData = stockData;
    this.log('SKU管理器初始化', { skuData, stockData });
  }

  /**
   * 调试日志输出
   */
  log(...args) {
    if (this.debug) {
      console.log('[SKU管理器]', ...args);
    }
  }

  /**
   * 生成库存检查的key
   * 按值从小到大排序，用下划线连接
   */
  generateStockKey(selectedSku) {
    if (!selectedSku || Object.keys(selectedSku).length === 0) {
      return '';
    }

    const selectedValues = Object.values(selectedSku);
    const sortedValues = selectedValues
      .map(val => parseInt(val))
      .sort((a, b) => a - b);
    
    const stockKey = sortedValues.join('_');
    this.log('生成库存key:', { selectedSku, stockKey });
    
    return stockKey;
  }

  /**
   * 检查库存是否可用
   */
  hasStock(stockKey) {
    if (!this.stockData || !stockKey) return false;
    const stock = this.stockData[stockKey];
    return stock && stock !== "0";
  }

  /**
   * 检查规格组合是否有库存
   */
  checkCombinationStock(testSku) {
    if (!this.stockData || !this.skuData?.attr) return false;
    
    const attrIds = Object.keys(this.skuData.attr);
    const hasAllSelected = attrIds.every(attrId => testSku.hasOwnProperty(attrId));
    
    if (!hasAllSelected) {
      // 如果不是所有属性都已选择，需要检查是否有任何可能的组合有库存
      return this.hasAnyValidCombination(testSku);
    } else {
      // 如果所有属性都已选择，直接检查这个组合的库存
      const stockKey = this.generateStockKey(testSku);
      return this.hasStock(stockKey);
    }
  }

  /**
   * 检查是否有任何可能的组合有库存
   */
  hasAnyValidCombination(partialSku) {
    if (!this.stockData || !this.skuData?.attr) return false;
    
    const attrIds = Object.keys(this.skuData.attr);
    const unselectedAttrs = attrIds.filter(attrId => !partialSku.hasOwnProperty(attrId));
    
    const allCombinations = this.generateAllCombinations(partialSku, unselectedAttrs);
    
    // 检查是否有任何组合有库存
    for (const combination of allCombinations) {
      const stockKey = this.generateStockKey(combination);
      if (this.hasStock(stockKey)) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * 生成所有可能的完整组合
   */
  generateAllCombinations(partialSku, unselectedAttrs) {
    if (unselectedAttrs.length === 0) {
      return [partialSku];
    }
    
    const combinations = [];
    const [firstAttr, ...restAttrs] = unselectedAttrs;
    const attribute = this.skuData.attr[firstAttr];
    
    if (attribute && attribute.children) {
      attribute.children.forEach(child => {
        const newPartialSku = { ...partialSku, [firstAttr]: child.vid };
        const subCombinations = this.generateAllCombinations(newPartialSku, restAttrs);
        combinations.push(...subCombinations);
      });
    }
    
    return combinations;
  }

  /**
   * 更新规格禁用状态
   */
  updateSkuDisabledStatus(currentSelectedSku) {
    if (!this.stockData || !this.skuData?.attr) return;
    
    this.log('开始更新规格禁用状态', currentSelectedSku);
    
    const attrIds = Object.keys(this.skuData.attr).sort((a, b) => parseInt(a) - parseInt(b));
    
    // 遍历每个属性
    attrIds.forEach(attrId => {
      const attribute = this.skuData.attr[attrId];
      if (!attribute || !attribute.children) return;
      
      // 遍历该属性的每个选项
      attribute.children.forEach(child => {
        // 如果这个选项已经被选中，不禁用
        if (child.selected === 1) {
          child.disabled = false;
          return;
        }
        
        // 构建假设选择了这个选项的SKU组合
        const testSku = { ...currentSelectedSku };
        testSku[attrId] = child.vid;
        
        // 检查这个组合是否有库存
        const hasStock = this.checkCombinationStock(testSku);
        
        // 设置禁用状态
        child.disabled = !hasStock;
        
        this.log(`属性${attrId}选项${child.vid}: ${hasStock ? '可选' : '禁用'}`);
      });
    });
    
    this.log('规格禁用状态更新完成');
  }

  /**
   * 生成所有组合，按优先级排序
   */
  generateCombinations(attrOptions) {
    const combinations = [];
    const lengths = attrOptions.map(opts => opts.length);
    const totalCombinations = lengths.reduce((total, len) => total * len, 1);
    
    // 生成所有组合
    for (let i = 0; i < totalCombinations; i++) {
      const combination = [];
      let temp = i;
      
      // 从右到左计算每个位置的索引
      for (let j = lengths.length - 1; j >= 0; j--) {
        combination[j] = temp % lengths[j];
        temp = Math.floor(temp / lengths[j]);
      }
      
      combinations.push(combination);
    }
    
    // 按优先级排序：优先级是从左到右的权重
    combinations.sort((a, b) => {
      for (let i = 0; i < a.length; i++) {
        if (a[i] !== b[i]) {
          return a[i] - b[i];
        }
      }
      return 0;
    });
    
    return combinations;
  }

  /**
   * 寻找有库存的组合
   */
  findValidStockCombination() {
    if (!this.stockData || !this.skuData?.attr) return null;
    
    const attrIds = Object.keys(this.skuData.attr).sort((a, b) => parseInt(a) - parseInt(b));
    this.log('寻找有库存组合，属性列表:', attrIds);
    
    // 获取每个属性的选项数量
    const attrOptions = [];
    attrIds.forEach(attrId => {
      const children = this.skuData.attr[attrId]?.children || [];
      attrOptions.push(children);
    });
    
    // 生成所有可能的组合，按优先级排序
    const combinations = this.generateCombinations(attrOptions);
    
    // 按顺序测试每个组合
    for (let i = 0; i < combinations.length; i++) {
      const combination = combinations[i];
      const combinationObj = {};
      
      // 构建组合对象
      attrIds.forEach((attrId, index) => {
        const optionIndex = combination[index];
        const option = attrOptions[index][optionIndex];
        if (option) {
          combinationObj[attrId] = option.vid;
        }
      });
      
      // 检查这个组合是否有库存
      const testKey = this.generateStockKey(combinationObj);
      if (testKey && this.hasStock(testKey)) {
        this.log('找到有库存的组合:', combinationObj, 'key:', testKey);
        return combinationObj;
      }
    }
    
    this.log('没有找到任何有库存的组合');
    return null;
  }

  /**
   * 应用库存组合到SKU选择
   */
  applyStockCombination(combination) {
    if (!combination || !this.skuData?.attr) return false;
    
    this.log('应用库存组合:', combination);
    
    // 先清除所有选择
    Object.keys(this.skuData.attr).forEach(attrId => {
      const attribute = this.skuData.attr[attrId];
      if (attribute && attribute.children) {
        attribute.children.forEach(child => {
          child.selected = 0;
        });
      }
    });
    
    // 应用新的选择
    Object.keys(combination).forEach(attrId => {
      const vid = combination[attrId];
      const attribute = this.skuData.attr[attrId];
      if (attribute && attribute.children) {
        const targetChild = attribute.children.find(child => child.vid == vid);
        if (targetChild) {
          targetChild.selected = 1;
        }
      }
    });
    
    return true;
  }

  /**
   * 重置SKU选择为默认或有库存的组合
   */
  resetToValidSelection() {
    if (!this.skuData?.attr) return false;
    
    this.log('重置SKU选择');
    
    // 先尝试默认选择（每项第一个）
    Object.keys(this.skuData.attr).forEach(attrId => {
      const attribute = this.skuData.attr[attrId];
      if (attribute && attribute.children && attribute.children.length > 0) {
        attribute.children.forEach(child => child.selected = 0);
        attribute.children[0].selected = 1;
      }
    });
    
    // 获取当前选择
    const currentSelection = this.getCurrentSelection();
    
    // 检查默认选择是否有库存
    const stockKey = this.generateStockKey(currentSelection);
    if (this.hasStock(stockKey)) {
      this.log('默认选择有库存');
      return currentSelection;
    }
    
    // 如果默认选择没有库存，找一个有库存的组合
    this.log('默认选择无库存，寻找有库存的组合');
    const validCombination = this.findValidStockCombination();
    if (validCombination) {
      this.applyStockCombination(validCombination);
      return validCombination;
    }
    
    // 如果没有找到任何有库存的组合，清除所有选择
    this.log('没有找到有库存的组合，清除所有选择');
    Object.keys(this.skuData.attr).forEach(attrId => {
      const attribute = this.skuData.attr[attrId];
      if (attribute && attribute.children) {
        attribute.children.forEach(child => {
          child.selected = 0;
        });
      }
    });
    
    return null;
  }

  /**
   * 获取当前选择的SKU
   */
  getCurrentSelection() {
    const selection = {};
    if (!this.skuData?.attr) return selection;
    
    Object.keys(this.skuData.attr).forEach(attrId => {
      const attribute = this.skuData.attr[attrId];
      if (attribute && attribute.children) {
        const selectedChild = attribute.children.find(child => child.selected === 1);
        if (selectedChild) {
          selection[attrId] = selectedChild.vid;
        }
      }
    });
    
    return selection;
  }
}

/**
 * 价格计算工具类
 * 使用Decimal.js进行精确的货币计算
 */
export class PriceCalculator {
  /**
   * 精确加法
   */
  static add(a, b) {
    return new Decimal(a).add(new Decimal(b)).toNumber();
  }

  /**
   * 精确减法
   */
  static subtract(a, b) {
    return new Decimal(a).sub(new Decimal(b)).toNumber();
  }

  /**
   * 精确乘法
   */
  static multiply(a, b) {
    return new Decimal(a).mul(new Decimal(b)).toNumber();
  }

  /**
   * 精确除法
   */
  static divide(a, b) {
    return new Decimal(a).div(new Decimal(b)).toNumber();
  }

  /**
   * 格式化价格显示
   */
  static formatPrice(price, symbol = '$', decimals = 2) {
    const decimal = new Decimal(price);
    return symbol + decimal.toFixed(decimals);
  }

  /**
   * 计算价格差
   */
  static getPriceDifference(newPrice, oldPrice) {
    return new Decimal(newPrice).sub(new Decimal(oldPrice)).toNumber();
  }

  /**
   * 四舍五入到指定小数位数
   * @param {number} value - 要四舍五入的值
   * @param {number} decimals - 小数位数，默认2位
   * @returns {number} - 四舍五入后的值
   */
  static round(value, decimals = 2) {
    return new Decimal(value).toDecimalPlaces(decimals, Decimal.ROUND_HALF_UP).toNumber();
  }

  /**
   * 四舍五入到指定小数位数并返回字符串格式
   * @param {number} value - 要四舍五入的值
   * @param {number} decimals - 小数位数，默认2位
   * @returns {string} - 四舍五入后的字符串格式
   */
  static roundToString(value, decimals = 2) {
    return new Decimal(value).toDecimalPlaces(decimals, Decimal.ROUND_HALF_UP).toFixed(decimals);
  }

  /**
   * 向上取整到指定小数位数
   * @param {number} value - 要向上取整的值
   * @param {number} decimals - 小数位数，默认2位
   * @returns {number} - 向上取整后的值
   */
  static ceil(value, decimals = 2) {
    return new Decimal(value).toDecimalPlaces(decimals, Decimal.ROUND_UP).toNumber();
  }

  /**
   * 向上取整到指定小数位数并返回字符串格式
   * @param {number} value - 要向上取整的值
   * @param {number} decimals - 小数位数，默认2位
   * @returns {string} - 向上取整后的字符串格式
   */
  static ceilToString(value, decimals = 2) {
    return new Decimal(value).toDecimalPlaces(decimals, Decimal.ROUND_UP).toFixed(decimals);
  }
}

// 默认导出SKU管理器实例
export default SkuManager; 