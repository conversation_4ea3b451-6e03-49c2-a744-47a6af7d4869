// GLB环境球体配置和使用说明
// 用于Three.js鱼眼环境效果

const EnvironmentSphereConfig = {
  // GLB文件应包含的结构
  requiredStructure: {
    scene: {
      name: "EnvironmentScene",
      children: [
        {
          name: "environment_sphere", // 环境球体必须包含这个名称
          type: "Mesh",
          geometry: "SphereGeometry",
          material: "MeshStandardMaterial",
          // 材质会被KTX2纹理替换
        }
      ]
    }
  },

  // 推荐的GLB环境设置
  recommendations: {
    sphereRadius: 100, // 环境球半径
    segments: 64, // 球体分段数（影响质量）
    materialSettings: {
      side: "BackSide", // 内表面渲染
      transparent: false,
      emissive: [0, 0, 0], // 不发光
      roughness: 1.0, // 完全粗糙
      metalness: 0.0, // 不是金属
    }
  },

  // KTX2纹理要求
  textureRequirements: {
    format: "KTX2",
    compression: "BasisU", // 推荐使用BasisU压缩
    resolution: {
      recommended: [2048, 1024], // 2:1宽高比（全景）
      minimum: [1024, 512],
      maximum: [4096, 2048]
    },
    mapping: "EquirectangularReflectionMapping"
  },

  // 使用示例
  usageExamples: [
    {
      name: "加载GLB+KTX2环境",
      code: `
// 方法1: 使用预设背景
changeBackground(4); // GLB环境ID

// 方法2: 手动加载
loadGLBEnvironment({
  name: "Custom Environment",
  url: "./path/to/environment.glb",
  textureUrl: "./path/to/texture.ktx2",
  textureType: "ktx2",
  mapping: "fisheye"
});
      `
    },
    {
      name: "纯代码创建鱼眼环境",
      code: `
// 加载KTX2纹理并应用鱼眼效果
loadKTX2Texture('./texture.ktx2', function(texture) {
  applyAdvancedFisheyeEffect(texture, {
    fisheyeStrength: 1.5,
    brightness: 1.2,
    radius: 150
  });
});
      `
    },
    {
      name: "程序化环境创建",
      code: `
// 使用程序化环境
changeBackground(5);

// 或自定义程序化环境
createProceduralEnvironment({
  config: {
    skyColor: "#87CEEB",
    horizonColor: "#FFE4B5", 
    groundColor: "#8B7355",
    intensity: 1.0
  },
  mapping: "fisheye"
});
      `
    }
  ]
};

// 环境质量优化建议
const PerformanceOptimizations = {
  // 低端设备配置
  lowEnd: {
    sphereSegments: 32,
    textureResolution: [1024, 512],
    fisheyeStrength: 1.0,
    enableMipmaps: false
  },

  // 中端设备配置
  midRange: {
    sphereSegments: 48,
    textureResolution: [2048, 1024],
    fisheyeStrength: 1.2,
    enableMipmaps: true
  },

  // 高端设备配置
  highEnd: {
    sphereSegments: 64,
    textureResolution: [4096, 2048],
    fisheyeStrength: 1.5,
    enableMipmaps: true,
    enableAnisotropy: true
  }
};

// 鱼眼效果参数说明
const FisheyeParameters = {
  fisheyeStrength: {
    description: "鱼眼扭曲强度",
    range: [0.5, 3.0],
    default: 1.2,
    effects: {
      "< 1.0": "减少扭曲，更接近普通球面投影",
      "1.0 - 1.5": "自然的鱼眼效果",
      "> 1.5": "强烈的鱼眼扭曲效果"
    }
  },

  brightness: {
    description: "环境亮度调整",
    range: [0.1, 3.0],
    default: 1.0,
    note: "影响整体场景光照"
  },

  radius: {
    description: "环境球半径",
    range: [50, 500],
    default: 100,
    note: "影响环境的视觉距离感"
  }
};

// 暴露配置
if (typeof window !== 'undefined') {
  window.EnvironmentSphereConfig = EnvironmentSphereConfig;
  window.PerformanceOptimizations = PerformanceOptimizations;
  window.FisheyeParameters = FisheyeParameters;
}

// Node.js 环境
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    EnvironmentSphereConfig,
    PerformanceOptimizations,
    FisheyeParameters
  };
} 