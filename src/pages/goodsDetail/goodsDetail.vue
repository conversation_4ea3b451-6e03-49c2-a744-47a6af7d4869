<template>
	<view class="goods-detail-box" @dblclick.stop.prevent="preventDoubleClick">

		<navbar autoBack :bgColor="navbarBgColor" :style="{
			position: is3DViewActive ? 'fixed' : 'relative',
			top: 0,
			left: 0,
			width: '100%',
			zIndex: 99,
			transition: 'background 0.2s',
			background: navbarBgColor
		}" />
		<u-loading-page :loading="isPageLoading" bg-color="#F0F0F0" iconSize="30"
			loadingText="Loading"></u-loading-page>

		<ShareCanvas :detail="goodsDetailData" :goodsPrice="goodsPrice + ''" :priceSymbol="priceSymbol"
			ref="shareRef" />

		<template v-if="!isPageLoading">
			<!-- tabs 菜单 -->
			<ProductNavigation :tabList="tabList" :currentIndex="currentIndex" :isNavFixed="isNavFixed"
				:navOpacity="navOpacity" :navTranslateY="navTranslateY" :statusBarAndNavHeight="statusBarAndNavHeight"
				@scroll-to-section="scrollToSection" v-if="!is3DViewActive && !!prodDetailInfo" />

			<!-- If 3D model exists, we can toggle -->
			<template v-if="is3DViewActive && isValidProductData">
				<ThreeDViewer :SoldOut="SoldOut" :productData="goodsDetailData" :is-visible="is3DViewActive"
					:quantity="numberValue" @add-to-cart-3d="handle3DAddToCart" :cartCId="cartCId"
					:cartAttributes="cartSelectSkuData && cartSelectSkuData.Attr" />
			</template>
			<view id="Product" :class="{ 'product-media-container': !is3DViewActive }" style="position: relative"
				v-else>
				<template>
					<goods-swiper :swiperList="goodsDetailData.pic_arr" height="512rpx" imgMode="aspectFit" indicator
						indicatorMode="dot" indicatorActiveColor="#FF5A1E" indicatorInactiveColor="#8C8C8C"
						circular></goods-swiper>
				</template>
			</view>
			<!-- 促销横幅 -->
			<PromotionBanner :productsType="productsType" :is3DViewActive="is3DViewActive" :isFavorite="isFavorite"
				:discountEndtime="discountEndtime" :killContent="killContent" @add-favorite="onAddFavorite"
				@share="onShare" />
			<!-- 收藏、分享 -->
			<view class="goods-content" v-if="!is3DViewActive"
				:style="{ top: is3DViewActive ? '-40rpx' : 0, position: 'relative' }">
				<template v-if="productsType == 0">
					<view class="favorite-box" @click="onAddFavorite">
						<img v-if="isFavorite != 1" src="@/static/assets/common/<EMAIL>" alt="">
						<img v-if="isFavorite == 1" src="@/static/assets/common/<EMAIL>" alt="">
					</view>
					<view class="share-box" @click="onShare">
						<img src="@/static/assets/common/<EMAIL>" alt="">
					</view>
				</template>
				<!-- 普通商品 -->
				<div v-if="!is3DViewActive">
					<ProductInfo :productData="goodsDetailData" :currentPrice="goodsPrice"
						:marketPrice="goodsMarketPrice" :rating="Rating" :productsType="productsType"
						:TotalRating="TotalRating" />
				</div>
				<u-gap height="1" bgColor="#F7F7F7"></u-gap>
				<!-- 规格选择 -->
				<SkuSelector :skuData="skuData" :goodsStock="goodsStock" :quantity="numberValue"
					:isCombination="goodsDetailData.pro.IsCombination" :baseStock="goodsDetailData.pro.Stock"
					@sku-select="handleSkuSelect" @quantity-change="handleQuantityChange" />

				<!-- Free Gift -->
				<FreeGift :freeGiftList="freeGiftData" />

				<u-gap height="1" bgColor="#F7F7F7"></u-gap>

				<!-- 组合购买 -->
				<!-- 				<cell-box class="default-pad">
						<u--text slot="left" text="Package-buy" color="#262626" size="33rpx" bold></u--text>
						<u--text slot="left" :text="`Save ${priceSymbol}300`" color="#8C8C8C" size="31rpx" bold
							align="right"></u--text>
			
						<view slot="content" class="package-buy-box">
							23233
						</view>
					</cell-box> -->

				<!-- 组合购买 -->
				<PackageBuy v-if="groupPurchaseData.length > 0" :showPackageBuy="true" :items="groupPurchaseData"
					:priceSymbol="priceSymbol" :packageType="'package-buy'" @updateItems="handleGroupPurchaseUpdate" />

				<!-- 组合促销 -->
				<PackageBuy v-if="combinationPromotionData.length > 0" :showPackageBuy="true"
					:items="combinationPromotionData" :priceSymbol="priceSymbol" :packageType="'combination-promotion'"
					@updateItems="handleCombinationPromotionUpdate" />
				<u-gap height="1" bgColor="#F7F7F7" v-if="false"></u-gap>

				<!-- 组合促销 -->
				<!-- 			<view v-if="goodsDetailData.products_type == 4" class="default-pad">
						<view class="flex align-center justify-between" style="margin-bottom: 21rpx;">
							<u--text text="Package Promotion" color="#262626" size="33rpx" bold></u--text>
						</view>
					</view> -->

				<!-- Information  虚拟商品赠送信息-->
				<Information v-if="goodsDetailData.GoodsType === '1'" ref="refInformation" />

				<!-- Review -->
				<Review :reviewTotal="reviewTotal" :commentFirstList="commentFirstList" :proId="ProId"
					@review-click="handleReviewClick" />

				<u-gap height="1" bgColor="#F7F7F7"></u-gap>

				<!-- Q&A -->
				<QA :qaTotal="QATotal" :qaFirstList="QAFirstList" :proId="ProId" @qa-click="handleQAClick" />

				<u-gap height="1" bgColor="#F7F7F7"></u-gap>

				<!-- maybe like -->
				<MaybeLike :maybeLikeList="maybeLikeList" @item-click="handleMaybeLikeClick" />
				<u-gap height="1" bgColor="#F7F7F7" v-if="maybeLikeList.length > 0"></u-gap>

				<!-- Detail -->
				<Detail :prodDetailInfo="prodDetailInfo" />
				<!-- <iframe ref="iframe" @load="initIframe"
						style="overflow: auto; -webkit-overflow-scrolling: touch; width: 100%; height: 500px;"
						:src="`/static/hybrid/html/detail.html`" class="web-contaniner"></iframe> -->
			</view>

			<footer-options-box v-if="showNormalFooter" @handleLeftFn="$store.commit('user/TO_SERVICE')">
				<template slot="optionsRight">
					<AddOrBuyNow subText="Buy Now" :isAddLoading="isAddLoading" :isPaymentLoading="isPaymentLoading"
						@handleAddToCart="handleAddToCart" @handlePayment="handlePayment"></AddOrBuyNow>
				</template>
			</footer-options-box>
			<footer-options-box v-else>
				<template slot="optionsRight">
					<div class="add-to-cart-package-box" @click="handleGroupBuyAddToCart">
						<u-icon class="plus-icon" name="plus" size="20" color="#FF5A1E"></u-icon>
						Add
					</div>
				</template>
			</footer-options-box>

			<!-- 下架 -->
			<footer-options-box v-if="SoldOut == 1"
				@handleLeftFn="$tab.navigateTo('/pages/tabs/mine/services/services')">
				<view slot="optionsRight">
					<view class="SoldOut-box">
						Sold Out
					</view>
				</view>
			</footer-options-box>

			<u-back-top class="backTop" :scrollTop="backScrollTop" top="300" bottom="150"
				@click.native="handleBackToTop"></u-back-top>
		</template>
	</view>
</template>

<script>
import {
	productDetail,
	productDetailCart
} from "@/api/home.js";

import {
	getCommentList,
	getConsultList,
	getProductsContent,
	getProductsGroup,
	getCartGiftList
} from "@/api/orderDetails.js";
import {
	getCartSelectPopCart,
	getCartAddToCart,
	add3DToCart,
	packageAddToCart
} from "@/api/products.js";
import {
	productFavorite
} from "@/api/home.js";
import {
	mapGetters
} from "vuex";
import { SkuManager, PriceCalculator } from './utils/skuManager.js';
export default {
	components: {
		Information: () => import('./components/Information/Information.vue'),
		ThreeDViewer: () => import('./components/ThreeDViewer/index.vue'),
		SkuSelector: () => import('./components/SkuSelector/SkuSelector.vue'),
		ProductInfo: () => import('./components/ProductInfo/ProductInfo.vue'),
		FreeGift: () => import('./components/FreeGift/FreeGift.vue'),
		ProductNavigation: () => import('./components/ProductNavigation/ProductNavigation.vue'),
		AddOrBuyNow: () => import('./components/AddOrBuyNow/AddOrBuyNow.vue'),
		Review: () => import('./components/Review/Review.vue'),
		QA: () => import('./components/QA/QA.vue'),
		MaybeLike: () => import('./components/MaybeLike/MaybeLike.vue'),
		Detail: () => import('./components/Detail/Detail.vue'),
		PromotionBanner: () => import('./components/PromotionBanner/PromotionBanner.vue'),
		PackageBuy: () => import('./components/PackageBuy/PackageBuy.vue'),
		ShareCanvas: () => import('./components/share/index.vue'),
	},
	data() {
		return {
			shareRef: null,
			showCanvas: false,
			// SKU管理器实例
			skuManager: new SkuManager({ debug: true }),
			// KTX2Loader加载状态
			ktx2LoaderReady: false,
			cCanvas: null,
			// iframe: null,
			refInformation: null,
			isPageLoading: true,
			Rating: 0,
			TotalRating: 0,
			isFavorite: 0,
			SoldOut: "",
			paymentPrice: "0.00",
			ProId: "",
			cartCId: '',
			numberValue: 1,
			cartSelectSkuData: {},
			goodsDetailData: {},
			skuData: {},
			selectedSku: {},
			reviewTotal: 0,
			commentFirstList: {},
			currentIndex: 0,
			scrollTop: 0,
			isNavFixed: false,
			navOpacity: 0, // 初始透明度为0
			navTranslateY: 0, // 初始向下偏移20px
			sectionTops: [],
			lastScrollTop: 0,
			ticking: false,
			showNavThreshold: 200, // 显示导航的滚动阈值
			tabList: [{
				name: 'Product',
				anchor: 'Product'
			},
			{
				name: 'Review',
				anchor: 'Review'
			},
			{
				name: 'Q&A',
				anchor: 'QA'
			},
			{
				name: 'Detail',
				anchor: 'Detail'
			}
			],
			isNavbarVisible: false,
			QATotal: 0,
			QAFirstList: {},
			maybeLikeList: [],
			prodDetailInfo: "",
			showPackageBuy: false,
			groupPurchaseData: [], //  组合购买
			combinationPromotionData: [], // 组合促销
			// 组合购买相关计算字段
			groupPurchaseTotal: 0,
			groupPurchaseOriginalTotal: 0,
			groupPurchaseSavings: 0,
			// 组合促销相关计算字段
			combinationPromotionTotal: 0,
			combinationPromotionOriginalTotal: 0,
			combinationPromotionSavings: 0,
			checkboxObj: {
				activeBackgroundColor: '#FF5A1E',
				activeBorderColor: '#FF5A1E',
				iconColor: '#fff'
			},
			freeGiftData: [], // Free Gift
			goodsStock: 0, // 商品最大数量
			goodsPrice: 0,
			goodsMarketPrice: 0,
			isAddLoading: false,
			isPaymentLoading: false,
			discountEndtime: 0, // 秒杀倒计时间
			killContent: '',
			backScrollTop: 0,
			showCanvas: false
		};
	},
	async onLoad(e) {
		this.ProId = e?.ProId;
		this.cartCId = e?.CId;
		if (this.cartCId) {
			await this.getCartSelectSkuData()
		}

		this.productDetail();
		this.getCommentList();
		this.getConsultList();

	},
	onShow() {
		this.showCanvas = false;
		// const statusBarHeight = uni.getSystemInfoSync().statusBarHeight;
		// this.$store.commit('SET_STATUSBARHEIGHT', statusBarHeight);
		this.getProductsContent();
		this.getCartGiftList();
		setTimeout(() => {
			this.showCanvas = true;
		}, 1000)
	},
	computed: {
		...mapGetters(['priceSymbol', 'statusBarAndNavHeight', 'cartNum', 'statusBarAndNavHeight']),
		navbarBgOpacity() {
			// 0 ~ 100px 区间渐变，超出后为 1
			if (this.is3DViewActive) return 0;
			const max = 450;
			return Math.min(this.scrollTop / max, 1);
		},
		navbarBgColor() {
			return !this.is3DViewActive ? '#fff' : `rgba(255,255,255,${this.navbarBgOpacity})`;
		},
		is3DViewActive() {
			return JSON.stringify(this.goodsDetailData) != '{}' && !Array.isArray(this.goodsDetailData.modeResult);
		},
		// 验证productData是否为有效对象
		isValidProductData() {
			return this.goodsDetailData &&
				typeof this.goodsDetailData === 'object' &&
				!Array.isArray(this.goodsDetailData) &&
				typeof this.goodsDetailData !== 'string';
		},
		heartColor() {
			return isFavorite => {
				return isFavorite == 1 ? '#FF4949' : '';
			}
		},

		productsType() {
			return this.goodsDetailData?.products_type;
		},
		isStatusBarAndNavHeight() {
			return `${this.statusBarAndNavHeight}px`;
		},

		isScrollHeight() {
			return {
				height: `calc(100vh - 87px - ${this.isStatusBarAndNavHeight} )`
			}
		},

		// 新增：底部按钮显示逻辑
		showPackageFooter() {
			return this.groupPurchaseData.some(item => item.checked || (item.pro && item.pro.some(sub => sub.checked)))
				|| this.combinationPromotionData.some(item => item.checked || (item.pro && item.pro.some(sub => sub.checked)));
		},
		showNormalFooter() {
			return !this.showPackageFooter;
		}
	},
	onPageScroll(e) {
		this.handleScroll(e)
		this.scrollTop = e.scrollTop;
	},
	methods: {
		toggle3DView() {
			this.is3DViewActive = !this.is3DViewActive;
		},
		handlePackageAddToCart() {
			console.log('handlePackageAddToCart');
		},
		getCartSelectSkuData() {
			return new Promise((resolve, reject) => {
				productDetailCart({
					CId: this.cartCId,
					ProId: this.ProId
				}).then(res => {
					this.cartSelectSkuData = res;
					resolve(res)
				}).catch(err => {
					reject(err);
				})
			})
		},
		productDetail() {
			productDetail({
				ProId: this.ProId
			}).then(res => {
				this.isPageLoading = false;
				this.goodsDetailData = res;
				this.skuData = res?.attr;
				this.isFavorite = res?.isFavorite;
				this.SoldOut = res?.pro?.SoldOut; // 1代表下架 其他状态均可购买
				this.maybeLikeList = res?.accessories_row;
				this.goodsStock = +res?.pro?.Stock;
				this.goodsPrice = res?.Price;
				this.goodsMarketPrice = res?.MarketPrice;


				let discount_endtime = 0;
				// if(res?.attr && res?.attr.length > 0) {
				//     for(let key of this.skuData.attr){
				// 		key.children[0].selected=1
				// 	}
				// }

				if (res?.products_type == 2) {
					this.killContent = res?.seckill?.discount_price_text;
					discount_endtime = res?.seckill?.discount_endtime;
				}

				if (res?.products_type == 6) {
					this.killContent = res?.lto?.discount_price_text;
					discount_endtime = res?.lto?.discount_endtime;
				}

				if (discount_endtime) {
					this.discountEndtime = discount_endtime * 1000 - +new Date();
				} else {
					this.discountEndtime = 0;
				}

				// 修复条件判断：skuData可能是对象而不是数组
				if (this.skuData && Object.keys(this.skuData?.attr || {}).length && this.goodsDetailData.pro.IsCombination !== '0') {
					console.log('SKU数据存在，开始处理规格选择');
					if (this.cartCId) {
						this.numberValue = this.cartSelectSkuData?.Qty;
						this.echoCartSkuData(this.skuData?.attr, this.cartSelectSkuData?.Attr);
					} else {
						console.log('调用resetSelectSku设置默认选择');
						this.resetSelectSku()
					}
				} else {
					console.log('SKU数据不存在或为空');
				}

				// 组合购买
				this.getProductsGroup(0)
				// 组合促销
				this.getProductsGroup(1)

				setTimeout(() => {
					this.calculateSectionPositions();
				}, 1000)
			})
		},
		//从购物车进入 → 调用 echoCartSkuData() 回显已选择的规格
		echoCartSkuData(originAttr, selectSku) {
			for (const attrId in selectSku) {
				if (attrId === "Overseas") continue;
				const selectedVid = selectSku[attrId];
				const attribute = originAttr[attrId];

				if (attribute) {
					for (const child of attribute.children) {
						if (child.vid.toString() === selectedVid) {
							child.selected = 1;
						} else {
							child.selected = 0;
						}
					}
				}
			}
		},
		resetSelectSku() {
			console.log('调用resetSelectSku设置默认选择');

			// 初始化SKU管理器
			this.skuManager.init(this.skuData, this.goodsDetailData?.ext_ary);

			// 重置为有效选择
			const validSelection = this.skuManager.resetToValidSelection();

			if (!validSelection) {
				console.log('没有找到有库存的组合，所有规格组合都已售罄');
				// 清空选择状态
				this.selectedSku = {};
				// 将数量设置为0（因为没有库存）
				this.numberValue = 0;
				// 更新规格禁用状态（所有选项都应该被禁用）
				this.skuManager.updateSkuDisabledStatus({});
				// 可以选择性地显示提示信息
				// this.$toast({
				// 	title: 'All combinations are out of stock',
				// 	icon: 'none'
				// });
				return;
			}

			// 获取当前选择
			this.getSelectdSkuData();

			// 准备查询数据
			let data = {
				ProId: this.ProId,
			}

			for (const key in this.selectedSku) {
				data[`Attr_ary[${key}]`] = this.selectedSku[key];
			}

			const originSkuAttr = Object.keys(this.skuData.attr);
			const selectedSkuAttr = Object.keys(this.selectedSku);

			// 如果所有规格都已选择，则查询对应的价格和库存信息
			if (originSkuAttr.length === selectedSkuAttr.length) {
				this.getCartSelectPopCart(data);
			}

			// 更新规格禁用状态
			this.skuManager.updateSkuDisabledStatus(this.selectedSku);
		},


		// 解析库存key为选择对象
		parseStockKey(stockKey) {
			const parts = stockKey.split('_');
			const combination = {};

			console.log('解析库存key:', stockKey);
			console.log('parts:', parts);

			// 获取属性列表（按ID排序）
			const attrIds = Object.keys(this.skuData?.attr || {}).sort((a, b) => parseInt(a) - parseInt(b));
			console.log('属性ID列表:', attrIds);

			// 现在我们知道stockKey格式是：值1_值2_值3（按值从小到大排序）
			// 我们需要找到每个值对应哪个属性的哪个选项

			// 为每个部分找到对应的属性和选项
			const parsedValues = parts.map(part => parseInt(part));
			console.log('解析的值:', parsedValues);

			// 遍历每个属性，找到匹配的选项
			attrIds.forEach(attrId => {
				const attribute = this.skuData.attr[attrId];
				if (attribute && attribute.children) {
					for (const child of attribute.children) {
						const childVid = parseInt(child.vid);
						if (parsedValues.includes(childVid)) {
							combination[attrId] = child.vid;
							console.log(`属性 ${attrId} 匹配到选项:`, child.vid, child.name);
							// 从数组中移除已匹配的值，避免重复匹配
							const index = parsedValues.indexOf(childVid);
							if (index > -1) {
								parsedValues.splice(index, 1);
							}
							break;
						}
					}
				}
			});

			console.log('解析结果:', combination);
			return combination;
		},

		// 应用有库存的组合到界面选择
		applyStockCombination(combination) {
			console.log('应用库存组合:', combination);

			// 先清空所有选择
			for (const attrId in this.skuData?.attr) {
				const attribute = this.skuData?.attr[attrId];
				if (attribute && attribute.children) {
					attribute.children.forEach(child => {
						child.selected = 0;
					});
				}
			}

			// 应用新的选择
			for (const attrId in combination) {
				const optionId = combination[attrId];
				const attribute = this.skuData?.attr[attrId];

				console.log(`处理属性 ${attrId}, 目标选项ID: ${optionId} (${typeof optionId})`);

				if (attribute && attribute.children) {
					console.log(`属性 ${attrId} 的所有选项:`, attribute.children.map(child => ({
						vid: child.vid,
						vidType: typeof child.vid,
						name: child.name,
						selected: child.selected
					})));

					// 尝试多种匹配方式
					let targetChild = null;

					// 方式1: 直接匹配
					targetChild = attribute.children.find(child => child.vid === optionId);
					if (targetChild) {
						console.log('直接匹配成功:', targetChild);
					} else {
						// 方式2: 转字符串匹配
						targetChild = attribute.children.find(child => child.vid.toString() === optionId.toString());
						if (targetChild) {
							console.log('字符串匹配成功:', targetChild);
						} else {
							// 方式3: 转数字匹配
							targetChild = attribute.children.find(child => parseInt(child.vid) === parseInt(optionId));
							if (targetChild) {
								console.log('数字匹配成功:', targetChild);
							} else {
								console.log(`属性 ${attrId} 没有找到匹配的选项 ${optionId}`);
							}
						}
					}

					if (targetChild) {
						targetChild.selected = 1;
						console.log('✅ 设置选中成功:', attrId, optionId, targetChild.name || targetChild.vid);
					} else {
						console.log('❌ 设置选中失败:', attrId, optionId);
					}
				} else {
					console.log(`❌ 属性 ${attrId} 不存在或没有children`);
				}
			}

			// 验证设置结果
			console.log('=== 设置完成后的验证 ===');
			for (const attrId in this.skuData?.attr) {
				const attribute = this.skuData?.attr[attrId];
				if (attribute && attribute.children) {
					const selected = attribute.children.filter(child => child.selected === 1);
					console.log(`属性 ${attrId} 选中项:`, selected.map(s => s.vid));
				}
			}
		},



		// 评论列表  review
		getCommentList() {
			getCommentList({
				ProId: this.ProId
			}).then(res => {
				console.log("review", res);
				this.Rating = res?.Rating;
				this.TotalRating = res?.TotalRating;
				this.reviewTotal = res?.reviews?.length;
				this.commentFirstList = res?.reviews && res?.reviews[0];
			})
		},
		// 咨询列表 QA
		getConsultList() {
			getConsultList({
				ProId: this.ProId
			}).then(res => {
				console.log("QA", res);
				this.QATotal = Object.values(res?.result).length;
				if (this.QATotal > 0) {
					this.QAFirstList = Object.values(res?.result)[0];
				}
			})
		},
		// 商品详情
		getProductsContent() {
			const oldEle = document.querySelector('.html-products-content');
			if (oldEle) {
				oldEle.remove();
			}


			getProductsContent({
				ProId: this.ProId
			}).then(res => {
				this.prodDetailInfo = res?.description;

				// 使用 nextTick 确保 DOM 已渲染
				this.$nextTick(() => {
					const body = document.querySelector('[data-page="pages/goodsDetail/goodsDetail"]');

					// 容错处理：检查 body 是否存在
					if (!body) {
						console.warn('未找到 data-page="pages/goodsDetail/goodsDetail" 的元素，尝试其他选择器');
						// 尝试其他可能的选择器
						const fallbackBody = document.querySelector('uni-page-body') ||
							document.querySelector('.uni-page-body') ||
							document.querySelector('body');
						if (!fallbackBody) {
							console.error('无法找到页面容器元素');
							return;
						}
						this.appendProductContent(fallbackBody, res?.description);
						return;
					}

					// 查找其下的 uni-page-wrapper
					const pageWrapper = body.querySelector('uni-page-wrapper');

					console.log('找到的页面容器:', pageWrapper || body); // 输出找到的元素

					// 使用找到的容器（优先使用 pageWrapper，否则使用 body）
					this.appendProductContent(pageWrapper || body, res?.description);
				});
			})
		},
		// 提取为独立方法，便于复用
		appendProductContent(container, description) {
			if (!container || !description) {
				console.warn('容器或描述内容为空，跳过添加');
				return;
			}

			const div = document.createElement('div');
			div.innerHTML = description;
			div.className = 'html-products-content';
			div.style.position = 'relative';
			div.style.top = uni.upx2px(-160) + 'px';
			div.style.paddingBottom = uni.upx2px(160) + 'px';

			// 将创建的元素添加到容器中
			container.appendChild(div);
		},
		// 赠品列表
		getCartGiftList() {
			getCartGiftList({
				ProId: this.ProId
			}).then(res => {
				this.freeGiftData = res;
			})
		},
		// 组合商品
		getProductsGroup(type) {
			console.log("🚀 ~ file: goodsDetail.vue:657 ~ type:", type)
			getProductsGroup({
				ProId: this.ProId,
				type
			}).then(res => {
				console.log("🚀 ~ file: goodsDetail.vue:661 ~ res:", res)

				if (type == 0) {
					// 组合购买
					this.groupPurchaseData = this.initializePackageData(res?.data || []);
					console.log('组合购买数据初始化完成:', this.groupPurchaseData);
				}

				if (type == 1) {
					// 组合促销
					this.combinationPromotionData = this.initializePackageData(res?.data || []);
					console.log('组合促销数据初始化完成:', this.combinationPromotionData);
				}
			}).catch(err => {
				console.error('获取组合商品失败:', err);
				if (type == 0) {
					this.groupPurchaseData = [];
				} else if (type == 1) {
					this.combinationPromotionData = [];
				}
			})
		},

		// 初始化套餐数据
		initializePackageData(data) {
			if (!Array.isArray(data)) return [];

			return data.map(item => {
				// 确保每个商品都有必要的属性
				const initializedItem = {
					...item,
					checked: false, // 默认未选中
					CId: item.CId || item.ProId || `temp_${Date.now()}_${Math.random()}`, // 确保有唯一ID
				};

				// 初始化子商品
				if (item.pro && Array.isArray(item.pro)) {
					initializedItem.pro = item.pro.map(subItem => ({
						...subItem,
						checked: false, // 默认未选中
						CId: subItem.CId || subItem.ProId || `temp_sub_${Date.now()}_${Math.random()}`, // 确保有唯一ID
					}));
				}

				// 初始化规格选择状态
				this.initializeItemSkuSelection(initializedItem);

				return initializedItem;
			});
		},

		// 初始化单个商品的规格选择状态
		initializeItemSkuSelection(item) {
			if (item.attr && Object.keys(item.attr).length > 0) {
				// 为主商品初始化规格选择
				Object.keys(item.attr).forEach(attrId => {
					const attribute = item.attr[attrId];
					if (attribute && attribute.children && attribute.children.length > 0) {
						// 默认选择第一个选项
						attribute.children.forEach((child, index) => {
							child.selected = index === 0 ? 1 : 0;
						});
					}
				});
			}

			// 为子商品初始化规格选择
			if (item.pro && Array.isArray(item.pro)) {
				item.pro.forEach(subItem => {
					if (subItem.attr && Object.keys(subItem.attr).length > 0) {
						Object.keys(subItem.attr).forEach(attrId => {
							const attribute = subItem.attr[attrId];
							if (attribute && attribute.children && attribute.children.length > 0) {
								// 默认选择第一个选项
								attribute.children.forEach((child, index) => {
									child.selected = index === 0 ? 1 : 0;
								});
							}
						});
					}
				});
			}
		},

		// 初始化套餐商品的规格选择状态
		initPackageSkuSelection() {
			this.groupPurchaseData.forEach(item => {
				// 如果商品有规格属性但没有选中的规格，初始化选择
				if (item.attr && Object.keys(item.attr).length > 0 && !item.selectedAttrs) {
					// 为每个规格属性选择第一个选项
					const selectedAttrs = {};
					Object.keys(item.attr).forEach(attrId => {
						const attribute = item.attr[attrId];
						if (attribute && attribute.children && attribute.children.length > 0) {
							const firstOption = attribute.children[0];
							selectedAttrs[attrId] = {
								vid: firstOption.vid,
								name: firstOption.name,
								pic: firstOption.pic
							};
						}
					});

					// 使用Vue.set确保响应式更新
					this.$set(item, 'selectedAttrs', selectedAttrs);
				}
			});
		},
		handleSkuSelect(event) {
			const { indexW, indexN } = event;
			this.tapAttr(indexW, indexN);
		},
		handleQuantityChange(value) {
			this.numberValue = value;
		},
		// Review 组件事件处理
		handleReviewClick(proId) {
			this.$tab.navigateTo(`/pages/review/review?ProId=${proId}`);
		},
		// QA 组件事件处理
		handleQAClick(proId) {
			this.$tab.navigateTo(`/pages/QA/QA?ProId=${proId}`);
		},
		// MaybeLike 组件事件处理
		handleMaybeLikeClick(item) {
			this.$tab.navigateTo(`/pages/goodsDetail/goodsDetail?ProId=${item.ProId}`);
		},
		// PackageBuy 组件事件处理
		checkboxChange(event) {
			// 处理组合购买复选框变化
			console.log('PackageBuy checkbox change:', event);
			// 这里可以添加具体的业务逻辑，比如更新选中状态、计算价格等
		},
		changeQty(item, event) {
			// 处理组合购买数量变化
			console.log('PackageBuy quantity change:', item, event);
			// 这里可以添加具体的业务逻辑，比如更新数量、重新计算价格等
		},

		// 处理组合购买数据更新
		handleGroupPurchaseUpdate(updatedItems) {
			console.log('组合购买数据更新:', updatedItems);
			this.groupPurchaseData = updatedItems;
			console.log("🚀 ~ file: goodsDetail.vue:866 ~ this.groupPurchaseData :", this.groupPurchaseData)

			// 计算组合购买的总价格和节省金额
			this.calculateGroupPurchaseTotal();

			// 触发自定义事件，通知父组件或其他组件
			this.$emit('groupPurchaseChanged', {
				items: updatedItems,
				total: this.getGroupPurchaseTotal(),
				savings: this.getGroupPurchaseSavings()
			});
		},

		// 处理组合促销数据更新
		handleCombinationPromotionUpdate(updatedItems) {
			console.log('组合促销数据更新:', updatedItems);
			this.combinationPromotionData = updatedItems;

			// 计算组合促销的总价格和节省金额
			this.calculateCombinationPromotionTotal();

			// 触发自定义事件，通知父组件或其他组件
			this.$emit('combinationPromotionChanged', {
				items: updatedItems,
				total: this.getCombinationPromotionTotal(),
				savings: this.getCombinationPromotionSavings()
			});
		},

		// 计算组合购买总价
		calculateGroupPurchaseTotal() {
			let total = 0;
			let originalTotal = 0;

			this.groupPurchaseData.forEach(item => {
				if (item.checked) {
					// 主商品价格
					total += parseFloat(item.CurPrice || 0);
					originalTotal += parseFloat(item.OldPrice || 0);

					// 子商品价格
					if (item.pro && Array.isArray(item.pro)) {
						item.pro.forEach(subItem => {
							if (subItem.checked) {
								total += parseFloat(subItem.CurPrice || 0);
								originalTotal += parseFloat(subItem.OldPrice || 0);
							}
						});
					}
				}
			});

			this.groupPurchaseTotal = total;
			this.groupPurchaseOriginalTotal = originalTotal;
			this.groupPurchaseSavings = originalTotal - total;

			return {
				total,
				originalTotal,
				savings: originalTotal - total
			};
		},

		// 计算组合促销总价
		calculateCombinationPromotionTotal() {
			let total = 0;
			let originalTotal = 0;

			this.combinationPromotionData.forEach(item => {
				if (item.checked) {
					// 主商品价格
					total += parseFloat(item.CurPrice || 0);
					originalTotal += parseFloat(item.OldPrice || 0);

					// 子商品价格
					if (item.pro && Array.isArray(item.pro)) {
						item.pro.forEach(subItem => {
							if (subItem.checked) {
								total += parseFloat(subItem.CurPrice || 0);
								originalTotal += parseFloat(subItem.OldPrice || 0);
							}
						});
					}
				}
			});

			this.combinationPromotionTotal = total;
			this.combinationPromotionOriginalTotal = originalTotal;
			this.combinationPromotionSavings = originalTotal - total;

			return {
				total,
				originalTotal,
				savings: originalTotal - total
			};
		},

		// 获取组合购买总价
		getGroupPurchaseTotal() {
			return this.groupPurchaseTotal || 0;
		},

		// 获取组合购买节省金额
		getGroupPurchaseSavings() {
			return this.groupPurchaseSavings || 0;
		},

		// 获取组合促销总价
		getCombinationPromotionTotal() {
			return this.combinationPromotionTotal || 0;
		},

		// 获取组合促销节省金额
		getCombinationPromotionSavings() {
			return this.combinationPromotionSavings || 0;
		},

		// 获取选中的组合购买商品
		getSelectedGroupPurchaseItems() {
			const selectedItems = [];

			this.groupPurchaseData.forEach(item => {
				if (item.checked) {
					selectedItems.push({
						...item,
						type: 'main'
					});
				}

				if (item.pro && Array.isArray(item.pro)) {
					item.pro.forEach(subItem => {
						if (subItem.checked) {
							selectedItems.push({
								...subItem,
								type: 'sub',
								parentId: item.PId || item.ProId
							});
						}
					});
				}
			});

			return selectedItems;
		},

		// 获取选中的组合促销商品
		getSelectedCombinationPromotionItems() {
			const selectedItems = [];

			this.combinationPromotionData.forEach(item => {
				if (item.checked) {
					selectedItems.push({
						...item,
						type: 'main'
					});
				}

				if (item.pro && Array.isArray(item.pro)) {
					item.pro.forEach(subItem => {
						if (subItem.checked) {
							selectedItems.push({
								...subItem,
								type: 'sub',
								parentId: item.PId || item.ProId
							});
						}
					});
				}
			});

			return selectedItems;
		},

		// 验证组合购买选择
		validateGroupPurchaseSelection() {
			const selectedItems = this.getSelectedGroupPurchaseItems();

			// 检查是否有选中的商品
			if (selectedItems.length === 0) {
				return {
					valid: false,
					message: 'Please select at least one item from the package'
				};
			}

			// 检查选中的商品是否都有完整的规格选择
			for (const item of selectedItems) {
				if (item.attr && Object.keys(item.attr).length > 0) {
					const hasUnselectedAttr = Object.keys(item.attr).some(attrId => {
						const attrGroup = item.attr[attrId];
						return !attrGroup.children.some(child => child.selected === 1);
					});

					if (hasUnselectedAttr) {
						return {
							valid: false,
							message: `Please select all specifications for ${item.Name || item.Name_en}`
						};
					}
				}
			}

			return {
				valid: true,
				selectedItems
			};
		},

		// 验证组合促销选择
		validateCombinationPromotionSelection() {
			const selectedItems = this.getSelectedCombinationPromotionItems();

			// 检查是否有选中的商品
			if (selectedItems.length === 0) {
				return {
					valid: false,
					message: 'Please select at least one item from the promotion'
				};
			}

			// 检查选中的商品是否都有完整的规格选择
			for (const item of selectedItems) {
				if (item.attr && Object.keys(item.attr).length > 0) {
					const hasUnselectedAttr = Object.keys(item.attr).some(attrId => {
						const attrGroup = item.attr[attrId];
						return !attrGroup.children.some(child => child.selected === 1);
					});

					if (hasUnselectedAttr) {
						return {
							valid: false,
							message: `Please select all specifications for ${item.Name || item.Name_en}`
						};
					}
				}
			}

			return {
				valid: true,
				selectedItems
			};
		},
		// 处理套餐商品规格选择
		handlePackageSkuSelected(data) {
			console.log('套餐商品规格选择:', data);
			// 更新套餐商品的规格信息
			const { product, selection } = data;
			// 这里可以添加更新价格、库存等逻辑
		},
		// 3D商品购物车处理
		handle3DAddToCart(cartData) {

			if (this.isAddLoading) {
				return;
			}

			this.isAddLoading = true;

			add3DToCart(cartData).then(res => {
				if (res.status == 200) {
					this.$toast({
						title: res.message,
						image: "/static/assets/common/add_successfully.png"
					});
					// 更新购物车数量
					this.$store.commit('SET_CARTNUM', +this.cartNum + 1);
				} else {
					this.$toast({
						title: res.message || 'Add to cart failed',
						icon: 'error'
					});
				}
			}).catch(err => {
				console.error('3D商品加入购物车失败:', err);
				this.$toast({
					title: 'Add to cart failed',
					icon: 'error'
				});
			}).finally(() => {
				this.isAddLoading = false;
			});
		},

		tapAttr(indexW, indexN) {
			console.log(indexW, indexN)
			const currentSku = this.skuData.attr[indexW].children[indexN];
			currentSku.selected = currentSku.selected === 1 ? 0 : 1;
			this.skuData.attr[indexW].children.map((item, index) => {
				if (index !== indexN) {
					item.selected = 0;
				}
			});

			this.getSelectdSkuData();

			// 更新规格禁用状态
			this.skuManager.updateSkuDisabledStatus(this.selectedSku);

			let data = {
				ProId: this.ProId,
			}

			for (const key in this.selectedSku) {
				data[`Attr_ary[${key}]`] = this.selectedSku[key];
			}

			const originSkuAttr = Object.keys(this.skuData.attr);
			const selectedSkuAttr = Object.keys(this.selectedSku);

			if (originSkuAttr.length === selectedSkuAttr.length) {
				this.getCartSelectPopCart(data);
			}
			// 移除这里的 numberValue = 0，让查询完成后保持为1
		},
		getSelectdSkuData() {
			this.selectedSku = {};
			for (const attrId in this.skuData.attr) {
				const selectedChild = this.skuData.attr[attrId].children.find(child => child.selected === 1);

				if (selectedChild) {
					this.selectedSku[`${attrId}`] = selectedChild.vid;
				}
			}
		},
		// 规格属性选择
		getCartSelectPopCart(data) {
			getCartSelectPopCart(data).then(data => {
				const {
					Price,
					MarketPrice,
					PicPath,
					Stock
				} = data;

				this.goodsPrice = Price;
				this.goodsMarketPrice = MarketPrice;
				this.productImg = PicPath;
				this.goodsStock = +Stock;

				// 查询完成后，将数量默认设置为1
				this.numberValue = 1;
				console.log('查询完成，数量设置为1');
			})
		},
		// 组合购买的选择项
		checkboxGroupPurchaseData() {

		},
		calculateSectionPositions() {
			const query = uni.createSelectorQuery().in(this);

			this.sectionTops = [];
			this.tabList.forEach((item, index) => {
				query.select(`#${item.anchor}`).boundingClientRect(rect => {
					if (rect) {
						this.sectionTops[index] = rect.top;
					}
				}).exec();
			});
		},
		// 阻止双击默认行为
		preventDoubleClick(e) {
			// 阻止双击事件的默认行为和冒泡
			e.preventDefault();
			e.stopPropagation();
			console.log('双击事件已被阻止');
		},
		// 返回顶部
		handleBackToTop() {
			// 直接使用uni.pageScrollTo，避免设置this.scrollTop导致的额外滚动
			uni.pageScrollTo({
				scrollTop: 0,
				duration: 300
			});
		},
		handleScroll(e) {
			const scrollTop = e.scrollTop;
			this.backScrollTop = scrollTop;
			this.lastScrollTop = scrollTop;
			if (!this.ticking) {
				requestAnimationFrame(() => {
					this.updateNavStatus(scrollTop);
					this.ticking = false;
				});
				this.ticking = true;
			}

			this.handleNavAppearance(scrollTop);
		},
		handleNavAppearance(scrollTop) {
			// 计算透明度 (0-1)
			let opacity = (scrollTop - this.showNavThreshold) / 50;
			opacity = Math.min(Math.max(opacity, 0), 1);

			// 计算垂直偏移 (20-0)
			let translateY = 20 - (opacity * 20);

			this.navOpacity = opacity;
			this.navTranslateY = 0

			// 当透明度大于0时固定导航栏
			this.isNavFixed = opacity > 0;
		},
		// 更新导航状态
		updateNavStatus(scrollTop) {
			// 加上一个偏移量（如导航栏高度），实现"接近顶部"才选中的效果
			const offset = 45; // 可根据需要调整
			const adjustedScrollTop = scrollTop + offset;

			// 找出当前应该激活的导航项
			for (let i = this.sectionTops.length - 1; i >= 0; i--) {
				if (adjustedScrollTop >= this.sectionTops[i]) {
					if (this.currentIndex !== i) {
						this.currentIndex = i;
					}
					break;
				}
			}
		},
		// 点击导航跳转到对应区块
		scrollToSection(event) {
			const { index, anchor } = event;
			this.currentIndex = index;

			// 获取目标区块的位置
			const query = uni.createSelectorQuery().in(this);
			query.select(`#${anchor}`).boundingClientRect(rect => {
				if (rect) {
					// 计算需要滚动的距离
					const scrollTop = rect.top + this.lastScrollTop - (this.isNavFixed ? 41 : 0);

					// 设置滚动位置
					this.scrollTop = this.lastScrollTop; // 先重置，确保能再次触发滚动
					uni.pageScrollTo({
						scrollTop: scrollTop,
						duration: 0
					});
				}
			}).exec();
		},
		onAddFavorite() {
			productFavorite({
				ProId: this.ProId
			}).then(res => {
				this.isFavorite = res.Status;

				if (res.Status === 1) {
					this.$toast({
						title: 'Successful collection',
						image: "/static/assets/common/add_successfully.png"
					})
				}
			}).catch(() => {
				this.$toast({
					title: 'Collection failure',
					icon: 'error'
				})
			})
		},

		// 规格选择判断
		checkSelectSku(skuData) {
			let Attr = {};
			let idObj = {};
			let skuNameList = [];
			if (skuData && Object.keys(skuData).length && this.goodsDetailData.pro.IsCombination !== '0') {
				for (const attrId in skuData) {
					const isSelectdSku = skuData[attrId].children.every(child => child.selected !== 1);
					if (isSelectdSku) {
						skuNameList.push(skuData[attrId].Name_en);
					}
				}

				if (skuNameList.length) {
					this.$toast({
						title: `Please select ${skuNameList.join(',')}`
					});
					return true;
				}

				for (const attrId in skuData) {
					const selectedChild = skuData[attrId].children.find(child => child.selected === 1);

					if (selectedChild) {
						Attr[attrId] = selectedChild.vid;
						idObj[`id[${attrId}]`] = selectedChild.vid;
					}
				}
			} else {
				Attr = undefined
			}
			return {
				Attr,
				idObj
			}
		},
		// 加入购物车
		handleAddToCart() {
			const skuSelectData = this.checkSelectSku(this.skuData?.attr);
			if (this.goodsStock == 0) {
				this.$toast({
					title: 'Sorry, this item is out of stock'
				})
				return;
			}
			if (this.numberValue == 0) {
				this.$toast({
					title: 'Please select quantity'
				})
				return;
			}
			if (skuSelectData == true) {
				return;
			}

			if (this.isAddLoading) {
				return;
			}

			this.isSubLoading = true;
			let data = {
				ProId: this.ProId,
				Qty: this.numberValue,
				ItemPrice: this.goodsPrice,
				products_type: this.productsType,
				Attr: skuSelectData?.Attr,
				SId: this.productsType == 2 ? this.goodsDetailData?.seckill?.SId : '',
				...skuSelectData?.idObj,
				giftCheck: 0,
			}
			//虚拟卡赠送表单校验
			const getGiftInfo = this.$refs?.refInformation?.getGiftInfo()
			if (getGiftInfo && getGiftInfo.isGift) {
				if (!this.$refs.refInformation.validateGiftForm()) {
					return
				}
				let { giftData } = getGiftInfo
				let infoMationParams = {
					card_time: giftData.date,
					card_content: giftData.message,
					card_name: giftData.name,
					card_email: giftData.email
				}
				data = { ...data, ...infoMationParams, giftCheck: 1 }
			}
			getCartAddToCart(data).then(res => {
				this.$toast({
					title: 'Add Successfully',
					image: "/static/assets/common/add_successfully.png"
				});

				this.$store.commit('SET_CARTNUM', +this.cartNum + 1);
			}).finally(() => {
				this.isSubLoading = false;
			})
		},
		// Payment
		handlePayment() {
			if (this.goodsStock == 0) {
				this.$toast({
					title: 'Sorry, this item is out of stock'
				})
				return;
			}
			if (this.numberValue == 0) {
				this.$toast({
					title: 'Please select quantity'
				})
				return;
			}
			const skuSelectData = this.checkSelectSku(this.skuData?.attr);
			console.log("🚀 ~ file: goodsDetail.vue:1436 ~ skuSelectData:", skuSelectData)
			let infoMationParams = {}
			//虚拟卡赠送表单校验
			const getGiftInfo = this.$refs?.refInformation?.getGiftInfo()
			if (getGiftInfo && getGiftInfo.isGift) {
				if (!this.$refs.refInformation.validateGiftForm()) {
					return
				}
				let { giftData } = getGiftInfo
				infoMationParams = {
					'Data[card_time]': giftData.date,
					'Data[card_content]': giftData.message,
					'Data[card_name]': giftData.name,
					'Data[card_email]': giftData.email
				}
			}

			if (skuSelectData == true) {
				return;
			}

			if (this.isPaymentLoading) {
				return;
			}

			this.isPaymentLoading = true;

			const {
				GPId,
				GoodsType,
				products_type
			} = this.goodsDetailData;
			const paymentData = {
				'Data': [this.ProId],
				'Data[ProId]': this.ProId,
				'Data[BuyType]': products_type,
				'Data[GPId]': GPId,
				'Data[Qty]': this.numberValue,
				'Data[Attr]': JSON.stringify(skuSelectData?.Attr),
				'Data[giftCheck]': GoodsType,

			}

			console.log('paymentData', paymentData)
			console.log(infoMationParams)
			this.isPaymentLoading = false;
			uni.setStorage({
				key: 'paymentData',
				data: JSON.stringify({ ...paymentData, ...infoMationParams })
			})
			this.$tab.navigateTo(
				`/pages/order_details/order_details?paymentData=${JSON.stringify({ ...paymentData, ...infoMationParams })}&isBuyType=1`)
		},

		async onShare() {
			this.$showLoading('Generating...');
			if (this.isShareLoading) {
				console.log("🚀 ~ file: goodsDetail.vue:1488 ~ this.isShareLoading:", this.isShareLoading)
				return
			}
			this.isShareLoading = true;
			try {
				let base64Data = await this.$refs.shareRef.getShareImage();
				console.log('shareImage', base64Data);
				// 增加分片数，减小每片大小以提高传输稳定性
				const size = 256 * 1024; // 减小到256KB每片
				const totalLength = base64Data.length;
				const chunkCount = Math.max(10, Math.ceil(totalLength / size)); // 最小分片数改为10
				console.log("🚀 ~ 当前分片数:", chunkCount);

				const platForm = uni.getDeviceInfo().platform;
				for (let i = 0; i < chunkCount; i++) {
					const start = i * Math.ceil(totalLength / chunkCount);
					const end = Math.min(start + Math.ceil(totalLength / chunkCount), totalLength);
					const chunk = base64Data.substring(start, end);

					if (platForm === 'android') {
						// type为1表示最后一片，否则为0
						window.zlbridge.shareApp(i === chunkCount - 1 ? 1 : 0, chunk, 'id=' + this.ProId + '&CId=' + (this.cartCId || '676'));
					} else if (platForm === 'ios') {
						let data = {
							type: i === chunkCount - 1 ? 1 : 0,
							imageData: chunk,
							url: 'id=' + this.ProId + (this.cartCId ? '&CId=' + this.cartCId : '')
						};
						window.webkit?.messageHandlers.shareApp.postMessage(data);
					}
				}
				this.isShareLoading = false;
			} catch (err) {
				this.isShareLoading = false;
			}
			this.$hideLoading();
		},
		// 查看HTML页面内容（示例方法）
		viewHtmlContent(Aid = '137') {
			this.$tab.navigateTo(`/pages/blank/blank?Aid=${Aid}`);
		},
		// 组合购买加入购物车
		handleGroupBuyAddToCart() {
			this.handlePackageAddToCart();
		},

		// 判断商品是否可通过提交
		//1.父级有子商品商品逻辑如下：
		// 1.1必须要有一个子商品勾选
		// 1.2勾选的子商品有规格情况下要选规格，没有就不要求
		//1.3父级也要判断规格流程
		//2.父级满足条件1个勾选就可以提交数据，父级没有子商品，只要满足规格的逻辑也视为有数据
		getCheckedValidPackageItems(items) {
			let hasValidChecked = false;

			for (const item of items) {
				const hasSubs = Array.isArray(item.pro) && item.pro.length > 0;

				if (!item.checked) continue; // 主商品没勾选，跳过

				// 主商品被勾选，检查是否需要选规格（attr 有内容则说明有规格需求）
				if (item.attr && Object.keys(item.attr).length > 0 && !item.popData) {
					return false;
				}

				if (hasSubs) {
					const checkedSubs = item.pro.filter(sub => sub.checked);
					if (checkedSubs.length === 0) return false; // 有子商品但一个都没选

					// 检查每个勾选的子商品是否需要规格而没选
					for (const sub of checkedSubs) {
						if (sub.attr && Object.keys(sub.attr).length > 0 && !sub.popData) {
							return false;
						}
					}
				}

				// 满足任一勾选且规格通过的商品，标记为通过
				hasValidChecked = true;
			}

			// 若遍历完后，没有任何勾选商品，视为不通过
			return hasValidChecked;
		},

		// 组装接口参数（严格按接口文档，支持主商品/子商品）
		buildPackageParams(item, products_type) {
			// item: 单个主商品（含子商品在 item.pro）
			const ProIdArr = [];
			const ExtAttr = {};
			let PId = '';

			// 逻辑检查：item 必须存在且为对象
			if (!item || typeof item !== 'object') {
				console.error('buildPackageParams: 参数 item 非法', item);
				return {};
			}

			if (item.checked) {
				// 主商品
				ProIdArr.push(item.ProId);
				PId = item.PId || PId; // 取第一个主商品的套餐ID
				// 主商品规格
				if (item.popData && item.popData.Attr && Object.keys(item.popData.Attr).length > 0) {
					ExtAttr[item.ProId] = { ...item.popData.Attr };
					if (item.popData.Overseas) {
						ExtAttr[item.ProId].Overseas = item.popData.Overseas;
					}
				} else {
					ExtAttr[item.ProId] = null;
				}
				// 子商品
				if (item.pro && Array.isArray(item.pro)) {
					item.pro.forEach(subItem => {
						if (subItem.checked) {
							ProIdArr.push(subItem.ProId);
							if (subItem.popData && subItem.popData.Attr && Object.keys(subItem.popData.Attr).length > 0) {
								ExtAttr[subItem.ProId] = { ...subItem.popData.Attr };
								if (subItem.popData.Overseas) {
									ExtAttr[subItem.ProId].Overseas = subItem.popData.Overseas;
								}
							} else {
								ExtAttr[subItem.ProId] = null;
							}
						}
					});
				}
			} else {
				// 逻辑检查：未勾选主商品时，不应组装参数
				console.warn('buildPackageParams: 主商品未勾选', item);
				return {};
			}

			return {
				ProId: ProIdArr.join(','),
				PId,
				ExtAttr: JSON.stringify(ExtAttr),
				products_type,
				back: 1
			};
		},


		// 统一处理套餐/促销加入购物车，增强校验和提示
		handlePackageAddToCart() {
			// 组合套餐
			const isChecked = this.getCheckedValidPackageItems([...this.groupPurchaseData, ...this.combinationPromotionData]);
			console.log("🚀 ~ file: goodsDetail.vue:1666 ~ isChecked:", isChecked)
			if (!isChecked) {
				this.$toast({ title: `Please select a specification` });
				return;
			}
			// 处理组合套餐商品
			(this.groupPurchaseData || []).forEach(item => {
				if (item.checked) {
					try {
						const params = this.buildPackageParams(item, 3);
						this.callAddToCartAPI(params);
					} catch (error) {
						console.error('组合套餐规格验证失败:', error.message);
						this.$toast({
							title: error.message,
							icon: 'none'
						});
					}
				}
			});

			// 处理组合促销商品
			(this.combinationPromotionData || []).forEach(item => {
				if (item.checked) {
					try {
						const params = this.buildPackageParams(item, 4);
						this.callAddToCartAPI(params);
					} catch (error) {
						console.error('组合促销规格验证失败:', error.message);
						this.$toast({
							title: error.message,
							icon: 'none'
						});
					}
				}
			});
		},

		// 调用加入购物车 API
		async callAddToCartAPI(params) {
			try {

				const res = await packageAddToCart(params);
				console.log("🚀 ~ file: goodsDetail.vue:1690 ~ res:", res)
				this.$toast({
					title: 'Add Successfully',
					image: "/static/assets/common/add_successfully.png"
				});
				this.$store.commit('SET_CARTNUM', +this.cartNum + 1);
			} catch (err) {
				console.error('加入购物车失败:', err);
				this.$toast({
					title: 'Add to cart failed',
					icon: 'error'
				});
			}
		},

	}
}
</script>

<style lang="scss" scoped>
.goods-detail-box {
	position: relative;
	/* 禁用双击选择文本 */
	user-select: none;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	/* 禁用双击缩放 */
	touch-action: manipulation;
	-webkit-touch-callout: none;

	::v-deep .u-tabbar__content__item-wrapper {
		min-height: 108rpx;
	}

	.default-pad {
		padding: 38rpx 31rpx;
	}

	.SoldOut-box {
		width: 477rpx;
		height: 85rpx;
		background: #F0F0F0;
		border-radius: 69rpx;
		font-size: 38rpx;
		color: #8C8C8C;
		display: inline-flex;
		justify-content: center;
		align-items: center;
	}

	// .tab-box {
	// 	box-shadow: 0px 2rpx 4rpx 0px rgba(0, 0, 0, 0.1);

	// 	::v-deep .u-tabs__wrapper__nav__item {
	// 		height: 108rpx !important;
	// 		flex: 1;
	// 	}
	// }

	.content-scroll {
		// height: 100vh;
	}





	.goods-content {
		background: #fff;
		border-radius: 31rpx 31rpx 0 0;
		position: relative;
		// padding-bottom: 40rpx;

		.favorite-box {
			position: absolute;
			right: 112rpx;
			top: -20rpx;
			width: 60rpx;
			height: 60rpx;
			background: #FFFFFF;
			border-radius: 50%;
			display: flex;
			justify-content: center;
			align-items: center;

			img {
				width: 130%;
			}

			.iconfont {
				font-size: 40rpx;
			}

			.icon-aixin {
				color: #B5B5B5;
			}

			.icon-aixin_shixin {
				color: #FF4949;
			}
		}

		.share-box {
			position: absolute;
			right: 31rpx;
			top: -20rpx;
			width: 60rpx;
			height: 60rpx;
			background: #FFFFFF;
			border-radius: 50%;
			display: flex;
			justify-content: center;
			align-items: center;

			img {
				width: 130%;
			}

			.icon-fenxiang {
				font-size: 36rpx;
				color: #FFC149;
			}
		}




	}
}

.web-contaniner {
	width: 100%;
	height: 100%;
	border: none;
}

.product-media-container {
	position: relative;
	height: 512rpx;
}

.canvas-box {
	// position: absolute;
	// top: -50vh;
}

.add-to-cart-package-box {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
	font-size: 30rpx;
	color: #FF5A1E;
	font-weight: bold;
	width: 477rpx;
	height: 85rpx;
	background: #FBDED1;
	border-radius: 69rpx 69rpx 69rpx 69rpx;
	font-size: 38rpx;
}
</style>