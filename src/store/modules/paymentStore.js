import config from '@/config/index.js'

const paymentStore = {
	namespaced: true,
	state: {
		affirm_public_api_key: config.payment.affirm.publicApiKey,
		country_code: config.payment.affirm.countryCode,
		locale: config.payment.affirm.locale,
		amazonPayData: {},
		amazonPayConfig: config.payment.amazonPay
	},
	mutations: {
		SET_AFFIRM_PUBLIC_API_KEY(state, publicApiKey) {
			state.affirm_public_api_key = publicApiKey;
		},
		SET_AMAZON_PAY_DATA(state, amazonPayData) {
			state.amazonPayData = amazonPayData;
		}
	},
	actions: {
	}
}

export default paymentStore;