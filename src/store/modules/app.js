import {
	getOverseas
} from '@/api/common.js';
import {
	getCartCartItemList
} from '@/api/products.js';
import config from '@/config/index.js'

const app = {
	state: {
		baseUrl: config.baseURL, // Default baseUrl
		priceSymbol: '$',
		statusBarHeight: 0,
		statusBarAndNavHeight: 0,
		safeAreaBottom: 0,
		cartNum: '0',
		lang:{
			id:'',
			list:[]
		}
	},
	mutations: {
		SET_BASE_URL: (state, baseUrl) => {
			state.baseUrl = baseUrl;
		},
		SET_LANG: (state, lang) => {
			state.lang = lang;
		},
		SET_PRICE_SYMBOL: (state, priceSymbol) => {
			state.priceSymbol = priceSymbol;
		},
		SET_COUNTRY_CODE: (state, country_code) => {
			state.country_code = country_code;
		},
		SET_LOCALE: (state, locale) => {
			state.locale = locale;
		},
		SET_STATUSBARHEIGHT: (state, statusBarHeight) => {
			const systemInfo = uni.getSystemInfoSync()
			// 安全区域底部位置 - 屏幕高度 = 安全区域高度
			state.safeAreaBottom = systemInfo.screenHeight - systemInfo.safeArea.bottom
			state.statusBarHeight = statusBarHeight;
			state.statusBarAndNavHeight = state.statusBarHeight +   uni.upx2px(108);
		},
		SET_CARTNUM: (state, cartNum) => {
			state.cartNum = cartNum > 99 ? '..' : cartNum;
		},
		SET_CART_TABBAR_COUNT: (state) => {
			const newCart = state.cartNum;
			
			// if (newCart > 0) {
			// 	uni.setTabBarBadge({
			// 		index: 2,
			// 		text: newCart.toString()
			// 	})
			// } else {
			// 	uni.removeTabBarBadge({
			// 		index: 2
			// 	})
			// }
		}
	},
	actions: {
		// 获取价格符号
		getPriceSymbol({
			commit
		}) {
			getOverseas().then(data => {
				commit('SET_PRICE_SYMBOL', data?.Symbol);
				commit('SET_LANG',{
					list: data?.OverseasArr,
					id:data?.Overseas_id
				});
			})
			},
		// 获取购物车数量
		getCartNum({
			commit
		}) {
			getCartCartItemList().then(data => {
				commit('SET_CARTNUM', data?.row_count ?? 0);
			})
		}
	},
}

export default app;