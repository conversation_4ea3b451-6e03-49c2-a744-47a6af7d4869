import {
	getToken,
	setToken,
	removeToken
} from '@/utils/security/auth'

const user = {
	namespaced: true,
	state: {
		platForm: null,
		token: getToken(),
	},
	mutations: {
		SET_PLATFORM: (state, platForm) => {
			state.platForm = platForm;
		},
		SET_TOKEN: (state, token) => {
			state.token = token
		},
		TO_SERVICE: (state) => {
			console.log('state.platForm', state.platForm);
			if (state.platForm === 'android') {
				window.zlbridge.routeApp('app/me/service');
			}

			if (state.platForm === 'ios') {
				window.webkit?.messageHandlers.routeApp.postMessage('app/me/service');
			}
		},
		TO_APP_PAGE: (state, page) => {
			console.log('page', page)
			if (state.platForm === 'android') {
				window.zlbridge.routeApp(page);
			}

			if (state.platForm === 'ios') {
				window.webkit?.messageHandlers.routeApp.postMessage(page);
			}
		}
	}
}

export default user;