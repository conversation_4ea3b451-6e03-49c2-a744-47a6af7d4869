<script>
import {
	getToken,
	setToken,
} from '@/utils/security/auth';
import {
	mapActions,
	mapGetters
} from 'vuex';

// #ifdef H5
// import h5PageAnimation from './components/h5-page-animation/index';
// import pageAnimation from './components/page-animation'
// #endif
import { debounce } from "lodash-es"
export default {
	data() {
		return {
		}
	},
	// #ifdef H5
	// mixins: [pageAnimation],
	// mixins: [h5PageAnimation],
	// #endif
	onLaunch: function (e) {
		console.log('App Launch')
		window.toWebPage = this.toWebPage.bind(this)
		const platForm = uni.getDeviceInfo().platform;
		console.log("🚀 ~ file: App.vue:29 ~ platForm:", platForm)
		console.log('uni.getSystemInfoSync()', uni.getSystemInfoSync())
		this.$store.commit('user/SET_PLATFORM', platForm);

		if (platForm === 'android') {
			const token = window?.zlbridge?.getToken?.();
			window.getToken = this.getToken;
			this.getToken(token);
			const statusBarHeight = window.zlbridge?.getSystemStatusHeight?.();
			this.getStatusBarHeight(statusBarHeight);
			// Get baseUrl from Android bridge
			const baseUrl = window.zlbridge?.getBaseUrl?.();
			this.getBaseUrl(baseUrl);

		} else if (platForm === 'ios') {
			window.getToken = this.getToken;
			window.webkit?.messageHandlers.getToken.postMessage(null);
			window.getStatusBarHeight = this.getStatusBarHeight;
			window.webkit?.messageHandlers.getStatusBarHeight.postMessage(null);
			window.toWebPage = this.toWebPage.bind(this)
			window.webkit?.messageHandlers.toWebPage.postMessage(null);
			window.getBaseUrl = this.getBaseUrl;
			window.webkit?.messageHandlers?.getBaseUrl.postMessage(null);
		}
		if (this.isDev || uni.getStorageSync('isDev')) {
			// const token = 'eyJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJBZGRtb3Rvci5jbGllbnQiLCJmVXNlcklkIjoxNDIzNzQsInNjb3BlcyI6IkFjY2VzcyIsImlzcyI6IkFkZG1vdG9yLnNlcnZlciIsImFwaV9sb2dpbl91c2VyX2tleSI6IjAxZjI3YTViLWE0OWUtNDQyNS1iZGJmLWNhYmFlNWVhMTZlZiIsImFwaV9sb2dpbl91c2VyX2lkX2tleSI6MTQyMzc0LCJleHAiOjE3ODI1NjI0ODAsIm5iZiI6MTc1MTAyNjQ4MCwiaWF0IjoxNzUxMDI2NDgwfQ.J3vCx5Cwlf-JpPDy-8yvpPYKcvX3KCvUyahQGLljFm8'
			// const token =
			// 	'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJVc2VySWQiOiIxMDE5MDU4Iiwic2NvcGVzIjoiQWNjZXNzIiwiaXNzIjoiQWRkbW90b3Iuc2VydmVyIiwiYXVkIjoiQWRkbW90b3IuY2xpZW50IiwiaWF0IjoxNzQ3OTkwNzY4LjEyOTM2MywibmJmIjoxNzQ3OTkwNzY5LjEyOTM2MywiZXhwIjoxNzc5NTI2NzY4LjEyOTM2M30.F0UEK8Xmk8xQYeP1Cqjtt28kbLlas1TF2NYn0o82buk';
			//正式
			// const token = `eyJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJBZGRtb3Rvci5jbGllbnQiLCJmVXNlcklkIjoxNDIxOTAsInNjb3BlcyI6IkFjY2VzcyIsImlzcyI6IkFkZG1vdG9yLnNlcnZlciIsImFwaV9sb2dpbl91c2VyX2tleSI6IjMxOGFjNGZkLWE5ZDUtNGM5NC05ZTE0LWJkN2YzNWQ3MmIyZCIsImFwaV9sb2dpbl91c2VyX2lkX2tleSI6MTQyMTkwLCJleHAiOjE3ODUzMjA4NDksIm5iZiI6MTc1Mzc4NDg0OSwiaWF0IjoxNzUzNzg0ODQ5fQ.IZBZHqwMgV94d4ocruTj7LywxkW5bjl9JsD86fGymTA`
			const token = `eyJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJBZGRtb3Rvci5jbGllbnQiLCJmVXNlcklkIjoxMDE5MDc4LCJzY29wZXMiOiJBY2Nlc3MiLCJpc3MiOiJBZGRtb3Rvci5zZXJ2ZXIiLCJhcGlfbG9naW5fdXNlcl9rZXkiOiI0NTk0ODc3Mi1lOGRkLTQ3Y2YtOGIyMi04ZTg3NzdhYWY1MDIiLCJhcGlfbG9naW5fdXNlcl9pZF9rZXkiOjEwMTkwNzgsImV4cCI6MTc4MzQ5NTA3NiwibmJmIjoxNzUxOTU5MDc2LCJpYXQiOjE3NTE5NTkwNzZ9.8yyjQrneSJj28JOkjW1Ur7puCd9nhaNDIhBQnfldb3w`
			this.getToken(token);
		}

	},
	onShow: function () {
		uni.$emit("setMescrollGlobalOption", {
			i18n: {
				type: 'en'
			}
		});
	},
	onHide: function () {
		console.log('App Hide')
	},
	watch: {
		cartNum(value) {
			if (value) {
				this.$store.commit('SET_CART_TABBAR_COUNT');
			}
		}
	},
	computed: {
		isDev() {
			return process.env.NODE_ENV === 'development';
		}
	},
	methods: {
		...mapActions(['getPriceSymbol', 'getCartNum']),
		getToken(token) {
			if (token) { setToken(token); this.$store.commit('user/SET_TOKEN', token); }
			this.getPriceSymbol();
			this.getCartNum()
		},
		getStatusBarHeight(statusBarHeight) {
			setTimeout(() => {
				if (statusBarHeight) {
					this.$store.commit('SET_STATUSBARHEIGHT', parseInt(statusBarHeight));
				}
			}, 100);
		},
		getBaseUrl(baseUrl) {
			if (!baseUrl) {
				console.error('APP传递的baseUrl为空');
				return;
			}

			// 导入setAppBaseUrl方法
			const { setAppBaseUrl } = require('@/utils/request');
			// 设置APP传递的baseUrl
			setAppBaseUrl(baseUrl);

			// 同时更新store中的状态(可选)
			this.$store.commit('SET_BASE_URL', baseUrl);
		},
		toWebPage: function (pageUrl) {
			// 额外检查Vue实例挂载状态
			this.$nextTick(() => {
				this.$tab.redirectTo(pageUrl);
			});
		},
	}
}
</script>

<style lang="scss">
/*每个页面公共css */
@import '@/uni_modules/uview-ui/index.scss';
@import '@/static/style/index.scss';

/* 自定义底部高度变量 - 兼容低端手机 */
:root {
	--custom-tabbar-height: 94rpx;
	/* 多层回退方案 */
	--custom-window-bottom: 94rpx;
	/* 基础回退值 */
	--custom-window-bottom: calc(94rpx + constant(safe-area-inset-bottom));
	/* iOS 11.0-11.2 */
	--custom-window-bottom: calc(94rpx + env(safe-area-inset-bottom));
	/* 现代浏览器 */
}

/* 禁用iOS长按图片弹出菜单 */
image,
img {
	-webkit-touch-callout: none !important;
	-webkit-user-select: none !important;
	user-select: none !important;
}

/* 禁用整个页面的长按选择 */
* {
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-webkit-tap-highlight-color: transparent;
}

page {
	background-color: #F0F0F0;
	position: relative;
	min-height: calc(100vh - var(--window-bottom));
	// z-index: 999;
}

/* 修正tabBar高度变化后的页面计算 */
.uni-app--showtabbar .uni-page-wrapper {
	height: calc(100% - 94rpx) !important;
}

/* 兼容安全区域 */
.uni-app--showtabbar .uni-page-wrapper {
	height: calc(100% - 94rpx - constant(safe-area-inset-bottom)) !important;
	height: calc(100% - 94rpx - env(safe-area-inset-bottom)) !important;
}

/* 低端手机兼容性工具类 */
.fixed-bottom-compatible {
	position: fixed;
	/* 基础回退 - 不支持calc和env的设备 */
	bottom: 94rpx;
	/* 支持calc但不支持env的设备 */
	bottom: calc(94rpx + 20px);
	/* 估算安全区域 */
	/* iOS 11.0-11.2 */
	bottom: calc(94rpx + constant(safe-area-inset-bottom));
	/* 现代浏览器 */
	bottom: calc(94rpx + env(safe-area-inset-bottom));
}

/* 针对不同平台的适配 */
/* #ifdef H5 */
.fixed-bottom-compatible {
	bottom: 94rpx;
}

/* #endif */

/* #ifdef MP-WEIXIN */
.fixed-bottom-compatible {
	bottom: calc(94rpx + 20rpx);
	/* 微信小程序安全区域估算 */
}

/* #endif */

/* 低端Android设备适配 */
@media screen and (max-width: 360px) {
	.fixed-bottom-compatible {
		bottom: calc(94rpx + 10px);
		/* 小屏幕设备减少间距 */
	}
}

/* 针对不支持CSS变量的浏览器 */
@supports not (bottom: var(--custom-window-bottom)) {
	.fixed-bottom-compatible {
		bottom: 114rpx;
		/* 94rpx + 20rpx估算 */
	}
}
</style>