/**
 * 支付SDK统一配置文件
 * 新增支付方式时，只需要在这里配置即可
 */

import config from "./index.js";

/**
 * 支付SDK配置
 * 支持的配置项：
 * - enabled: 是否启用该支付方式
 * - paymentId: 支付方式ID（用于匹配后端）
 * - sdkLoader: SDK加载函数
 * - sdkChecker: SDK检查函数（检查window对象上的SDK是否存在）
 * - preloadConfig: 预加载时的配置参数
 */
export const PAYMENT_SDK_CONFIG = {
  paypal: {
    enabled: true,
    paymentId: 1,
    name: 'PayPal',
    sdkLoader: () => import('@/utils/payment/paypalSDK').then(m => m.default),
    sdkChecker: () => !!(window.paypal && window.paypal.Buttons),
    preloadConfig: {} // PayPal SDK 不需要特殊配置
  },
  
  amazonPay: {
    enabled: true,
    paymentId: 89,
    name: 'Amazon Pay',
    sdkLoader: () => import('@/utils/payment/amazonPaySDK').then(m => m.default),
    sdkChecker: () => !!(window.amazon && window.amazon.Pay),
    preloadConfig: {
      region: 'US',
      sandbox: config.amazonPay?.sandbox !== false,
      sdkSrc: config.amazonPay?.sdkSrc
    }
  },
  
  stripe: {
    enabled: true,
    paymentId: 2, // 需要与后端协商确定Stripe的ID
    name: 'Stripe',
    sdkLoader: () => import('@/utils/payment/stripeSDK').then(m => m.default),
    sdkChecker: () => !!(window.Stripe),
    preloadConfig: {
      publishableKey: config.payment?.stripe?.publishableKey || config.stripePublishableKey || config.stripe?.publishableKey || config.stripeKey,
      stripeAccount: config.payment?.stripe?.stripeAccount || config.stripe?.stripeAccount,
      locale: 'en'
    }
  }
  
  // 🚀 新增支付方式示例：
  // applePay: {
  //   enabled: false, // 暂时禁用
  //   paymentId: 90,
  //   name: 'Apple Pay',
  //   sdkLoader: () => import('@/utils/applePaySDK').then(m => m.default),
  //   sdkChecker: () => !!(window.ApplePaySession),
  //   preloadConfig: {
  //     merchantId: config.applePay?.merchantId,
  //     region: 'US'
  //   }
  // },
  
  // googlePay: {
  //   enabled: false,
  //   paymentId: 91,
  //   name: 'Google Pay',
  //   sdkLoader: () => import('@/utils/googlePaySDK').then(m => m.default),
  //   sdkChecker: () => !!(window.google && window.google.payments),
  //   preloadConfig: {
  //     environment: config.googlePay?.environment || 'TEST',
  //     merchantId: config.googlePay?.merchantId
  //   }
  // }
};

/**
 * 获取启用的支付方式列表
 */
export const getEnabledPaymentMethods = () => {
  return Object.entries(PAYMENT_SDK_CONFIG)
    .filter(([_, config]) => config.enabled)
    .map(([key, config]) => ({ key, ...config }));
};

/**
 * 根据支付方式key获取配置
 */
export const getPaymentConfig = (paymentKey) => {
  return PAYMENT_SDK_CONFIG[paymentKey];
};

/**
 * 根据支付ID获取配置
 */
export const getPaymentConfigById = (paymentId) => {
  const numericId = parseInt(paymentId);
  return Object.entries(PAYMENT_SDK_CONFIG)
    .find(([_, config]) => config.paymentId === numericId)?.[1];
};

/**
 * 检查支付方式是否启用
 */
export const isPaymentEnabled = (paymentKey) => {
  return PAYMENT_SDK_CONFIG[paymentKey]?.enabled === true;
};

/**
 * 全局开关：是否启用支付SDK预加载
 */
export const SDK_PRELOAD_ENABLED = true;

/**
 * 全局开关：是否启用路由级SDK检查
 */
export const ROUTE_SDK_CHECK_ENABLED = true;

/**
 * 全局开关：是否启用组件级SDK检查
 */
export const COMPONENT_SDK_CHECK_ENABLED = true;

/**
 * SDK检查间隔（毫秒）
 */
export const SDK_CHECK_INTERVAL = 10000; // 10秒

/**
 * SDK加载超时时间（毫秒）
 */
export const SDK_LOAD_TIMEOUT = 10000; // 10秒

/**
 * 组件等待预加载的超时时间（毫秒）
 */
export const COMPONENT_WAIT_TIMEOUT = 5000; // 5秒

export default PAYMENT_SDK_CONFIG;