// 配置文件

// 根据环境获取不同的配置
const getConfig = () => {
  const env = process.env.VUE_APP_ENV || "development";

  // 不同环境的配置
  const configs = {
    development: {
      baseURL: "https://api.binyo.net",
      paypalClientId:
        "Ac3DytkDXMloeAl_xWJj1bhbeGuTJzAaoaezx3a9YYDmVQ1sJvXp0bno-w9575Ea0DDaw28FWCSkcvNe",
      stripePublishableKey:
        "pk_test_51RnEZNQJykW4XD0RgZGZHHzrX7S2N8zBjPOD6x8LXr6ngPKX0X0nJNseS7LKqk7uM49fq0xWhWoMalSjF2gwsJXV0090gxagrV",
      shareUrl: "http://file.addmotor.com/appShare.html",
      threerglbUrl:
        "https://binyo.net/static/themes/default/products/3d/assets/init_env.glb",
      threeKTX2Url:
        "https://binyo.net/static/themes/default/products/3d/assets/je_gray_park.ktx2",
      // Amazon Pay 开发环境
      amazonPaySdkSrc: "https://static-na.payments-amazon.com/checkout.js",
      amazonPayMerchantId: "A1W9T6S1SN3JW7",
      amazonPayPublicKeyId: "SANDBOX-AFMMMEA6DSMQQH3ZKSGCTR6B",
      amazonPaySandbox: true,
      // Affirm 开发环境
      affirmPublicApiKey: "XT48GG9GFRIDEP4Q",
      affirmCountryCode: "USA",
      affirmLocale: "en_US",
    },
    test: {
      baseURL: "https://api.binyo.net",
      paypalClientId:
        "Ac3DytkDXMloeAl_xWJj1bhbeGuTJzAaoaezx3a9YYDmVQ1sJvXp0bno-w9575Ea0DDaw28FWCSkcvNe",
      stripePublishableKey:
        "pk_test_51RnEZNQJykW4XD0RgZGZHHzrX7S2N8zBjPOD6x8LXr6ngPKX0X0nJNseS7LKqk7uM49fq0xWhWoMalSjF2gwsJXV0090gxagrV",
      shareUrl: "http://file.addmotor.com/appShare.html",
      threerglbUrl:
        "https://binyo.net/static/themes/default/products/3d/assets/init_env.glb",
      threeKTX2Url:
        "https://binyo.net/static/themes/default/products/3d/assets/je_gray_park.ktx2",
      // Amazon Pay 测试环境
      amazonPaySdkSrc: "https://static-na.payments-amazon.com/checkout.js",
      amazonPayMerchantId: "A1W9T6S1SN3JW7",
      amazonPayPublicKeyId: "SANDBOX-AFMMMEA6DSMQQH3ZKSGCTR6B",
      amazonPaySandbox: true,
      // Affirm 测试环境
      affirmPublicApiKey: "XT48GG9GFRIDEP4Q",
      affirmCountryCode: "USA",
      affirmLocale: "en_US",
    },
    production: {
      baseURL: "https://storeapi.addmotor.com",
      paypalClientId:
        "Ac3DytkDXMloeAl_xWJj1bhbeGuTJzAaoaezx3a9YYDmVQ1sJvXp0bno-w9575Ea0DDaw28FWCSkcvNe",
      stripePublishableKey:
        "pk_test_51RnEZNQJykW4XD0RgZGZHHzrX7S2N8zBjPOD6x8LXr6ngPKX0X0nJNseS7LKqk7uM49fq0xWhWoMalSjF2gwsJXV0090gxagrV",
      shareUrl: "http://file.addmotor.com/appShare.html",
      threerglbUrl:
        "https://binyo.net/static/themes/default/products/3d/assets/init_env.glb",
      threeKTX2Url:
        "https://binyo.net/static/themes/default/products/3d/assets/je_gray_park.ktx2",
      // Amazon Pay 测试环境
      amazonPaySdkSrc: "https://static-na.payments-amazon.com/checkout.js",
      amazonPayMerchantId: "A1W9T6S1SN3JW7",
      amazonPayPublicKeyId: "SANDBOX-AFMMMEA6DSMQQH3ZKSGCTR6B",
      amazonPaySandbox: true,
      // Affirm 测试环境
      affirmPublicApiKey: "XT48GG9GFRIDEP4Q",
      affirmCountryCode: "USA",
      affirmLocale: "en_US",
    },
  };

  return configs[env] || configs.development;
};

const config = getConfig();

export default {
  // API基础URL
  baseURL: config.baseURL,
  shareUrl: config.shareUrl,
  threerglbUrl: config.threerglbUrl,
  threeKTX2Url: config.threeKTX2Url,
  // 应用配置
  app: {
    name: "Add Motor Shop",
    version: "1.0.0",
    env: process.env.VUE_APP_ENV || "development",
  },

  // 支付配置
  payment: {
    btnOpacity: 0,
    paypal: {
      clientId: process.env.PAYPAL_CLIENT_ID || config.paypalClientId,
    },
    stripe: {
      publishableKey:
        process.env.STRIPE_PUBLISHABLE_KEY || config.stripePublishableKey,
    },
    amazonPay: {
      sdkSrc: config.amazonPaySdkSrc,
      merchantId: config.amazonPayMerchantId,
      publicKeyId: config.amazonPayPublicKeyId,
      sandbox: config.amazonPaySandbox,
    },
    affirm: {
      publicApiKey: config.affirmPublicApiKey,
      countryCode: config.affirmCountryCode,
      locale: config.affirmLocale,
    },
  },
};
