# 支付SDK统一配置系统

## 📋 概述

这是一个统一管理所有支付SDK的配置系统，支持动态添加新的支付方式，提供三重保险机制确保支付功能在各种网络环境下都能正常工作。

## 🔧 如何新增支付方式

### 1. 在配置文件中添加新支付方式

编辑 `/src/config/paymentSDKConfig.js`：

```javascript
export const PAYMENT_SDK_CONFIG = {
  // 现有的支付方式...
  paypal: { /* ... */ },
  amazonPay: { /* ... */ },
  
  // 🚀 新增支付方式
  applePay: {
    enabled: true,                    // 是否启用
    paymentId: 90,                   // 后端支付方式ID
    name: 'Apple Pay',               // 显示名称
    sdkLoader: () => import('@/utils/applePaySDK').then(m => m.default),
    sdkChecker: () => !!(window.ApplePaySession),
    preloadConfig: {
      merchantId: config.applePay?.merchantId,
      region: 'US'
    }
  }
};
```

### 2. 创建对应的SDK加载器

创建 `/src/utils/applePaySDK.js`：

```javascript
/**
 * Apple Pay SDK 加载工具
 */

let applePaySDKPromise = null;

const loadApplePaySDK = (config = {}) => {
  if (applePaySDKPromise) {
    return applePaySDKPromise;
  }

  if (window.ApplePaySession) {
    applePaySDKPromise = Promise.resolve(window.ApplePaySession);
    return applePaySDKPromise;
  }

  console.log('📦 开始加载Apple Pay SDK...');
  
  applePaySDKPromise = new Promise((resolve, reject) => {
    // Apple Pay 通常不需要额外加载SDK
    // 直接检查浏览器支持
    if (window.ApplePaySession && ApplePaySession.canMakePayments()) {
      resolve(window.ApplePaySession);
    } else {
      reject(new Error('Apple Pay不被支持'));
    }
  });

  return applePaySDKPromise;
};

export default loadApplePaySDK;
```

### 3. 创建对应的支付按钮组件

#### Apple Pay 示例
创建 `/src/components/ApplePayButton/ApplePayButton.vue`：

```vue
<template>
  <div class="apple-pay-button" @click="handlePayment">
    Apple Pay
  </div>
</template>

<script>
import paymentSDKManager from '@/utils/paymentSDKManager.js';

export default {
  name: 'ApplePayButton',
  methods: {
    async handlePayment() {
      try {
        // 确保SDK可用
        await paymentSDKManager.ensureSDK('applePay');
        
        // 处理支付逻辑
        // ...
      } catch (error) {
        console.error('Apple Pay支付失败:', error);
      }
    }
  }
};
</script>
```

#### Stripe 集成示例 ✅ (已完成)
Stripe已经集成到统一系统中，现在支持：

- ✅ **应用启动预加载**: Stripe SDK在应用启动时自动预加载
- ✅ **路由级检查**: 页面切换时自动检查Stripe SDK状态
- ✅ **组件级兜底**: 支付按钮点击时确保SDK可用
- ✅ **统一配置**: 在`paymentSDKConfig.js`中统一管理

```javascript
// Stripe配置 (已添加到系统中)
stripe: {
  enabled: true,
  paymentId: 2,
  name: 'Stripe',
  sdkLoader: () => import('@/utils/stripeSDK').then(m => m.default),
  sdkChecker: () => !!(window.Stripe),
  preloadConfig: {
    publishableKey: config.stripe?.publishableKey,
    stripeAccount: config.stripe?.stripeAccount,
    locale: 'en'
  }
}
```

## 🎛️ 全局开关配置

在 `/src/config/paymentSDKConfig.js` 中可以控制各种开关：

```javascript
// 全局开关：是否启用支付SDK预加载
export const SDK_PRELOAD_ENABLED = true;

// 全局开关：是否启用路由级SDK检查
export const ROUTE_SDK_CHECK_ENABLED = true;

// 全局开关：是否启用组件级SDK检查
export const COMPONENT_SDK_CHECK_ENABLED = true;

// SDK检查间隔（毫秒）
export const SDK_CHECK_INTERVAL = 10000;

// SDK加载超时时间（毫秒）
export const SDK_LOAD_TIMEOUT = 10000;
```

## 🛡️ 三重保险机制

### 第一重：应用启动预加载
- **位置**: `main.js`
- **触发**: 应用启动时
- **作用**: 为良好网络环境提供最佳用户体验

### 第二重：路由级检查
- **位置**: 全局mixin (`sdkCheckMixin.js`)
- **触发**: 每次页面显示时
- **作用**: 在用户浏览过程中提前准备SDK

### 第三重：组件级兜底
- **位置**: 支付组件内部
- **触发**: 组件mounted + 支付按钮点击时
- **作用**: 确保支付功能100%可用

## 📊 如何禁用某个支付方式

### 临时禁用
```javascript
// 在 paymentSDKConfig.js 中
applePay: {
  enabled: false,  // 设置为 false
  // ... 其他配置
}
```

### 完全移除
直接从 `PAYMENT_SDK_CONFIG` 中删除对应的配置项。

## 🔍 调试和监控

### 查看SDK状态
```javascript
import paymentSDKManager from '@/utils/paymentSDKManager.js';

// 获取所有SDK状态
console.log(paymentSDKManager.getSDKStatus());

// 手动确保某个SDK
await paymentSDKManager.ensureSDK('applePay');

// 确保所有启用的SDK
await paymentSDKManager.ensureAllEnabledSDKs();
```

### 日志格式
```
🚀 开始预加载支付SDK...
📦 PayPal SDK预加载中...
📦 Stripe SDK预加载中...
📦 Amazon Pay SDK预加载中...
✅ PayPal SDK预加载完成
✅ Stripe SDK预加载完成
✅ Amazon Pay SDK预加载完成
🔍 路由守卫开始检查SDK状态...
✅ 路由守卫检查：所有SDK都已可用
```

## 🚨 注意事项

1. **支付ID唯一性**: 确保每个支付方式的 `paymentId` 在系统中唯一
2. **SDK检查函数**: `sdkChecker` 函数必须准确检查SDK是否真的可用
3. **配置参数**: `preloadConfig` 中的参数要与SDK加载器期望的参数匹配
4. **错误处理**: SDK加载器要有完善的错误处理机制

## 🔄 系统流程图

```
应用启动
    ↓
[第一重] 预加载所有启用的SDK
    ↓
用户浏览应用
    ↓
[第二重] 页面切换时检查SDK状态
    ↓
用户进入支付页面
    ↓
[第三重] 组件检查 + 点击时确认
    ↓
支付成功 ✅
```

这个系统确保了无论在什么网络环境下，用户都能获得流畅的支付体验！