<template>
	<view class="payment-button">
		<u-button class="amount" text="+Add" :loading="isAddLoading" @click="$emit('handleAddToCart')"></u-button>
		<u-button class="payment-btn" :text="subText" shape="circle" :loading="isPaymentLoading"
			@click="$emit('handlePayment')"></u-button>
	</view>
</template>

<script>
	import {
		mapGetters
	} from 'vuex'
	export default {
		name: "addOrBuyNow",
		props: {
			Price: {
				type: [String, Number],
				default: "0.00"
			},
			isPaymentLoading: {
				type: <PERSON>olean,
				default: false
			},
			isAddLoading: {
				type: Boolean,
				default: false
			},
			subText: {
				type: String,
				default: "Payment"
			}
		},
		data() {
			return {

			};
		},
		computed: {
			...mapGetters(['priceSymbol'])
		},
		methods: {

		}
	}
</script>

<style lang="scss" scoped>
	.payment-button {
		position: relative;
		width: 477rpx;

		.amount {
			position: absolute;
			left: 0;
			top: 0;
			z-index: 10;
			width: 194rpx;
			height: 84rpx;
			background: #FBDED1;
			border-radius: 48rpx 88rpx 4rpx 48rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-weight: bold;
			font-size: 38rpx;
			color: #FF5A1E;
			border: 1px solid #FF5A1E;
		}

		.payment-btn {
			background: transparent;
			border: none;
			font-weight: 600;
			font-size: 38rpx;
			color: #FFFFFF;
			background: #FF5A1E;
			height: 85rpx;
			border-radius: 69rpx;
			display: flex;
			justify-content: flex-end;
			padding-right: 18%;
		}
	}
</style>