<template>
	<view class="cell-box">
		<view class="cell-box-top">
			<slot name="left"></slot>
			<slot name="right"></slot>
		</view>
		<slot name="content"></slot>
	</view>
</template>

<script>
	export default {
		name:"cell-box",
		data() {
			return {
				
			};
		}
	}
</script>

<style lang="scss" scoped>
	.cell-box {
		padding: 0 31rpx;
		.cell-box-top {
			display: flex;
			justify-content: space-between;
			align-items: center;
  @include flex-gap(0, 20rpx); // 替换了 column-gap
		}
	}
</style>