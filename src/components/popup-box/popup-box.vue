<template>
	<view @touchmove.stop.prevent>
		<u-popup :show="isShow" :round="round" :closeable="closeable" @close="isShow = false" >
			<view class="header-box">
				<!-- 判断具名 slot 是否存在 -->
				<slot name="header" v-if="$slots.header"></slot>
				<!-- 如果没有 header slot，显示默认标题 -->
				<u--text v-if="!$slots.header" :text="mainTitle" color="#000" size="38rpx" bold align="center"></u--text>
			</view>

			<view class="content">
				<slot name="content"></slot>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		name: "popup-box",
		props: {
			mainTitle: {
				type: String,
				default: '标题'
			},
			round: {
				type: Number,
				default: 15
			},
			closeable: {
				type: Boolean,
				default: true
			}
		},
		data() {
			return {
				isShow: false
			};
		}
	}
</script>

<style lang="scss" scoped>
	.header-box {
		padding: 30rpx 0;
		border-bottom: 1px solid #F7F7F7;
	}

	.content {
		padding: 30rpx;
	}
</style>