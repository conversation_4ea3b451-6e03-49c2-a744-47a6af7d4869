# Popup Box 组件 - Slot 判断方法说明

## Vue 2 中判断 slot 的方法

### 1. 在模板中直接判断
```vue
<!-- 判断具名 slot -->
<slot name="header" v-if="$slots.header"></slot>

<!-- 判断默认 slot -->
<slot v-if="$slots.default"></slot>

<!-- 反向判断 -->
<div v-if="!$slots.content">没有内容</div>
```

### 2. 在 computed 中判断
```javascript
computed: {
  hasHeaderSlot() {
    return !!this.$slots.header;
  },
  hasContentSlot() {
    return !!this.$slots.content;
  },
  hasDefaultSlot() {
    return !!this.$slots.default;
  }
}
```

### 3. 在 methods 中判断
```javascript
methods: {
  hasSlot(name) {
    return !!this.$slots[name];
  },
  checkSlots() {
    console.log('Header slot:', !!this.$slots.header);
    console.log('Content slot:', !!this.$slots.content);
    console.log('Default slot:', !!this.$slots.default);
  }
}
```

## Vue 3 中判断 slot 的方法

### 1. 使用 $slots（推荐）
```vue
<!-- Vue 3 中推荐写法 -->
<slot name="header" v-if="$slots.header"></slot>

<!-- 或者使用函数调用 -->
<slot name="header" v-if="$slots.header()"></slot>
```

### 2. 使用 setup 语法
```javascript
// Vue 3 Composition API
import { useSlots } from 'vue'

export default {
  setup() {
    const slots = useSlots()
    
    const hasHeaderSlot = computed(() => !!slots.header)
    const hasContentSlot = computed(() => !!slots.content)
    
    return {
      hasHeaderSlot,
      hasContentSlot
    }
  }
}
```

## 实际使用示例

```vue
<template>
  <!-- 使用 popup-box 组件 -->
  <popup-box ref="myPopup" mainTitle="我的弹窗">
    <!-- 自定义 header -->
    <template slot="header">
      <div class="custom-header">自定义标题</div>
    </template>
    
    <!-- 自定义 content -->
    <template slot="content">
      <div class="custom-content">自定义内容</div>
    </template>
  </popup-box>
  
  <!-- 不提供 slot 的情况 -->
  <popup-box ref="simplePopup" mainTitle="简单弹窗">
    <!-- 只会显示默认标题和空内容提示 -->
  </popup-box>
</template>
```

## 注意事项

1. **Vue 2**: 使用 `this.$slots.slotName` 判断
2. **Vue 3**: 可以使用 `this.$slots.slotName` 或 `this.$slots.slotName()`
3. **空白内容**: 即使 slot 存在但为空，`$slots.slotName` 仍然为 `true`
4. **动态判断**: slot 的存在性在组件创建时确定，运行时不会改变

## 调试技巧

```javascript
// 在组件的 mounted 钩子中查看所有 slots
mounted() {
  console.log('Available slots:', Object.keys(this.$slots));
  console.log('Slots content:', this.$slots);
}
``` 