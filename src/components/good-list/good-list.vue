<template>
	<view class="goods-item-box">
		<view class="waterfall">
			<view class="column" v-for="(column, columnIndex) in columns" :key="columnIndex">
				<view class="item" v-for="(item, index) in column" :key="index">
					<!-- 判断是否为广告位 -->
					<block v-if="isAdPosition(columnIndex, index) && path_app">
						<u--image class="ad-image" :src="path_app" mode="scaleToFill" width="100%" height="463rpx"
							radius="31rpx"></u--image>
					</block>

					<!-- 普通商品内容 -->
					<view class="goods-intro-box"
						@click="$tab.navigateTo(`/pages/goodsDetail/goodsDetail?ProId=${item.ProId}`)">
						<image class="favorite_icon" :src="favoriteImg(item.isFavorite)"
							@click.stop="onAddFavorite(item)">
						</image>
						<view class="bike-img-box">
							<!-- <u--image :src="item.Pic" mode="widthFix" width="100%" height="288rpx" radius="31rpx"
								duration="0">
								<template v-slot:loading>
									<u-loading-icon></u-loading-icon>
								</template>
								<template v-slot:error>
									<u-icon name="error-circle" size="24"></u-icon>
								</template>
							</u--image> -->
							<easy-loadimage mode="widthFix" :scroll-top="scrollTop" loading-mode="skeleton-1" :image-src="item.Pic"></easy-loadimage>
						</view>
						<view class="goods-info-box">
							<u--text :text="item.Name" color="#262626" size="26rpx" bold
								margin="19rpx 0 4rpx" lines="2"></u--text>
							<view class="discount-price-box" v-if="item.tipType === 1 && item.tip">
								<image class="sale_icon" src="@/static/assets/home/<USER>"></image>
								<u--text :text="item.tip" lines="1" color="#FF5A1E" size="22rpx"
									margin="0 4px 0 4px"></u--text>
							</view>
							<view class="price-footer-box">
								<view class="price-box">
									<text class="origin-price">${{ item.Price }}</text>
									<text class="discount-price"
										v-show="item.MarketPrice !== '0.00'">${{ item.MarketPrice }}</text>
								</view>
								<image class="addCart" src="@/static/assets/home/<USER>"
									@click.stop="onAddCart(item)"></image>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			path_app: {
				type: String,
				default: ""
			},
			list: {
				type: Array,
				default () {
					return []
				}
			},
			columns: {
				type: Array,
				default () {
					return [
						[],
						[]
					]
				}
			},
			scrollTop: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				isFavorite: false
			};
		},
		computed: {
			favoriteImg() {
				return isFavorite => {
					return require(`@/static/assets/home/<USER>'favorite' : 'favorite_no' }.png`);
				}
			}
		},
		methods: {
			// 判断是否为广告位
			isAdPosition(columnIndex, index) {
				return columnIndex === 1 && index === 0; // 第一列第二行
			},
			// 图片加载完成时的处理
			onImageLoad(event, columnIndex, index) {
				const imgWidth = event?.detail?.width;
				const imgHeight = event?.detail?.height;
				const columnWidth = this.columns[columnIndex].length === 0 ? 0 : this.columns[columnIndex][this.columns[
					columnIndex].length - 1].height;
				const height = columnWidth + imgHeight;
				this.columns[columnIndex][index] = {
					height
				};
			},
			onAddFavorite({
				ProId,
				isFavorite
			}) {
				this.$emit("addFavoriteFn", {
					ProId,
					isFavorite
				})
			},
			onAddCart(item) {
				if (item.tipType === 0) { // 0代表无库存
					return this.$toast({
						title: item.tip
					})
				}

				this.$emit("onAddCart", item)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.goods-item-box {
		display: flex;
		justify-content: center;
		align-items: flex-start;
		padding: 0 31rpx;
		box-sizing: border-box;

		.waterfall {
			display: flex;
			flex-wrap: wrap;
			width: 100%;
			box-sizing: border-box;
			justify-content: space-between;

			.column {
				width: calc(50% - 15rpx);

				.item {
					margin-bottom: 31rpx;

					.ad-image {
						margin-bottom: 31rpx;
					}
				}
			}
		}

		.goods-intro-box {
			background: #FFFFFF;
			box-shadow: 0px 35 87rpx 0px rgba(167, 167, 167, 0.2);
			border-radius: 31rpx;
			padding: 33rpx 23rpx 27rpx;
			box-sizing: border-box;
			position: relative;
			overflow: hidden;

			.favorite_icon {
				position: absolute;
				top: 6rpx;
				right: 16rpx;
				z-index: 666;
				width: 70rpx;
				height: 70rpx;
			}

			.bike-img-box {
				width: 100%;
				min-height: 288rpx;
				padding: 20rpx 0 0rpx;
			}

			.goods-info-box {
				.discount-price-box {
					display: inline-flex;
					align-items: center;
					max-width: 100%;
					height: 33rpx;
					border-radius: 8rpx;
					border: 2rpx solid #FF5A1E;
					box-sizing: border-box;
					overflow: hidden;

					.sale_icon {
						width: 30rpx;
						height: 30rpx;
						background: #FF5A1E;
						border-radius: 4rpx;
					}
				}

				.price-footer-box {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-top: 10rpx;

					.price-box {
						.origin-price {
							font-weight: bold;
							font-size: 24rpx;
							color: #FF5A1E;
							margin-right: 10rpx;
						}

						.discount-price {
							font-weight: bold;
							font-size: 24rpx;
							color: #999999;
							text-decoration: line-through;
						}
					}

					.addCart {
						width: 36rpx;
						height: 36rpx;
					}
				}
			}
		}
	}
</style>