<template>
	<u-tabbar :fixed="true" :placeholder="true" :safeAreaInsetBottom="true" class="footer-options-box">
		<view class="options-box">
			<view class="options-left" @click="$emit('handleLeftFn')" v-if="showService">
				<slot name="optionsLeft">
					<u--image src="/static/assets/mine/order_services.png" width="85rpx" height="85rpx"></u--image>
				</slot>
			</view>
			<view class="options-right">
				<slot name="optionsRight"></slot>
			</view>
		</view>
	</u-tabbar>
</template>

<script>
import { mapState, } from "vuex"
export default {
	name: "footer-options-box",
	props: {
		showService: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {

		};
	},
	computed: {
		...mapState({
			safeAreaBottom: state => state.app.safeAreaBottom
		})
	},
}
</script>

<style lang="scss">
::v-deep .u-tabbar--fixed {
	z-index: 999 !important;
}

.footer-options-box {
	::v-deep .u-tabbar__content__item-wrapper {
		height: auto !important;
		max-height: 108rpx;
	}

	.options-box {
		box-sizing: border-box;
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100vw;
		padding: 0 30rpx;
	}
}
</style>