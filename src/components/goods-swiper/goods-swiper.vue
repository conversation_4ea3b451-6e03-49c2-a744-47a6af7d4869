<template>
	<view class="goods-swiper-box">
		<u-swiper :list="swiperList" v-bind="$attrs"  v-if='swiperList.length' @click="handleClick"></u-swiper>
		<view v-else>
			<img src="@/static/easy-loadimage/loading.png" mode="aspectFit"
				style="width: 80%;  margin: 0 auto;display: block;" ></img>
		</view>
	</view>
</template>

<script>
export default {
	name: "goods-swiper",
	props: {
		swiperList: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {

		};
	},
	methods: {
		handleClick(index) {
			this.$emit('click', index);
		}
	}
}
</script>

<style lang="scss" scoped>
.goods-swiper-box {
	::v-deep .u-swiper__indicator {
		.u-swiper-indicator__wrapper {
  @include flex-gap(0, 6rpx); // 替换了 column-gap

			.u-swiper-indicator__wrapper__dot {
				margin: 0;
				border-radius: 19rpx;
				width: 12rpx;
				height: 6rpx;
			}

			.u-swiper-indicator__wrapper__dot--active {
				width: 31rpx;
			}
		}
	}
}
</style>