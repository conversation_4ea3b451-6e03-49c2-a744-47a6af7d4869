<template>
    <FooterOptionsBox class="paypal-button-container" :showService="false" style="background: transparent;">
        <template #optionsRight>
            <view class="paypal-button-container">
                <div ref="paypalButtonContainer"></div>
            </view>
        </template>
    </FooterOptionsBox>
</template>

<script>
import { waitForPayPalSDK, isPayPalSDKLoaded } from "@/utils/payment/paypalSDK";
import FooterOptionsBox from "@/components/footer-options-box/footer-options-box.vue";
import paymentSDKManager from "@/utils/payment/paymentSDKManager.js";
export default {
    name: 'PaypalButton',
    components: {
        FooterOptionsBox
    },
    props: {
        // Optional initial payment number
        paymentNumber: {
            type: String,
            default: ''
        },
        // Components to load
        components: {
            type: String,
            default: 'buttons'
        },
        // Funding sources to enable
        enableFunding: {
            type: String,
            default: 'venmo,paylater,card'
        },
        // Button style options
        buttonStyle: {
            type: Object,
            default: () => ({
                layout: 'vertical',
                size: 'large',
                height: 55
            })
        },
        payFetch: {
            type: Function,
            default: () => { }
        },
        // Timeout for payment number request (in ms)
        timeout: {
            type: Number,
            default: 12000
        }
    },
    data() {
        console.log('📊 PaypalButton组件初始化data');
        return {
            paypalSDKLoaded: false,
            currentPaymentNumber: this.paymentNumber,
            paymentNumberResolver: null,
            paymentNumberPromise: null
        }
    },
    async mounted() {
        console.log('🚀 PaypalButton组件开始mounted');
        console.log('🔍 检查window.paypal是否存在:', !!window.paypal);

        try {
            console.log('⏳ 等待PayPal SDK加载完成...');

            if (isPayPalSDKLoaded()) {
                console.log('✅ PayPal SDK已经预加载完成');
                this.paypalSDKLoaded = true;
            } else {
                console.log('🔄 PayPal SDK正在加载中，等待完成...');
                
                // 尝试等待预加载完成，但设置5秒超时
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('等待预加载超时')), 5000);
                });

                try {
                    const paypal = await Promise.race([
                        waitForPayPalSDK(),
                        timeoutPromise
                    ]);
                    console.log('✅ PayPal SDK预加载成功:', paypal);
                    console.log('🔧 PayPal SDK功能检查:', {
                        hasButtons: !!paypal?.Buttons,
                        hasFUNDING: !!paypal?.FUNDING,
                        version: paypal?.version || 'unknown'
                    });
                    this.paypalSDKLoaded = true;
                } catch (timeoutError) {
                    console.warn('⚠️ 预加载等待超时，尝试重新加载 PayPal SDK');
                    // 预加载失败，使用SDK管理器重新加载
                    await paymentSDKManager.ensurePayPalSDK();
                    console.log('✅ PayPal SDK重新加载成功');
                    this.paypalSDKLoaded = true;
                }
            }
        } catch (error) {
            console.error('❌ PayPal SDK加载失败:', error);
            this.paypalSDKLoaded = false;
            return;
        }

        console.log('🎨 开始渲染PayPal按钮...');
        this.renderPayPalButton();
    },
    beforeDestroy() {
        // Component cleanup if needed
    },
    methods: {
        // Initialize the promise for receiving payment number
        initializePaymentPromise() {
            this.paymentNumberPromise = new Promise((resolve) => {
                this.paymentNumberResolver = resolve;
            });
        },


        // Signal to open payment view
        signalOpenView() {
            // Emit event to parent component
            this.$emit('open-view');
        },

        // Set payment number from parent component
        setPaymentNumber(paymentNumber) {
            if (this.paymentNumberResolver) {
                this.currentPaymentNumber = paymentNumber;
                this.paymentNumberResolver(paymentNumber);
            }
        },

        // Render the PayPal button
        renderPayPalButton() {
            console.log('🎨 开始渲染PayPal按钮');
            console.log('🔍 检查window.paypal:', !!window.paypal);
            console.log('📋 按钮样式配置:', this.buttonStyle);
            console.log('📦 容器元素:', this.$refs.paypalButtonContainer);

            if (!window.paypal) {
                console.error('❌ PayPal SDK未加载，无法渲染按钮');
                return;
            }

            try {
                console.log('⚙️ 创建PayPal Buttons配置...');
                const buttonConfig = {
                    style: this.buttonStyle,
                    fundingSource: window.paypal.FUNDING.PAYPAL,

                    createOrder: async (data, actions) => {
                        console.log('💳 createOrder被调用');
                        console.log('📊 createOrder data:', data);
                        try {
                            // 确保PayPal SDK在点击时是可用的
                            console.log('🔍 检查PayPal SDK可用性...');
                            await paymentSDKManager.ensurePayPalSDK();
                            console.log('✅ PayPal SDK确认可用');

                            console.log('📞 调用payFetch获取支付号码...');
                            const payNum = await this.payFetch();
                            console.log('✅ 获取到支付号码:', payNum);

                            // 🚨 修复：特殊处理订单创建失败的情况
                            if (payNum === false) {
                                console.log('🔍 订单创建失败，payFetch返回false，抛出特定错误');
                                // 订单创建失败时，handlePayment已经显示了错误信息
                                // 抛出特定错误，让PayPal不要显示自己的错误信息
                                throw new Error('ORDER_CREATION_FAILED');
                            }

                            if (!payNum) {
                                console.error('❌ 未获取到支付号码');
                                throw new Error('No payment number received');
                            }

                            // Signal to open the view
                            console.log('📱 发送打开视图信号');
                            this.signalOpenView();

                            return payNum;
                        } catch (error) {
                            console.error('❌ createOrder过程中出错:', error);
                            this.$emit('error', error);
                            throw error;
                        }
                    },

                    onApprove: (data, actions) => {
                        console.log('✅ PayPal支付被批准');
                        console.log('📊 onApprove data:', data);
                        this.$emit('success', data);
                    },

                    onCancel: (data) => {
                        console.log('❌ PayPal支付被取消');
                        console.log('📊 onCancel data:', data);
                        this.$emit('cancel', data);
                    },

                    onError: (err) => {
                        console.error('❌ PayPal支付出错:', err);
                        this.$emit('error', err);
                    }
                };

                console.log('🔧 PayPal Buttons配置:', buttonConfig);
                console.log('🎯 开始渲染到容器...');

                // 渲染按钮并在完成后修改样式
                const renderResult = window.paypal.Buttons(buttonConfig).render(this.$refs.paypalButtonContainer);
                if (renderResult && typeof renderResult.then === 'function') {
                    renderResult.then(() => {
                        this.modifyPaypalButtonStyle();
                    });
                } else {
                    // 兜底方案：500ms后尝试修改
                    setTimeout(() => {
                        this.modifyPaypalButtonStyle();
                    }, 500);
                }
                console.log('✅ PayPal按钮渲染成功');

            } catch (error) {
                console.error('❌ 渲染PayPal按钮时出错:', error);
                console.error('🔍 错误详情:', {
                    message: error.message,
                    stack: error.stack,
                    name: error.name
                });
                this.$emit('error', error);
            }
        },
        modifyPaypalButtonStyle() {

        },
    }
}
</script>

<style lang="scss">
#zoid-paypal-buttons-uid_36f057a615_mdu6ndy6mzc {
    width: 50vw;
}

.paypal-buttons {
    width: 50vw;
}

.paypal-button-container {
    width: 100%;

    ::v-deep .options-box {
        justify-content: flex-end;
    }

    ::v-deep .options-right {
        width: 50vw;
    }

    ::v-deep .u-tabbar__content {
        background: transparent;
    }
}

/* Override PayPal popup styles */
:global(.paypal-checkout-close) {
    top: 56px !important;
}

:global(.paypal-overlay-context-popup.paypal-checkout-overlay) {
    height: 50vh !important;
}
</style>
