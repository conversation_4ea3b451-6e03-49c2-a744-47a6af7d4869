<template>
	<view v-if="visible" class="mask-box" @touchmove.stop.prevent>
		<view class="toast">
			<view class="toast-content">
				<image class="icon-img" src="/static/assets/common/add_successfully.png" mode="aspectFit" lazy-load>
				</image>
				<text class="message-text">{{ message }}</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				visible: false,
				message: "Add Successfully",
				duration: 1500
			};
		},
		methods: {
			showToast({
				message = 'Add Successfully',
				duration = 1500
			} = {}) {
				this.visible = true;
				this.message = message;
				this.duration = duration;
				setTimeout(() => {
					this.visible = false;
				}, this.duration);
			}
		}
	};
</script>

<style lang="scss" scoped>
	.mask-box {
		position: fixed;
		top: 0;
		right: 0;
		left: 0;
		bottom: 0;
		z-index: 666;
	}
	.toast {
		position: fixed;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		background-color: rgba(0, 0, 0, 0.7);
		color: #fff;
		padding: 75rpx 30rpx 29rpx;
		border-radius: 31rpx;
		z-index: 666;
		opacity: 1;
		transition: opacity 0.3s, transform 0.3s;
	}

	.toast-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;

		.message-text {
			font-weight: 600;
			font-size: 46rpx;
			color: #FFFFFF;
			margin-top: 25rpx;
			white-space: nowrap;
		}

		.icon-img {
			width: 83rpx;
			height: 83rpx;
		}
	}
</style>