<template>
	<view>
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="statusBarStyle">
		</view>

		<!-- 导航栏 -->
		<view class="navbar" :style="navbarStyle">
			<!-- 左侧按钮 -->
			<view v-if="isLeftShow" class="navbar-left" @click="leftClick">
				<slot name="left">
					<image src="/static/assets/common/return.png" mode="widthFix" class="left-icon">
					</image>
				</slot>
			</view>

			<!-- 中间标题 -->
			<view class="navbar-content" :style="centerStyle">
				<slot name="center">
					<view>{{ title }}</view>
				</slot>
			</view>

			<!-- 右侧按钮 -->
			<view v-if="isRightShow" class="navbar-right" @click="rightClick">
				<slot name="right"></slot>
			</view>
			<view v-else-if="!hiddenCloseApp" class="navbar-right" @click="logoutApp">
				<u-icon name="close" size="28"></u-icon>
			</view>
		</view>

		<!-- 非沉浸式模式的占位空间 -->
		<view v-if="!isImmersion" :style="placeholderStyle" :class="{ 'placeholder-debug': isDebugMode }">
		</view>
	</view>
</template>

<script>
import { mapGetters } from 'vuex';
import { getStatusBarHeight } from '@/utils/bridge/appBridge';

export default {
	name: "navbar",
	props: {
		hiddenCloseApp: {
			type: Boolean,
			default: false
		},
		navBarHeight: {
			type: Number,
			default: 54
		},
		title: {
			type: [String, Number],
			default: 'Shop'
		},
		bgColor: {
			type: String,
			default: '#F0F0F0'
		},
		titleStyle: {
			type: Object,
			default: () => ({
				color: '#262626',
				fontSize: '34rpx',
				fontWeight: 'bold'
			})
		},
		titleWidth: {
			type: String,
			default: '400rpx'
		},
		autoBack: {
			type: Boolean,
			default: false,
		},
		isLeftShow: {
			type: Boolean,
			default: true
		},
		isRightShow: {
			type: Boolean,
			default: false
		},
		isImmersion: {
			type: Boolean,
			default: false
		},
		threshold: {
			type: Number,
			default: 100
		},
		scrollTop: {
			type: Number,
			default: 0
		}
	},

	data() {
		return {
			safeStatusBarHeight: 0
		}
	},

	watch: {
		// 监听 scrollTop 变化，确保透明度及时更新
		scrollTop: {
			handler(newVal, oldVal) {
				// 当 scrollTop 发生变化时，强制重新计算样式
				this.$forceUpdate();
			},
			immediate: true
		}
	},

	computed: {
		...mapGetters(['platForm', 'statusBarHeight']),

		// 当前使用的状态栏高度
		currentStatusBarHeight() {
			return this.statusBarHeight <= 0 ? this.safeStatusBarHeight : this.statusBarHeight;
		},

		// 是否为调试模式
		isDebugMode() {
			return process.env.NODE_ENV === 'development';
		},

		// 背景颜色样式
		backgroundStyle() {
			if (!this.isImmersion) {
				return { backgroundColor: this.bgColor };
			}

			if (this.scrollTop > this.threshold) {
				return { backgroundColor: this.bgColor };
			}

			// 沉浸式模式下的渐变透明度计算
			const opacity = this.calculateOpacity();
			const rgba = this.hexToRgba(this.bgColor, opacity);

			return { backgroundColor: rgba };
		},

		// 状态栏样式
		statusBarStyle() {
			return {
				height: `${this.currentStatusBarHeight}px`,
				...this.backgroundStyle
			};
		},

		// 导航栏样式
		navbarStyle() {
			return {
				...this.backgroundStyle,
				top: `${this.currentStatusBarHeight}px`
			};
		},

		// 中间内容样式
		centerStyle() {
			return {
				width: this.titleWidth,
				...this.titleStyle
			};
		},

		// 占位空间样式
		placeholderStyle() {
			// 将 rpx 转换为 px（1rpx ≈ 0.5px 在标准屏幕上）
			const navBarHeightInPx = uni.upx2px(108); // 导航栏实际高度 108rpx
			const totalHeight = this.currentStatusBarHeight + navBarHeightInPx;
			return {
				height: `${totalHeight}px`
			};
		}
	},

	mounted() {
		this.initStatusBarHeight();
	},

	activated() {
		this.initStatusBarHeight();
	},

	methods: {
		// 计算透明度
		calculateOpacity() {
			const safeScrollTop = Math.max(0, this.scrollTop);
			const safeThreshold = Math.max(1, this.threshold);
			return Math.min(1, safeScrollTop / safeThreshold);
		},

		// 将十六进制颜色转换为 RGBA
		hexToRgba(hex, opacity = 1) {
			const cleanHex = hex.replace('#', '');
			const r = parseInt(cleanHex.substring(0, 2), 16);
			const g = parseInt(cleanHex.substring(2, 4), 16);
			const b = parseInt(cleanHex.substring(4, 6), 16);
			return `rgba(${r}, ${g}, ${b}, ${opacity})`;
		},

		// 初始化状态栏高度
		initStatusBarHeight() {
			this.$nextTick(async () => {
				if (this.statusBarHeight <= 0) {
					let height = 0;
					try {
						height = await getStatusBarHeight();
						console.log('app获取到状态栏高度:', height);
					} catch (error) {
						console.error('获取失败:', error);
					}
					setTimeout(() => {
						const sysInfo = uni.getWindowInfo();
						console.log("🚀 ~ file: navbar.vue:159 ~ sysInfo:", sysInfo)

						if (height == 0) {
							height = sysInfo.statusBarHeight || 0;
						}
						this.$store.commit('SET_STATUSBARHEIGHT', height);
					}, 100);
				}

			});
		},

		// 左侧按钮点击
		leftClick() {
			console.log('🔄 navbar leftClick 被点击');
			console.log('📋 autoBack 参数:', this.autoBack);

			if (this.autoBack) {
				// 获取当前页面信息
				const pages = getCurrentPages();
				const currentPage = pages[pages.length - 1];
				const currentRoute = currentPage.route;

				console.log('📄 当前页面信息:');
				console.log('  - pages长度:', pages.length);
				console.log('  - currentRoute:', currentRoute);
				console.log('  - currentPage:', currentPage);

				// 判断是否为tabBar页面
				const isTabBarPage = this.isCurrentPageTabBar(currentRoute);
				console.log('📊 isTabBarPage:', isTabBarPage);

				if (!isTabBarPage) {
					console.log('🚀 执行 uni.navigateBack');
					// 非tabBar页面，尝试使用uni.navigateBack
					if (pages.length == 1) {
						this.$tab.switchTab('/pages/tabs/index/index');
					} else {
						uni.navigateBack({
							success: () => {
								console.log('✅ uni.navigateBack 返回成功');
							},
							fail: (error) => {
								// uni.back失败，返回首页
								console.log('❌ uni.navigateBack 返回失败:', error);
								console.log('🏠 准备跳转到首页');
								this.$tab.switchTab('/pages/tabs/index/index');
							}
						});
					}

				} else {
					console.log('🌐 tabBar页面，执行 window.history.back()');
					// tabBar页面使用原逻辑
					window.history.back();
				}
			} else {
				console.log('📤 emit leftClick 事件');
				this.$emit('leftClick');
			}
		},

		// 判断当前页面是否为tabBar页面
		isCurrentPageTabBar(route) {
			const tabBarPages = [
				'pages/tabs/index/index',
				'pages/tabs/sort/sort',
				'pages/tabs/cart/cart',
				'pages/tabs/mine/mine'
			];
			const isTabBar = tabBarPages.includes(route);
			console.log('🔍 检查tabBar页面:', {
				route: route,
				tabBarPages: tabBarPages,
				isTabBar: isTabBar
			});
			return isTabBar;
		},

		// 右侧按钮点击
		rightClick() {
			this.$emit('rightClick');
		},

		// 退出应用
		logoutApp() {
			this.$appBridge('exitStore');
		}
	}
}
</script>

<style lang="scss" scoped>
.status-bar {
	width: 100%;
	position: fixed;
	top: 0;
	left: 0;
	z-index: 9999;
}

.navbar {
	width: 100%;
	position: fixed;
	/* 使用 CSS 变量 */
	left: 0;
	z-index: 9999;
	display: flex;
	justify-content: center;
	align-items: center;
	box-sizing: border-box;
	height: 108rpx;

	.navbar-left {
		padding: 0 13px;
		display: flex;
		align-items: center;
		position: absolute;
		left: 0;
		top: 0;
		bottom: 0;

		.left-icon {
			width: 46rpx;
			height: 46rpx;
		}
	}

	.navbar-content {
		text-align: center;
		z-index: 10000;
		width: 100%;
	}

	.navbar-right {
		padding: 0 13px;
		position: absolute;
		right: 0;
		top: 0;
		bottom: 0;
		display: flex;
		flex-direction: row;
		align-items: center;
	}
}
</style>