<template>
	<view>
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px', ...bgColors }"></view>
		<!-- 导航栏 -->
		<view class="navbar" :style="{ height: navBarHeight + 'px', ...bgColors, top: statusBarHeight + 'px' }">
			<view v-if="isLeftShow" class="navbar-left" @click="leftClick">
				<slot name="left">
					<image src="/static/assets/common/return.png" mode="aspectFit"></image>
				</slot>
			</view>
			<view class="navbar-content" :style="{ width: titleWidth, ...titleStyle  }">
				<slot name="center">
					<view>{{ title }}</view>
				</slot>
			</view>
			<view v-if="isRightShow" class="navbar-right" @click="rightClick">
				<slot name="right">
				</slot>
			</view>
		</view>

		<view v-if="!isImmersion" :style="{ height: statusBarHeight+navBarHeight  + 'px' }"></view>
	</view>
</template>

<script>
	import {
		mapGetters
	} from 'vuex';

	export default {
		name: "navbar",
		props: {
			navBarHeight: {
				type: Number,
				default: 44
			},
			title: {
				type: [String, Number],
				default: 'Shop'
			},
			bgColor: {
				type: String,
				default: '#F0F0F0'
			},
			titleStyle: {
				type: Object,
				default: () => {
					return {
						color: '#262626',
						fontSize: '34rpx',
						fontWeight: 'bold'
					}
				}
			},
			titleWidth: {
				type: String,
				default: '400rpx'
			},
			autoBack: {
				type: Boolean,
				default: false,
			},
			isLeftShow: {
				type: Boolean,
				default: true
			},
			isRightShow: {
				type: Boolean,
				default: false
			},
			isImmersion: {
				type: Boolean,
				default: false
			},
			threshold: {
				type: Number,
				default: 100
			},
			scrollTop: {
				type: Number,
				default: 0
			}
		},
		computed: {
			...mapGetters(['statusBarHeight']),
			bgColors() {
				if (this.isImmersion) {
					if (this.scrollTop <= this.threshold) {
						const opacity = this.scrollTop / this.threshold;
						const hex = this.bgColor.replace('#', '');
						const r = parseInt(hex.substring(0, 2), 16);
						const g = parseInt(hex.substring(2, 4), 16);
						const b = parseInt(hex.substring(4, 6), 16);

						return {
							backgroundColor: `rgba(${r}, ${g}, ${b}, ${opacity})`
						};
					} else {
						return {
							backgroundColor: this.bgColor,
						}
					}
				} else {
					return {
						backgroundColor: this.bgColor,
					}
				}
			}
		},
		methods: {
			leftClick() {
				if (this.autoBack) {
					uni.navigateBack()
				} else {
					this.$emit('leftClick');
				}
			},
			rightClick() {
				this.$emit('rightClick');
			},
		}
	}
</script>

<style lang="scss" scoped>
	.status-bar {
		width: 100%;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 999;
	}

	.navbar {
		width: 100%;
		position: fixed;
		/* 使用 CSS 变量 */
		left: 0;
		z-index: 999;
		display: flex;
		justify-content: center;
		align-items: center;
		box-sizing: border-box;

		.navbar-left {
			padding: 0 13px;
			display: flex;
			align-items: center;
			position: absolute;
			left: 0;
			top: 0;
			bottom: 0;

			image {
				width: 24px;
				height: 24px;
			}
		}

		.navbar-content {
			text-align: center;
		}

		.navbar-right {
			padding: 0 13px;
			position: absolute;
			right: 0;
			top: 0;
			bottom: 0;
			display: flex;
			flex-direction: row;
			align-items: center;
		}
	}
</style>