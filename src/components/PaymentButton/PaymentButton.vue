<template>
	<!-- <u-tabbar :fixed="true" :placeholder="true" :safeAreaInsetBottom="true" class="payment-box">
		<view class="content-box">
			<view class="payment-button">
				<view class="amount">{{ priceSymbol }}{{ Price }}</view>
				<u-button class="payment-btn" :text="subText" shape="circle" :loading="isPaymentLoading"
					@click="$emit('handlePayment')"></u-button>
			</view>
		</view>
	</u-tabbar> -->
	<u-tabbar :fixed="true" :placeholder="true" :safeAreaInsetBottom="true" class="payment-box">
		<div class="pd-2">
			<view class="payment-button">
				<view class="amount" @click="$emit('handleAddToCart')">
					<text>{{ priceSymbol }}{{ Price }}</text>
				</view>
				<view class="payment-btn" :loading="isPaymentLoading" @click="$emit('handlePayment')">{{ subText }}
				</view>
			</view>
		</div>
	</u-tabbar>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
	name: "PaymentButton",
	props: {
		Price: {
			type: [String, Number],
			default: "0.00"
		},
		isPaymentLoading: {
			type: Boolean,
			default: false
		},
		subText: {
			type: String,
			default: "Payment"
		}
	},
	data() {
		return {

		};
	},
	computed: {
		...mapGetters(['priceSymbol', 'safeAreaBottom'])
	},
	methods: {

	}
}
</script>

<style lang="scss">
.payment-box {
	height: 108rpx;

	::v-deep .u-tabbar__content__item-wrapper {
		height: auto;
	}
}

.pd-2 {
	padding: 13rpx 0;
	margin: auto;
}

.payment-button {
	position: relative;
	width: 688rpx;
	height: 85rpx;
	max-width: 688rpx;
	display: flex;
	align-items: center;
	background: #FF5A1E;
	border-radius: 69rpx 69rpx 69rpx 69rpx;
	overflow: hidden;
	margin: auto;

	&::before {
		position: absolute;
		top: 0;
		left: 0;
		border: 2rpx solid #FF5A1E;
		width: 100%;
		height: 100%;
		box-sizing: border-box;
		content: '';
		border-radius: 69rpx 69rpx 69rpx 69rpx;
		pointer-events: none;

	}

	.amount {
		width: 312rpx;
		height: 85rpx;
		background: #FBDED1;
		border-radius: 0 88rpx 4rpx 0rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-weight: bold;
		font-size: 38rpx;
		color: #FF5A1E;

		img {
			width: 37rpx;
			height: 37rpx;
			vertical-align: middle;
		}

		.uicon-plus {
			margin-right: 5rpx;
		}

	}

	.payment-btn {
		background: transparent;
		border: none;
		font-weight: 600;
		font-size: 38rpx;
		color: #FFFFFF;

		height: 85rpx;
		border-radius: 69rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		flex: 1;
	}
}
</style>