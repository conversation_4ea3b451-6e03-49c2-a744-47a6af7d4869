<template>
	<view class="app-init-wrapper">
		<!-- 初始化加载状态 -->
		<view v-if="showLoading" class="init-loading">
			<view class="loading-content">
				<view class="loading-logo">
					<!-- 可以放置你的logo图片 -->
					<text class="logo-text">🚗</text>
				</view>
				<view class="loading-text">Loading...</view>
				<view class="loading-spinner">
					<view class="spinner"></view>
				</view>
			</view>
		</view>

		<!-- 初始化完成后显示页面内容 -->
		<view v-else class="page-content">
			<slot></slot>
		</view>
	</view>
</template>

<script>
import { getToken } from '@/utils/security/auth';

export default {
	name: 'AppInitWrapper',
	props: {
		// 是否需要等待token初始化
		waitForToken: {
			type: Boolean,
			default: true
		},
		// 超时时间（毫秒）
		timeout: {
			type: Number,
			default: 5000
		}
	},
	data() {
		return {
			showLoading: true
		}
	},
	created() {
		this.checkInitialization();
	},
	activated() {
		this.validateOnShow();
	},
	methods: {
		async validateOnShow() {
			console.log('validateOnShow');
			if (!this.waitForToken) {
				this.showLoading = false;
				return;
			}
			try {
				await this.waitForTokenReady();
				this.showLoading = false;
				// 触发初始化完成事件
				this.$emit('show-complete');
			} catch (error) {
				console.error('初始化等待超时:', error);
				this.showLoading = false;
				this.$emit('init-timeout');
			}
		},
		async checkInitialization() {
			if (!this.waitForToken) {
				this.showLoading = false;
				return;
			}

			try {
				await this.waitForTokenReady();
				this.showLoading = false;
				// 触发初始化完成事件
				this.$emit('init-complete');
			} catch (error) {
				console.error('初始化等待超时:', error);
				this.showLoading = false;
				this.$emit('init-timeout');
			}
		},

		waitForTokenReady() {
			return new Promise((resolve, reject) => {
				// 设置超时
				const timeoutId = setTimeout(() => {
					reject(new Error('Token initialization timeout'));
				}, this.timeout);

				const checkToken = () => {
					const token = getToken();
					if (token) {
						clearTimeout(timeoutId);
						console.log('Token已准备就绪:', token);
						resolve();
					} else {
						// 每100ms检查一次
						setTimeout(checkToken, 100);
					}
				};

				checkToken();
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.app-init-wrapper {
	width: 100%;
	height: 100vh;
}

.init-loading {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 100vh;
	background-color: #F0F0F0;

	.loading-content {
		display: flex;
		flex-direction: column;
		align-items: center;

		.loading-logo {
			margin-bottom: 40rpx;

			.logo-text {
				font-size: 80rpx;
			}
		}

		.loading-text {
			font-size: 32rpx;
			color: #666;
			margin-bottom: 40rpx;
		}

		.loading-spinner {
			.spinner {
				width: 60rpx;
				height: 60rpx;
				border: 4rpx solid #f3f3f3;
				border-top: 4rpx solid #FF5A1E;
				border-radius: 50%;
				animation: spin 1s linear infinite;
			}
		}
	}
}

.page-content {
	width: 100%;
	height: 100%;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}
</style>