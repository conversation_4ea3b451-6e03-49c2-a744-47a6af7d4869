<template>
	<view class="collapsible-container" :style="{ width: boxW }">
		<view class="sku-left-box" ref="contentBox" :class="{ 'collapsed': !isOpen }" @click.stop="toggleExpand">
			<slot name="content"></slot>
		</view>

		<u-icon :name="expandIcon" size="8px" bold color="#8C8C8C" v-if='shouldShowArrow' @click.stop="toggleExpand"></u-icon>
	</view>
</template>

<script>
	export default {
		name: 'CollapsibleComponent',
		props: {
			boxW: {
				type: String,
				default: '266rpx'
			},
			maxHeight: {
				type: String,
				default: 'fit-content'
			},
			showArrow: {
				type: Boolean,
				default: true
			},
			itemCount: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				isOpen: false
			}
		},
		computed: {
			expandIcon() {
				return this.isOpen ? 'arrow-up' : 'arrow-down';
			},
			shouldShowArrow() {
				return this.showArrow && this.hasMultipleItems;
			},
			hasMultipleItems() {
				return this.itemCount > 1;
			}
		},
		methods: {
			toggleExpand() {
				if (this.hasMultipleItems) {
					this.isOpen = !this.isOpen;
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.collapsible-container {
		display: inline-flex;
		justify-content: space-between;
		align-items: flex-start;
		@include flex-gap(0, 20rpx); // 替换了 column-gap
		padding: 4rpx 15rpx;
		background: #F0F0F0;
		border-radius: 12rpx;
		box-sizing: border-box;
		color: #8C8C8C;
		
		.sku-left-box {
			flex: 1;
			overflow: hidden;
			transition: all 0.3s ease;
			
			&.collapsed {
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 1;
				overflow: hidden;
				text-overflow: ellipsis;
				line-height: 27rpx;
				max-height: 27rpx;
			}
			
			&:not(.collapsed) {
				display: block;
				max-height: none;
			}
		}
		
		.u-icon {
			margin-top: 4rpx;
			flex-shrink: 0;
		}
	}
</style>