<template>
	<view>
		<u-button v-bind="$attrs" :style="defaultStyle" class="confirm-btn" :text="text" :loading="loading" :color="bgColor"
			@click="$emit('handleConfirm')"></u-button>
	</view>
</template>

<script>
	export default {
		name: "SubmitButton",
		props: {
			text: {
				type: String,
				default: 'Confirm'
			},
			loading: {
				type: Boolean,
				default: false
			},
			bgColor: {
				type: String,
				default: '#FF5A1E'
			},
			btnW: {
				type: String,
				default: '688rpx'
			},
			btnH: {
				type: String,
				default: '112rpx'
			},
			borderRadius: {
				type: String,
				default: '30rpx'
			},
			fontColor: {
				type: String,
				default: '#FFFFFF'
			}
		},
		computed: {
			defaultStyle() {
				return {
					width: this.btnW,
					height: this.btnH,
					borderRadius: this.borderRadius,
					color: this.fontColor
				}
			}
		},
	}
</script>

<style lang="scss" scoped>
	.confirm-btn {
		::v-deep .u-button__text {
			font-weight: 500;
			font-size: 35rpx !important;
		}
	}
</style>