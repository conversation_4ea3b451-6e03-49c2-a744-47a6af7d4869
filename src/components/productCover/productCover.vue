<template>
	<view :class="['product-cover', { 'product-cover--ad': product.isAd }]">
		<template v-if="product.isAd">
			<image mode="widthFix" :src="product[imgNameData]" style="width:100%" @click="handleToRouter(product)">
			</image>
			<!-- <easy-loadimage mode="widthFix" :scroll-top="scrollTop" loading-mode="skeleton-1" class="ad-image-width"
				:image-src="product[imgNameData]" style="border-radius: 32rpx;overflow: hidden;"></easy-loadimage> -->
		</template>
		<template v-else>
			<image class="favorite_icon" :src="favoriteImg(product.isFavorite)" @click.stop="onAddFavorite(product)">
			</image>
			<easy-loadimage mode="aspectFit" :scroll-top="scrollTop" loading-mode="skeleton-1"
				:image-src="product[imgNameData]"></easy-loadimage>
			<view class="product-info">
				<view class="product-title">
					<u--text :text="product[titleName]" size="28rpx" color="#262626" bold :lines="2" margin="0 0 7rpx"
						lineHeight="37rpx" style="font-family: PingFang SC-Bold!important;"></u--text>
				</view>
				<view class="discount-price-box" v-if="product.tipType === 1 && product.tip">
					<image class="sale_icon" src="@/static/assets/home/<USER>"></image>
					<u--text :text="product.tip" lines="1" color="#FF5A1E" size="26rpx" margin="0 4px 0 4px"></u--text>
				</view>

				<view class="price-footer-box">
					<view class="flex">
						<u--text :text="`${priceSymbol}${product.Price}`" size="26rpx" color="#FF5A1E" bold
							margin="0 10rpx 0 0" style="font-family: PingFang SC-Bold!important;"></u--text>

					</view>
					<view> <u--text v-show="product.MarketPrice !== '0.00'"
							:text="`${priceSymbol}${product.MarketPrice}`" size="26rpx" color="#999999" bold
							decoration="line-through" style="font-family: PingFang SC-Medium!important;"></u--text>
					</view>

					<image class="addCart" src="@/static/assets/home/<USER>" @click.stop="onAddCart(product)">
					</image>
				</view>
			</view>
		</template>
	</view>
</template>

<script>

import {
	mapGetters
} from "vuex";

export default {
	name: "productCover",
	props: {
		product: {
			type: Object,
			default: () => ({})
		},
		width: {
			type: String,
			default: '100%'
		},
		height: {
			type: String,
			default: '140px'
		},
		imgName: {
			type: String,
			default: 'PicPath_0'
		},
		titleName: {
			type: String,
			default: 'Name_en'
		},
		scrollTop: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {

		};
	},
	computed: {
		...mapGetters(["priceSymbol"]),
		favoriteImg() {
			return isFavorite => {
				return require(`@/static/assets/home/<USER>'favorite' : 'favorite_no'}.png`);
			}
		},
		imgNameData() {
			return this.imgName;
		}
	},
	methods: {
		onAddFavorite({
			ProId,
			isFavorite
		}) {
			this.$emit("addFavoriteFn", {
				ProId,
				isFavorite
			})
		},

		onAddCart(item) {
			if (item.tipType === 0) { // 0代表无库存
				return this.$toast({
					title: item.tip
				})
			}

			this.$emit("onAddCart", item)
		},
		handleToRouter(item) {
			if (item.product_url || item.path_app_ur1) {
				this.$tab.navigateTo(item.product_url || item.path_app_ur1)
			}
		}
	},
}
</script>

<style lang="scss" scoped>
.product-cover {
	position: relative;
	background: #FFFFFF;
	box-shadow: 0px 36rpx 91rpx 0px rgba(167, 167, 167, 0.2);
	border-radius: 32rpx;
	// padding: 46rpx 25rpx 28rpx;
	box-sizing: border-box;
	overflow: hidden;

	&.product-cover--ad {
		padding: 0;
		overflow: hidden;

		&::v-deep uni-image {
			vertical-align: middle;
		}
	}

	.favorite_icon {
		position: absolute;
		top: 12rpx;
		right: 12rpx;
		z-index: 10;
		width: 70rpx !important;
		height: 70rpx;
	}

	.easy-loadimage {
		width: 100%;
		height: 280rpx;

		&.ad-image-width {
			height: auto;
			width: 100%;
			border-radius: 32rpx;
			overflow: hidden;

			::v-deep .origin-img {
				max-height: inherit !important;
			}
		}
	}

	.product-info {
		padding: 25rpx;
	}

	.discount-price-box {
		display: inline-flex;
		align-items: center;
		max-width: 100%;
		height: 33rpx;
		border-radius: 8rpx;
		border: 2rpx solid #FF5A1E;
		box-sizing: border-box;
		overflow: hidden;

		.sale_icon {
			width: 30rpx;
			height: 30rpx;
			background: #FF5A1E;
			border-radius: 4rpx;
		}
	}

	.price-footer-box {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: flex-start;
		position: relative;

		.addCart {
			position: absolute;
			bottom: 0;
			right: 0;
			width: 35rpx;
			min-width: 35rpx;
			height: 35rpx;
		}
	}
}
</style>