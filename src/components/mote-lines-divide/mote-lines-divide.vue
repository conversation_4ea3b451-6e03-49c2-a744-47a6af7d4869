<template>
	<view>
		<view style="position: relative" v-if="isHide">
			<view class="dt-content" :style="'-webkit-line-clamp:' + line">
				<view class="content">
					<slot>{{ dt ? dt : '' }}</slot>
				</view>
			</view>

			<view class="button-show" @tap.stop="isHide = false" v-if="enableButton && lines > line">
				<view class="expand-text" :style="{ color: expandTextColor, fontWeight: 'bold' }">{{ expandText }}
				</view>
			</view>
		</view>
		<view v-else>
			<view>
				<view class="content">
					<slot>{{ dt ? dt : '' }}</slot>
				</view>
			</view>
			<view class="fold-hint" v-if="foldHint">
				<view class="fold-text" @tap.stop="isHide = true" :style="{ color: foldHintColor, fontWeight: 'bold' }">
					{{ foldHint }}</view>
			</view>
		</view>
		<view class="placeholder" v-if="placeholder">
			{{ placeholder }}
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			// 是否隐藏多余行。初始状态不隐藏
			isHide: true,
			// 全量所占文本高度
			textHeight: 0,
			// 单行文本所占高度
			lineHeight: 1,
			// 占位文本
			placeholder: '占位'
		};
	},
	props: {
		// 展示多少行
		line: {
			type: [Number, String],
			default: 1
		},
		// 文本
		dt: {
			type: [String],
			default: ''
		},
		enableButton: {
			type: Boolean,
			default: true
		},
		// 自定义展开提示
		expandText: {
			type: String,
			default: "展开"
		},
		// 自定义收起提示
		foldHint: {
			type: String,
			default: "收起"
		},
		expandTextColor: {
			type: String,
			default: "#FF5A1E"
		},
		foldHintColor: {
			type: String,
			default: "#FF5A1E"
		},
	},
	watch: {
		dt() {
			let that = this
			setTimeout(() => {
				that.calculateHeight();
			}, 150) // 增加延迟确保DOM更新完成
		}
	},

	mounted() {
		if (this.enableButton) {
			// 延迟执行，确保DOM完全渲染
			setTimeout(() => {
				this.calculateHeight();
			}, 200);
		}
	},
	methods: {
		// 计算文本高度的方法
		calculateHeight() {
			this.$nextTick(() => {
				// 使用$nextTick确保DOM更新完成
				let query = uni.createSelectorQuery().in(this);

				// 获取所有元素信息
				query.selectAll('.content, .content *, .dt-content, .dt-content *').boundingClientRect(allData => {
					// 找到有实际高度的元素
					let maxHeight = 0;

					if (allData && allData.length > 0) {
						allData.forEach((item) => {
							if (item && item.height > maxHeight) {
								maxHeight = item.height;
							}
						});
					}

					if (maxHeight > 0) {
						this.textHeight = maxHeight;
					} else {
						this.estimateTextHeight();
					}

					this.calculateLineHeight();
				}).exec();
			});
		},



		// 计算单行高度
		calculateLineHeight() {
			// 先设置占位文本
			this.placeholder = 'A';

			this.$nextTick(() => {
				let query = uni.createSelectorQuery().in(this);

				query.select('.placeholder').boundingClientRect(data => {
					if (data && data.height > 0) {
						this.lineHeight = data.height;
					} else {
						// 使用CSS计算的行高
						this.lineHeight = 22; // 根据实际字体大小调整
					}

					// 计算完成后清除占位文本
					setTimeout(() => {
						this.placeholder = '';
					}, 100);
				}).exec();
			});
		},

		// 估算文本高度（备用方法）
		estimateTextHeight() {
			if (this.dt && typeof this.dt === 'string') {
				// 简单估算：假设每行约50个字符，每行高度22px
				const estimatedLines = Math.ceil(this.dt.length / 50);
				this.textHeight = estimatedLines * 22;
				this.lineHeight = 22;
			}
		}


	},

	computed: {
		// 全文本所占总行数
		lines() {
			if (!this.enableButton) {
				return this.line
			}
			return Math.floor(this.textHeight > 0 && this.lineHeight > 0 ? this.textHeight / this.lineHeight : 0);
		}
	}
}
</script>

<style scoped>
.content {
	/* 确保view元素能够正确计算文本高度 */
	display: block;
	margin: 0;
	padding: 0;
	line-height: 1.4;
	word-wrap: break-word;
	word-break: break-all;
	font-size: 14px;
	/* 确保文本内容能够正确换行和计算高度 */
	white-space: pre-wrap;
	/* 确保容器能够包含内部文本的高度 */
	min-height: 1em;
	overflow: visible;
}

.dt-content {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	position: relative;
	padding-right: 40px;
}

.button-show {
	width: fit-content;
	position: absolute;
	right: 0;
	bottom: 0;
	z-index: 0;
	text-align: right;
	background-image: linear-gradient(-180deg, rgba(233, 236, 239, 0) 50%, #FFF 80%);
	padding-top: 2rem;
	font-size: 14px;
}

.fold-hint {
	color: blue;
	text-align: right;
	font-size: 14px;
}

.placeholder {
	/* 占位元素样式，用于计算单行高度 */
	font-size: 14px;
	line-height: 1.4;
	opacity: 0;
	position: absolute;
	top: 0;
	left: 0;
	z-index: -1;
	pointer-events: none;
	white-space: nowrap;
}

.expand-text,
.fold-text {
	/* 展开和收起按钮文本样式 */
	display: inline-block;
	font-size: 14px;
	cursor: pointer;
}
</style>