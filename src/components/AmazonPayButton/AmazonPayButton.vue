<template>
	<view style="height:0px;">
		<FooterOptionsBox class="amazon-pay-button-container" :showService="false" style="background: transparent;">
			<template #optionsRight>
				<view class="amazon-button-wrapper">
					<div :id="buttonId" class="amazon-pay-button" ref="amazonButton"
						:style="{ opacity: $config.payment.btnOpacity }"></div>
				</view>
			</template>
		</FooterOptionsBox>
	</view>
</template>

<script>
import {
	mapState
} from 'vuex'
import FooterOptionsBox from '@/components/footer-options-box/footer-options-box.vue'
import { waitForAmazonPaySDK, isAmazonPaySDKLoaded } from '@/utils/payment/amazonPaySDK.js'
import paymentSDKManager from '@/utils/payment/paymentSDKManager.js'

export default {
	name: 'AmazonPayButton',
	components: {
		FooterOptionsBox
	},
	props: {
		// 支付获取函数（类似PayPal的payFetch）
		payFetch: {
			type: Function,
			required: true
		}
	},
	data() {
		return {
			amazonSDKLoaded: false,
			isLoading: false,
			errorMessage: '',
			loadingText: '正在加载Amazon Pay...',
			buttonId: 'AmazonPayButton_' + Math.random().toString(36).substr(2, 9),
			currentAmazonPayData: null
		};
	},
	computed: {
		...mapState('paymentStore', {
			amazonPayConfig: state => state?.amazonPayConfig
		})
	},
	async mounted() {
		console.log('🚀 AmazonPayButton组件开始mounted');
		console.log('🔍 检查window.amazon是否存在:', !!window.amazon);

		console.log('📋 Amazon Pay配置:', this.amazonPayConfig);
		console.log('🔍 配置详细信息:', {
			merchantId: this.amazonPayConfig?.merchantId,
			publicKeyId: this.amazonPayConfig?.publicKeyId,
			sdkSrc: this.amazonPayConfig?.sdkSrc,
			sandbox: this.amazonPayConfig?.sandbox
		});
		console.log('🏪 整个paymentStore状态:', this.$store.state.paymentStore);

		try {
			// 等待预加载的SDK，但设置超时机制
			console.log('⏳ 等待Amazon Pay SDK加载完成...');

			if (isAmazonPaySDKLoaded()) {
				console.log('✅ Amazon Pay SDK已经预加载完成');
				this.amazonSDKLoaded = true;
			} else {
				console.log('🔄 Amazon Pay SDK正在加载中，等待完成...');
				
				// 尝试等待预加载完成，但设置5秒超时
				const timeoutPromise = new Promise((_, reject) => {
					setTimeout(() => reject(new Error('等待预加载超时')), 5000);
				});

				try {
					const amazonSDK = await Promise.race([
						waitForAmazonPaySDK(),
						timeoutPromise
					]);
					console.log('✅ Amazon Pay SDK预加载成功:', amazonSDK);
					this.amazonSDKLoaded = true;
				} catch (timeoutError) {
					console.warn('⚠️ 预加载等待超时，尝试重新加载 Amazon Pay SDK');
					// 预加载失败，使用SDK管理器重新加载
					await paymentSDKManager.ensureAmazonPaySDK();
					console.log('✅ Amazon Pay SDK重新加载成功');
					this.amazonSDKLoaded = true;
				}
			}
		} catch (error) {
			console.error('❌ Amazon Pay SDK加载失败:', error);
			this.amazonSDKLoaded = false;
			return;
		}

		console.log('🎨 开始渲染Amazon Pay按钮...');
		this.renderAmazonPayButton();
	},

	methods: {

		// 渲染Amazon Pay按钮
		renderAmazonPayButton() {
			console.log('🎨 开始渲染Amazon Pay按钮');
			console.log('🔍 检查window.amazon:', !!window.amazon);
			console.log('📦 容器元素:', this.$refs.amazonButton);

			if (!window.amazon || !window.amazon.Pay) {
				console.error('❌ Amazon Pay SDK未加载，无法渲染按钮');
				return;
			}

			this.$nextTick(() => {
				setTimeout(() => {
					try {
						console.log('⚙️ 创建Amazon Pay按钮配置...');

						// 使用ref获取DOM元素
						const buttonElement = this.$refs.amazonButton;
						if (!buttonElement) {
							throw new Error(`找不到按钮元素: ${this.buttonId}`);
						}

						// 清除之前的按钮内容
						buttonElement.innerHTML = '';

						console.log('🔧 Amazon Pay按钮配置:');
						console.log('- Button ID:', this.buttonId);
						console.log('- Merchant ID:', this.amazonPayConfig?.merchantId);
						console.log('- Public Key ID:', this.amazonPayConfig?.publicKeyId);

						// 直接渲染真实的Amazon Pay按钮
						this.renderRealAmazonPayButton();

						console.log('✅ Amazon Pay按钮渲染成功');

					} catch (error) {
						console.error('❌ 渲染Amazon Pay按钮时出错:', error);
						this.errorMessage = `支付按钮初始化失败: ${error.message}`;
						this.$emit('error', error);
					}
				}, 100);
			});
		},



		// 渲染真实的Amazon Pay按钮
		async renderRealAmazonPayButton(amazonPayData = null) {
			const buttonElement = this.$refs.amazonButton;
			if (!buttonElement) return;

			// 清空容器
			buttonElement.innerHTML = '';

			try {
				// 如果没有传入数据，且有paymentData prop，使用它
				if (!amazonPayData && this.paymentData) {
					amazonPayData = this.paymentData;
				}

				// 如果没有数据，使用PC端的两步法：先创建按钮，点击时获取数据
				if (!amazonPayData) {
					console.log('📋 没有支付数据，使用PC端模式创建Amazon Pay按钮');

					// 第一步：创建Amazon Pay按钮（不带createCheckoutSessionConfig）
					const amazonPayButton = amazon.Pay.renderButton(`#${this.buttonId}`, {
						merchantId: this.amazonPayConfig?.merchantId,
						publicKeyId: this.amazonPayConfig?.publicKeyId,
						ledgerCurrency: 'USD',
						checkoutLanguage: 'en_US',
						productType: 'PayAndShip',
						placement: 'Checkout',
						buttonColor: 'Gold'
						// 注意：不包含 createCheckoutSessionConfig
					});

					console.log('✅ Amazon Pay按钮创建成功:', amazonPayButton);

					// 强制修改按钮样式
					setTimeout(() => {
						this.forceButtonHeight();
					}, 500);

					setTimeout(() => {
						this.forceButtonHeight();
					}, 1000);

					// 第二步：添加点击事件处理
					amazonPayButton.onClick(async () => {
						try {
							console.log('💳 Amazon Pay按钮被点击，开始获取支付数据');

							// 确保Amazon Pay SDK在点击时是可用的
							console.log('🔍 检查Amazon Pay SDK可用性...');
							await paymentSDKManager.ensureAmazonPaySDK();
							console.log('✅ Amazon Pay SDK确认可用');

							// 发送打开视图信号
							console.log('📱 发送打开视图信号');
							this.$emit('open-view');

							// 调用父组件传入的payFetch函数获取支付数据
							console.log('📞 调用payFetch获取Amazon Pay数据...');
							const payData = await this.payFetch();
							console.log('✅ 获取到Amazon Pay数据:', payData);

							// 🚨 修复：特殊处理订单创建失败的情况
							if (payData === false) {
								console.log('🔍 订单创建失败，payFetch返回false，直接返回不处理');
								// 订单创建失败时，handlePayment已经显示了错误信息，这里直接返回
								return;
							}

							// 🚨 修复：更详细的数据验证和错误处理
							console.log('🔍 payData详细检查:', {
								hasPayData: !!payData,
								payDataType: typeof payData,
								hasPayload: !!(payData && payData.payload),
								payDataKeys: payData ? Object.keys(payData) : 'N/A'
							});

							if (!payData) {
								console.error('❌ 未获取到Amazon Pay数据 - payData为空');
								throw new Error('No Amazon Pay data received - payData is null/undefined');
							}
							
							if (!payData.payload) {
								console.error('❌ Amazon Pay数据中缺少payload字段');
								console.log('🔍 收到的数据结构:', payData);
								throw new Error('No payload in Amazon Pay data - received: ' + JSON.stringify(payData));
							}
							console.log('🔐 签名:', JSON.stringify({
								payloadJSON: payData.payload,
								signature: payData.signature,
								publicKeyId: this.amazonPayConfig?.publicKeyId
							}));

							// 第三步：使用获取到的数据初始化checkout（类似PC端）
							amazonPayButton.initCheckout({
								createCheckoutSessionConfig: {
									payloadJSON: payData.payload,
									signature: payData.signature,
									publicKeyId: this.amazonPayConfig?.publicKeyId
								}
							});

							console.log('✅ Amazon Pay checkout初始化成功');

						} catch (error) {
							console.error('❌ Amazon Pay按钮点击处理失败:', error);
							this.$emit('error', error);
						}
					});

					console.log('✅ PC端模式Amazon Pay按钮设置完成');
					return;
				}

				// 有数据的情况下，直接渲染
				console.log('📋 使用现有数据渲染Amazon Pay按钮');

				// 确保payload是正确的JSON字符串并验证格式
				let payloadString;
				let payloadObj;

				if (typeof amazonPayData.payload === 'string') {
					try {
						payloadObj = JSON.parse(amazonPayData.payload);
						payloadString = amazonPayData.payload;
					} catch (e) {
						console.error('❌ payload不是有效的JSON字符串:', amazonPayData.payload);
						throw new Error('Invalid payload JSON string');
					}
				} else if (typeof amazonPayData.payload === 'object') {
					payloadObj = amazonPayData.payload;
					payloadString = JSON.stringify(amazonPayData.payload);
				} else {
					throw new Error('Payload must be an object or JSON string');
				}
				payloadString = JSON.stringify(payloadObj);
				console.log('📋 最终的payloadJSON:', payloadString);
				console.log('🔐 签名:', amazonPayData.signature);

				// 获取货币信息
				const currency = amazonPayData.payload?.paymentDetails?.chargeAmount?.currencyCode || 'USD';

				// 渲染真实的Amazon Pay按钮 - 使用官方标准格式
				console.log('🎯 准备调用amazon.Pay.renderButton，配置如下:');
				const buttonConfig = {
					// set checkout environment
					merchantId: this.amazonPayConfig?.merchantId,
					publicKeyId: this.amazonPayConfig?.publicKeyId,
					ledgerCurrency: currency,
					// customize the buyer experience
					checkoutLanguage: 'en_US',
					productType: 'PayAndShip',
					placement: 'Checkout',
					buttonColor: 'Gold',
					estimatedOrderAmount: {
						amount: payloadObj?.paymentDetails?.chargeAmount?.amount || "1.00",
						currencyCode: currency
					},
					// configure Create Checkout Session request - 使用静态值而不是回调
					createCheckoutSessionConfig: {
						payloadJSON: payloadString, // string generated in step 2
						signature: amazonPayData.signature, // signature generated in step 3
						publicKeyId: this.amazonPayConfig?.publicKeyId,
					}
				};
				console.log('📋 按钮配置:', buttonConfig);
				console.log('🎯 按钮ID:', `#${this.buttonId}`);

				const amazonPayButton = amazon.Pay.renderButton(`#${this.buttonId}`, buttonConfig);
				console.log('🎯 Amazon Pay按钮对象:', amazonPayButton);

				console.log('✅ 真实Amazon Pay按钮渲染成功');

			} catch (error) {
				console.error('❌ 渲染真实Amazon Pay按钮失败:', error);
				this.errorMessage = `支付按钮渲染失败: ${error.message}`;
				this.$emit('error', error);
			}
		},

		// 强制设置按钮内容区域大小
		forceButtonHeight() {
			console.log('🎨 强制修改Amazon Pay按钮内容区域大小');

			// 查找Amazon Pay按钮容器
			const buttonContainer = document.querySelector(`#${this.buttonId}`);
			if (buttonContainer) {
				console.log('🎯 找到按钮容器:', buttonContainer);

				// 查找内部的iframe（Amazon Pay按钮内容）
				const iframe = buttonContainer.querySelector('iframe');
				if (iframe) {
					console.log('📺 找到iframe，调整大小');
					iframe.style.setProperty('height', '70px', 'important');
					iframe.style.setProperty('min-height', '70px', 'important');
					iframe.style.setProperty('width', '100%', 'important');
					iframe.style.setProperty('transform', 'scale(1.2)', 'important'); // 放大内容
					iframe.style.setProperty('transform-origin', 'center', 'important');
				}

				// 查找所有div元素（可能是按钮内容）
				const divs = buttonContainer.querySelectorAll('div');
				divs.forEach((div, index) => {
					console.log(`🔧 处理div ${index}:`, div.className);
					div.style.setProperty('height', '70px', 'important');
					div.style.setProperty('min-height', '70px', 'important');
					div.style.setProperty('font-size', '18px', 'important'); // 增大字体
					div.style.setProperty('line-height', '70px', 'important');
					div.style.setProperty('padding', '0 20px', 'important'); // 增加内边距
				});

				// 设置容器本身的大小
				buttonContainer.style.setProperty('height', '70px', 'important');
				buttonContainer.style.setProperty('min-height', '70px', 'important');
				buttonContainer.style.setProperty('width', '100%', 'important');
				buttonContainer.style.setProperty('overflow', 'visible', 'important');
			}

			// 全局查找Amazon Pay相关元素并放大内容
			const amazonElements = document.querySelectorAll('[class*="amazon"], [id*="amazon"], [class*="amazonpay"], [id*="amazonpay"]');
			amazonElements.forEach(element => {
				if (element.tagName.toLowerCase() === 'iframe') {
					element.style.setProperty('transform', 'scale(1.2)', 'important');
					element.style.setProperty('transform-origin', 'center', 'important');
				}
				element.style.setProperty('font-size', '18px', 'important');
				element.style.setProperty('height', '70px', 'important');
			});

			console.log('✅ Amazon Pay按钮内容区域放大完成');
		},

		// 重试支付
		async retryPayment() {
			this.errorMessage = '';
			console.log('🔄 重试Amazon Pay按钮初始化');
			this.$emit('retry');
			await this.renderAmazonPayButton();
		}
	}
};
</script>

<style lang="scss" scoped>
.amazon-pay-button-container {
	width: 100%;

	::v-deep .options-box {
		justify-content: flex-end;
	}

	::v-deep .options-right {
		width: 50vw;
	}

	::v-deep .u-tabbar__content {
		background: transparent;
	}

	.amazon-button-wrapper {
		width: 100%;

		.amazon-pay-button {
			width: 100%;
			min-height: 110rpx;
			background: transparent;

		}
	}
}

/* Amazon Pay按钮基础样式 */
.amazon-button-container {
	width: 100%;
	min-height: 70px;
}

/* 放大Amazon Pay按钮内容区域 */
:global([id^="AmazonPayButton"]) {
	width: 100% !important;
	min-height: 70px !important;
	height: 70px !important;
	overflow: visible !important;
}

/* 放大iframe内容（按钮的实际内容） */
:global([id^="AmazonPayButton"] iframe) {
	width: 100% !important;
	min-height: 70px !important;
	height: 70px !important;
	transform: scale(1.2) !important;
	transform-origin: center !important;
	border: none !important;
}

/* 放大按钮内的文字和内容 */
:global([id^="AmazonPayButton"] div) {
	height: 70px !important;
	min-height: 70px !important;
	font-size: 18px !important;
	line-height: 70px !important;
	padding: 0 20px !important;
}

/* 确保按钮文字更大更清晰 */
:global([id^="AmazonPayButton"] span),
:global([id^="AmazonPayButton"] button),
:global([id^="AmazonPayButton"] [role="button"]) {
	font-size: 18px !important;
	font-weight: bold !important;
	height: 70px !important;
	line-height: 70px !important;
	padding: 0 20px !important;
}

/* 针对Amazon Pay按钮的各种可能的类名 */
:global(.amazon-pay-button),
:global(.amazonpay-button-parent-container-checkout),
:global([class*="amazon-pay"]),
:global([class*="amazonpay"]) {
	width: 100% !important;
	min-height: 70px !important;
	height: 70px !important;
	max-height: none !important;
}

:global(.amazon-pay-button *),
:global(.amazonpay-button-parent-container-checkout *),
:global([class*="amazon-pay"] *),
:global([class*="amazonpay"] *) {
	height: 70px !important;
	min-height: 70px !important;
	max-height: none !important;
}
</style>