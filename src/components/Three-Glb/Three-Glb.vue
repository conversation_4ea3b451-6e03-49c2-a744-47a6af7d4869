<template>
	<canvas type="webgl" id="webgl" style="width: 100%; height: 100%;" @touchstart="touchStart" @touchmove="touchMove"
		@touchend="touchEnd"></canvas>
</template>

<script>
	/*
	 * @Author: sonders
	 * @Date: 2023-09-15 09:46:04
	 * @LastEditTime: 2023-09-15 09:46:04
	 * @Description: ThreeJS渲染glb模型
	 * @Description: 微信小程序自测没问题，很丝滑
	 * @Description: 引入该组件前先执行  npm  install three-platformize
	 * @Description: 本组件也是辛苦琢磨好几天才搞出来的，分享出来只是为了帮助和我一样不太懂ThreeJS的人快速渲染GLB模型
	 */
	import * as THREE from 'three-platformize';
	import {
		WechatPlatform
	} from 'three-platformize/src/WechatPlatform';
	import {
		GLTFLoader
	} from 'three-platformize/examples/jsm/loaders/GLTFLoader';
	import {
		OrbitControls
	} from 'three-platformize/examples/jsm/controls/OrbitControls';
	export default {
		name: 'Three3DViewGlb',
		data() {
			return {
				canvas: null,
				camera: null,
				scene: null,
				renderer: null,
				model: null,
				controls: null,
				loopIndex: null
			}
		},
		props: {
			baseUrl: {
				type: String,
				default: 'https://dtmall-tel.alicdn.com/edgeComputingConfig/upload_models/1591673169101/RobotExpressive.glb'
			}
		},
		mounted() {
			this.$nextTick(() => {
				this.init();
			})
		},
		methods: {
			async init() {
				const {
					canvas
				} = await this.getCanvas();
				this.canvas = canvas;
				const platform = new WechatPlatform(canvas); // webgl canvas
				platform.enableDeviceOrientation('game'); // 开启DeviceOrientation
				THREE.PLATFORM.set(platform);
				this.platform = platform;
				this.renderModel();
			},
			//获取画布
			async getCanvas(delay = 200) {
				return new Promise((resolve, reject) => {
					const t = setTimeout(() => {
						clearTimeout(t);
						uni.createSelectorQuery().in(this)
							.select('#webgl')
							.fields({
								node: true
							})
							.exec((res) => {
								console.log('res', res)
								if (res && res[0] && res[0].node) {
									const canvas = res[0].node;
									resolve({
										canvas
									});
								} else {
									reject("获取canvas失败");
								}
							});
					}, delay);
				});
			},
			async renderModel() {
				this.camera = new THREE.PerspectiveCamera(45, this.canvas.width / this.canvas.height, 0.25, 100);
				this.camera.lookAt(new THREE.Vector3(0, 2, 0));
				this.scene = new THREE.Scene();
				this.scene.background = new THREE.Color(0xe0e0e0);
				this.scene.fog = new THREE.Fog(0xe0e0e0, 20, 100);
				this.clock = new THREE.Clock();
				// lights
				var light = new THREE.HemisphereLight(0xffffff, 0x444444);
				this.scene.add(light);
				// 改变外壳颜色
				var AmbientLight = new THREE.AmbientLight(0x815800); // 环境光
				this.scene.add(AmbientLight);
				// 平行光
				light = new THREE.DirectionalLight(0xffffff);
				light.position.set(0, 20, 10);
				this.scene.add(light);
				// ground
				var mesh = new THREE.Mesh(new THREE.PlaneBufferGeometry(2000, 2000), new THREE.MeshPhongMaterial({
					color: 0x999999,
					depthWrite: false
				}));
				mesh.rotation.x = -Math.PI / 2;
				this.scene.add(mesh);
				// 方块线
				// var grid = new THREE.GridHelper(200, 40, 0x000000, 0x000000);
				// grid.material.opacity = 0.6;
				// grid.material.transparent = true;
				// this.scene.add(grid);

				var loader = new GLTFLoader();
				loader.load(this.baseUrl, (
					gltf) => {
						this.model = gltf.scene;
						this.scene.add(this.model);
					}, undefined,
					function(e) {
						console.error(e);
					});

				this.renderer = new THREE.WebGLRenderer({
					antialias: true
				});
				this.renderer.setPixelRatio(wx.getSystemInfoSync().pixelRatio);
				this.renderer.setSize(this.canvas.width, this.canvas.height);
				this.renderer.gammaFactor = 2.2;

				this.controls = new OrbitControls(this.camera, this.renderer.domElement);
				this.controls.enableZoom = false;
				this.camera.position.set(1, 1, 2);
				this.animate();
			},
			animate() {
				this.loopIndex = this.canvas.requestAnimationFrame(this.animate);
				this.renderer.render(this.scene, this.camera);
				this.controls.update();
			},
			touchStart(e) {
				this.platform.dispatchTouchEvent(e);
			},
			touchMove(e) {
				this.platform.dispatchTouchEvent(e);
			},
			touchEnd(e) {
				this.platform.dispatchTouchEvent(e);
			}
		}
	}
</script>