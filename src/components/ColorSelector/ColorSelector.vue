<template>
  <div class="color-selector">
    <div
      v-for="(color, index) in colorList"
      :key="index"
      :style="{ backgroundColor: color }"
      class="color-option"
      :class="{ 'is-selected': color === selectedColor }"
      @click="selectColor(color)"
    >
      <div v-if="color === selectedColor" class="selected-animation"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ColorSelector',
  props: {
    colorList: {
      type: Array,
      required: true
    },
    value: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      selectedColor: this.value
    };
  },
  watch: {
    value(newValue) {
      this.selectedColor = newValue;
    }
  },
  methods: {
    selectColor(color) {
      this.selectedColor = color;
      this.$emit('input', color);
    }
  }
};
</script>

<style scoped>
.color-selector {
  display: flex;
  align-items: center;
  @include flex-gap(0, 38rpx); // 替换了 column-gap
}

.color-option {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  position: relative;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.color-option:hover {
  transform: scale(1.1);
}

.is-selected {
	width: 37rpx;
	height: 37rpx;
	border: 1px solid red;
}
</style>