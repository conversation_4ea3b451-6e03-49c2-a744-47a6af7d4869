<template>
	<view>
		<view class="goods-intro-box">
			<image class="favorite_icon" :src="favoriteImg" @click="onAddFavorite"></image>
			<view class="bike-img-box">
				<image class="goodsImg" src="@/static/assets/home/<USER>" mode="aspectFit" lazy-load
					:draggable="false">
				</image>
			</view>
			<view class="goods-info-box">
				<view class="goods-name">foldtan M-160</view>
				<view class="discount-price-box">
					<image class="sale_icon" src="@/static/assets/home/<USER>"></image>
					<text class="discount-price-text">$500 OFF</text>
				</view>
				<view class="price-footer-box">
					<view class="price-box">
						<text class="origin-price">$50.18</text>
						<text class="discount-price">$50.18</text>
					</view>
					<image class="addCart" src="@/static/assets/home/<USER>" @click="onAddCart"></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "goodsIntro",
		data() {
			return {
				colorCurrent: 0,
				isFavorite: false
			};
		},
		computed: {
			favoriteImg() {
				return require(`@/static/assets/home/<USER>'favorite' : 'favorite_no' }.png`);
			}
		},
		methods: {
			onAddFavorite() {
				this.isFavorite = !this.isFavorite;
				
				uni.showToast({
					mask: true,
					title: 'Add Successfully',
					image: "/static/assets/common/add_successfully.png"
				})
			},
			onAddCart() {
				// this.isAddCartShow = true;
				// this.$refs.popup.open()
				uni.$emit('onOpenPopup')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.goods-intro-box {
		width: 100%;
		background: #FFFFFF;
		box-shadow: 0px 35 87rpx 0px rgba(167, 167, 167, 0.2);
		border-radius: 31rpx;
		padding: 33rpx 23rpx 27rpx;
		box-sizing: border-box;
		position: relative;

		.favorite_icon {
			position: absolute;
			top: 6rpx;
			right: 16rpx;
			z-index: 666;
			width: 70rpx;
			height: 70rpx;
		}

		.bike-img-box {
			width: 100%;
			height: 213rpx;
			padding: 20rpx 0 10rpx;

			.goodsImg {
				width: 100%;
				height: 100%;
			}
		}

		.goods-info-box {
			.goods-name {
				font-weight: bold;
				font-size: 27rpx;
				color: #262626;
				margin: 19rpx 0 4rpx;
			}

			.discount-price-box {
				display: inline-flex;
				align-items: center;
				height: 33rpx;
				border-radius: 8rpx;
				border: 2rpx solid #FF5A1E;
				box-sizing: border-box;

				.sale_icon {
					width: 30rpx;
					height: 30rpx;
					background: #FF5A1E;
					border-radius: 4rpx;
				}

				.discount-price-text {
					font-weight: 400;
					font-size: 24rpx;
					color: #FF5A1E;
					padding: 0 8rpx;
				}
			}

			.price-footer-box {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.price-box {
					.origin-price {
						font-weight: bold;
						font-size: 27rpx;
						color: #FF5A1E;
						margin-right: 10rpx;
					}

					.discount-price {
						font-weight: bold;
						font-size: 27rpx;
						color: #999999;
						text-decoration: line-through;
					}
				}

				.addCart {
					width: 36rpx;
					height: 36rpx;
				}
			}
		}
	}
</style>