<template>
	<view class="coupon-list-box">
		<scroll-view scroll-y="true" :style="{ height: isContentHeight }">
			<radio-group shape="circle" placement="column" v-bind="checkboxObj" v-show="couponShowType!=4">
				<label class="coupon-item" v-for="(item, index) in singleCouponData" :key="index"
					@click="checkboxChange(item)">
					<view class="coupon-left">
						<text class="sign">{{item.type==0?'':priceSymbol }}</text>
						<text class="price">{{item.type==0? item.discount +'%' : item.cutprice}}</text>
					</view>
					<view class="coupon-center flex-twice">
						<u--text text="Expires:" color="#C8AD8B" size="27rpx"></u--text>
						<u--text :text="`To ${item.end}`" color="#C8AD8B" size="27rpx"></u--text>
					</view>
					<view class="coupon-right">
						<radio :value="String(item.cid)" :checked="selectCoupon === item.cid"
							:disabled="isTimeDisabled(item.end)" color="#FF5A1E" style="transform:scale(0.7)" />
					</view>
				</label>
			</radio-group>

			<checkbox-group @change="toggleCouponSelection" v-show="couponShowType==4">
				<label class="coupon-item" v-for="(item, index) in multipleCouponData" :key="index">
					<view class="coupon-left">
						<text class="sign">{{ priceSymbol }}</text>
						<text class="price">{{ item.cutprice }}</text>
					</view>
					<view class="coupon-center flex-twice">
						<u--text text="Expires:" color="#C8AD8B" size="27rpx"></u--text>
						<u--text :text="`To ${item.end}`" color="#C8AD8B" size="27rpx"></u--text>
					</view>
					<view class="coupon-right">
						<checkbox :value="String(item.cid)" :checked="selectedCouponIds.includes(item.cid)"
							:disabled="isTimeDisabled(item.end) || item.disabled" color="#fff" activeBackgroundColor="#FF5A1E"
							style="transform:scale(0.7)" />
					</view>
				</label>
			</checkbox-group>
		</scroll-view>
		<view class="flex justify-around align-center" style="margin-top: 20rpx;">
			<SubmitButton btnW="300rpx" @handleConfirm="handleCouponConfirm"></SubmitButton>
			<SubmitButton btnW="300rpx" text="Nonuse" bgColor="#ddd" fontColor="#000" @handleConfirm="handleClearCoupon"></SubmitButton>
		</view>
	</view>
</template>

<script>
	import {
		mapGetters
	} from 'vuex';
	

	export default {
		name: "coupon-list",
		props: {
			couponShowType: {
				type: Number,
				default: 0
			},
			selectCouponCid: {
				type: Number,
				default: 0
			},
			selectMultipleCouponCid: {
				type: Array,
				default: () => []
			},
			couponData: {
				type: Array,
				default: () => []
			},
			isContentHeight: {
				type: String,
				default: '350px'
			}
		},
		data() {
			return {
				selectCoupon: this.selectCouponCid,
				selectedCouponIds: this.selectMultipleCouponCid,
				checkboxObj: {
					activeColor: '#FF5A1E',
					iconColor: '#fff'
				}
			};
		},
		computed: {
			...mapGetters(['priceSymbol']),
			isTimeDisabled() {
				return (end) => {
					const now = new Date().getTime();
					const endtime = new Date(end).getTime();
					return now > endtime;
				}
			},
			singleCouponData() {
				return this.couponData.filter(item => item.CouponWay != 4);
			},
			multipleCouponData() {
				const couponData = this.couponData.filter(item => item.CouponWay == 4);
				couponData.forEach(item => {
					this.$set(item, 'disabled', false)
				});
				return couponData;
			}
		},
		methods: {
			checkboxChange(item) {
				if (this.isTimeDisabled(item.end)) return;

				const selectedId = item.cid;
				if (this.selectCoupon === selectedId) {
					// 取消选择普通优惠券
					this.selectCoupon = null;
				} else {
					// 选择普通优惠券，需要清空推荐优惠券的选择
					this.selectCoupon = selectedId;
					this.selectedCouponIds = []; // 清空推荐优惠券选择
				}
				console.log('普通优惠券选择:', this.selectCoupon)
			},
			toggleCouponSelection(event) {
				const selectedCouponIds = event.target.value;
				console.log('推荐优惠券选择变化:', selectedCouponIds)

				// 如果选择了推荐优惠券，清空普通优惠券的选择
				if (selectedCouponIds.length > 0) {
					this.selectCoupon = null;
				}

				// 限制推荐优惠券最多选择3张
				if (selectedCouponIds.length >= 3) {
					this.multipleCouponData.forEach(item => {
						if (!selectedCouponIds.includes(String(item.cid))) {
							item.disabled = true;
						}
					})
				} else {
					this.multipleCouponData.forEach(item => {
						item.disabled = false;
					})
				}
				
				this.selectedCouponIds = selectedCouponIds.map(cid => +cid);
				console.log('当前推荐优惠券选择:', this.selectedCouponIds)
			},
			handleCouponConfirm() {
				console.log('确认优惠券选择 - 普通优惠券:', this.selectCoupon, '推荐优惠券:', this.selectedCouponIds);
				
				let selectCouponData = [];
				let couponList = [];
				
				// 如果选择了普通优惠券
				if (this.selectCoupon) {
					selectCouponData = [this.selectCoupon];
					couponList = this.couponData.filter(item => item.cid === this.selectCoupon);
				}
				// 如果选择了推荐优惠券
				else if (this.selectedCouponIds.length > 0) {
					selectCouponData = [...this.selectedCouponIds];
					couponList = this.couponData.filter(item => this.selectedCouponIds.includes(item.cid));
				}
				
				console.log('最终选择的优惠券:', couponList);
				this.$emit('handleSelectCoupon', couponList)
			},
			handleClearCoupon() {
				// 清空所有优惠券选择
				this.selectCoupon = null;
				this.selectedCouponIds = [];
				
				// 重置推荐优惠券的禁用状态
				this.multipleCouponData.forEach(item => {
					item.disabled = false;
				});
				
				console.log('已清空所有优惠券选择');
				this.$emit('handleClearCoupon')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.coupon-list-box {
		::v-deep uni-checkbox .uni-checkbox-input{
			border-radius: 50%;
		}
		.coupon-item {
			background: #FFFFFF;
			border-radius: 31rpx;
			border: 1px solid rgba(0, 0, 0, 0.05);
			padding: 30rpx;
			box-sizing: border-box;
			display: flex;
			align-items: center;
  @include flex-gap(0, 35rpx); // 替换了 column-gap
			margin-bottom: 30rpx;

			&:last-child {
				margin-bottom: 0;
			}

			.coupon-left {
				width: 165rpx;
				height: 165rpx;
				border-radius: 15px;
				background: url(../../static/assets/mine/Group.png) no-repeat center center #FFE8D3;
				background-size: 112rpx 112rpx;
				display: flex;
				justify-content: center;
				align-items: center;

				.sign {
					font-weight: 600;
					font-size: 23rpx;
					color: #915A16;
				}

				.price {
					font-weight: 600;
					font-size: 38rpx;
					color: #915A16;
					margin-left: 2rpx;
					margin-bottom: 8rpx;
				}
			}
		}
	}
</style>