<template>
	<view class="img-list-box">
		<view v-if="imgList.length == 1">
			<u--image :src="imgList[0]" width="100%" :height="oneImageHeight" radius="18rpx" mode="aspectFill"
				@click="previewImage(imgList, 0)"></u--image>
		</view>
		<template v-if="imgList.length == 2">
			<view class="two-img-box">
				<u--image v-for="(item, index) in imgList" :key="index" :src="item" width="100%" :height="oneImageHeight"
					radius="18rpx" mode="aspectFill" @click="previewImage(imgList, index)"></u--image>
			</view>
		</template>
		<template v-if="imgList.length >= 3">
			<view class="three-img-box">
				<view class="left-box">
					<u--image :src="imgList[0]" width="100%" :height="oneImageHeight" radius="18rpx" mode="aspectFill"
						@click="previewImage(imgList, 0)"></u--image>
				</view>
				<view class="right-box">
					<u--image :src="imgList[1]" width="100%" height="100%" radius="18rpx" mode="aspectFill"
						@click="previewImage(imgList, 1)"></u--image>
					<view class="more-img-box" @click="previewImage(imgList, 2)">
						<u--image class="image" :src="imgList[2]" width="100%" height="100%" radius="18rpx"
							mode="aspectFill"></u--image>
						<template v-if="imgMoreCount > 0">
							<view class="mask"></view>
							<view class="image-more-count">
								+{{ imgMoreCount }}
							</view>
						</template>
					</view>
				</view>
			</view>
		</template>
	</view>
</template>

<script>
	export default {
		name: "imageList",
		props: {
			imgList: {
				type: Array,
				default: () => []
			},
			oneImageHeight: {
				type: String,
				default: '308rpx'
			}
		},
		data() {
			return {

			};
		},
		computed: {
			imgMoreCount() {
				return `${this.imgList.length - 3}`
			}
		},
		methods: {
			previewImage(imgList, current) {
				uni.previewImage({
					current,
					loop: true,
					urls: imgList
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.two-img-box {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
  @include flex-gap(0, 8rpx); // 替换了 column-gap
	}

	.three-img-box {
		display: grid;
		grid-template-columns: 1fr 177rpx;
  @include flex-gap(0, 8rpx); // 替换了 column-gap

		.right-box {
			display: grid;
			grid-template-rows: repeat(2, 1fr);
  @include flex-gap(8rpx, 0); // 替换了 row-gap

			.more-img-box {
				position: relative;
				height: 100%;

				.image {
					height: inherit;
				}

				.mask {
					position: absolute;
					left: 0;
					top: 0;
					width: 100%;
					height: 100%;
					background-color: rgba(0, 0, 0, 0.5);
					border-radius: 18rpx;
				}

				.image-more-count {
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
					width: 62rpx;
					height: 62rpx;
					border-radius: 50%;
					background: rgba(0, 0, 0, 0.51);
					display: flex;
					justify-content: center;
					align-items: center;
					color: #fff;
				}
			}
		}
	}
</style>