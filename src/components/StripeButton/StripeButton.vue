<template>
	<view class="stripe-button-container">
		<!-- 移动端优化的支付表单弹窗 -->
		<u-popup :show="showPaymentForm" :round="20" :closeable="false" mode="center" :overlay="true"
			:closeOnClickOverlay="false" @close="showPaymentForm = false" customStyle="width: 90%; max-width: 500px;">
			<view class="payment-form">
				<!-- 弹窗标题 -->
				<view class="payment-header">
					<text class="payment-title">Secure Payment</text>
					<view class="close-btn" @click="closePaymentForm">
						<text class="close-icon">×</text>
					</view>
				</view>

				<view class="form-content">

					<!-- 使用 Payment Element 替代 Card Element -->
					<view class="payment-element-container">
						<view id="payment-element" class="payment-element"></view>
					</view>

					<view class="payment-actions">
						<u-button class="submit-btn" text="Pay Now" :loading="isSubmitting" :disabled="isSubmitting"
							@click="submitPayment" color="#FF5A1E" shape="round" size="large">
						</u-button>
					</view>
				</view>
			</view>
		</u-popup>


	</view>
</template>

<script>
import config from '@/config';
import { stripeCapture } from "@/api/orderDetails";
import paymentSDKManager from '@/utils/payment/paymentSDKManager.js';
import { waitForStripeSDK, isStripeSDKLoaded } from '@/utils/payment/stripeSDK.js';
/**
 * StripeButton 组件 - 使用 Stripe Elements 标准流程
 * 
 * ✅ 推荐逻辑结构：
 * 1. 主动判断 PaymentIntent 状态
 * 2. 如果 status === 'requires_payment_method'，弹出表单收集卡片信息
 * 3. 如果有 payment_method，直接确认支付
 * 
 * 标准流程：
 * 1. 初始化 Stripe
 * 2. 创建 Elements 实例
 * 3. 创建卡片元素并挂载
 * 4. 用户提交表单时执行 confirmCardPayment
 * 
 * 使用方式：
 * <StripeButton 
 *   :clientSecret="clientSecret"
 *   :amount="19990000"
 *   :billingName="customerName"
 *   :checkPaymentIntentStatus="false"  // 默认关闭，避免依赖后端接口
 *   @success="handlePaymentSuccess"
 *   @error="handlePaymentError"
 * />
 * 
 * 可选：启用主动状态检查（简化版本，直接假设需要支付方式）
 * :checkPaymentIntentStatus="true"
 */
export default {
	name: 'StripeButton',
	props: {
		// PHP 提供的 Stripe client_secret
		clientSecret: {
			type: String,
			default: ''
		},
		// 支付金额
		amount: {
			type: [String, Number],
			default: '0.00'
		},
		orderInfo: {
			type: Object,
			default: () => ({

			})
		},
		// 订单号（用于支付结果页面）
		orderNumber: {
			type: String,
			default: ''
		},
		// 是否自动跳转到支付结果页面
		autoRedirect: {
			type: Boolean,
			default: true
		},
		// 是否自动支付
		autoPay: {
			type: Boolean,
			default: true
		},
		// 账单姓名
		billingName: {
			type: String,
			default: ''
		},
		// 是否主动检查 PaymentIntent 状态
		checkPaymentIntentStatus: {
			type: Boolean,
			default: false  // 默认关闭，避免依赖后端接口
		}
	},
	data() {
		return {
			isLoading: false,
			stripe: null,
			showPaymentForm: false,
			isSubmitting: false,
			elements: null,
			paymentElement: null,
			paymentMethodReady: false,
			isMobile: false
		}
	},
	computed: {
		// 从全局配置获取 Stripe 公钥
		stripeKey() {
			return this.stripePublishableKey || config.payment.stripe.publishableKey || '';
		}
	},
	watch: {
		// 监听 clientSecret 变化，直接初始化 Elements 并显示表单
		clientSecret: {
			handler(newClientSecret, oldClientSecret) {
				console.log("🚀 ~ file: StripeButton.vue:63 ~ newClientSecret:", newClientSecret)
				console.log("🚀 ~ file: StripeButton.vue:63 ~ oldClientSecret:", oldClientSecret)

				if (this.autoPay && newClientSecret && newClientSecret !== oldClientSecret) {
					console.log('检测到新的 client_secret，直接初始化 Elements 并显示表单:', newClientSecret);

					// 如果 Stripe 还没初始化，等待初始化完成
					if (!this.stripe) {
						console.log('Stripe 未初始化，等待初始化完成...');
						this.waitForStripeInit(newClientSecret);
					} else {
						console.log('Stripe 已初始化，直接显示支付表单');
						this.$nextTick(() => {
							this.initElementsAndShowForm();
						});
					}
				}
			},
			immediate: false
		}
	},

	methods: {
		// 初始化 Stripe
		async initStripe() {
			try {
				console.log('🚀 StripeButton组件开始初始化');
				console.log('🔍 检查window.Stripe是否存在:', !!window.Stripe);

				// 使用统一的SDK管理系统确保Stripe SDK可用
				if (isStripeSDKLoaded()) {
					console.log('✅ Stripe SDK已经预加载完成');
					this.stripe = window.Stripe;
				} else {
					console.log('🔄 Stripe SDK正在加载中，等待完成...');

					// 尝试等待预加载完成，但设置5秒超时
					const timeoutPromise = new Promise((_, reject) => {
						setTimeout(() => reject(new Error('等待预加载超时')), 5000);
					});

					try {
						this.stripe = await Promise.race([
							waitForStripeSDK(),
							timeoutPromise
						]);
						console.log('✅ Stripe SDK预加载成功:', this.stripe);
					} catch (timeoutError) {
						console.warn('⚠️ 预加载等待超时，尝试重新加载 Stripe SDK');
						// 预加载失败，使用SDK管理器重新加载
						await paymentSDKManager.ensureSDK('stripe');
						this.stripe = window.Stripe;
						console.log('✅ Stripe SDK重新加载成功');
					}
				}

				if (!this.stripe) {
					console.error('❌ Stripe SDK加载失败');
					return;
				}

				console.log('✅ Stripe SDK准备就绪');
				console.log('🔧 Stripe SDK功能检查:', {
					hasStripe: !!this.stripe,
					hasElements: !!this.stripe.elements,
					hasConfirmPayment: !!this.stripe.confirmPayment
				});

				// 如果设置了自动支付且有 clientSecret，直接显示表单
				if (this.autoPay && this.clientSecret) {
					console.log('初始化完成，直接显示支付表单');
					this.$nextTick(() => {
						this.initElementsAndShowForm();
					});
				}
			} catch (error) {
				console.error('Error initializing Stripe:', error);
				this.$emit('error', error);
			}
		},



		// 使用Elements收集支付方式 - 标准流程（已废弃，使用 initElementsAndShowForm）
		async handlePaymentWithElements() {
			console.log('调用 handlePaymentWithElements，重定向到 initElementsAndShowForm');
			this.initElementsAndShowForm();
		},

		// 显示支付表单
		showPaymentFormModal() {
			this.showPaymentForm = true;

			// 等待DOM更新后挂载支付元素
			this.$nextTick(() => {
				setTimeout(() => {
					this.mountPaymentElement();
				}, 100);
			});
		},

		// 挂载支付元素 - 标准流程
		mountPaymentElement() {
			try {
				const container = document.getElementById('payment-element');
				if (container && this.paymentElement) {
					// 2️⃣ 挂载Payment Element到DOM
					this.paymentElement.mount('#payment-element');
					console.log('✅ Payment Element挂载成功');

					// 监听Payment Element变化
					this.paymentElement.on('change', (event) => {
						console.log('Payment Element状态变化:', event);
						this.paymentMethodReady = event.complete;
					});

					// 监听准备状态
					this.paymentElement.on('ready', () => {
						console.log('✅ Payment Element已准备就绪');
						this.paymentMethodReady = true;
					});
				}
			} catch (error) {
				console.error('💥 挂载Payment Element失败:', error);
			}
		},

		// 提交支付 - 统一支付处理
		async submitPayment() {
			try {
				this.isSubmitting = true;
				console.log('💳 Stripe支付按钮被点击');

				// 确保Stripe SDK在点击时是可用的
				console.log('🔍 检查Stripe SDK可用性...');
				await paymentSDKManager.ensureSDK('stripe');
				console.log('✅ Stripe SDK确认可用');

				// 确保stripe实例和elements都已就绪
				if (!this.stripe) {
					this.stripe = window.Stripe;
				}

				if (!this.elements) {
					this.$toast({
						title: '支付元素未初始化',
						type: 'error'
					});
					return;
				}

				console.log('🔄 处理支付...');

				// 统一支付处理，适用于移动端和Web端
				const data = await this.stripe.confirmPayment({
					elements: this.elements,
					confirmParams: {
						payment_method_data: {
							billing_details: this.getBillingDetails()
						}
					},
					redirect: 'if_required',
					handleActions: true,
				});
				let { error, paymentIntent } = data
				console.log("🚀 ~ submitPayment ~ data:", JSON.stringify(data))

				await this.handlePaymentResult(error, paymentIntent);

			} catch (error) {
				console.error('💥 支付过程中出错:', error);
				this.$toast({
					title: error.message || '支付失败，请重试',
					type: 'error'
				});
				this.$emit('error', error);
			} finally {
				this.isSubmitting = false;
			}
		},

		// 处理支付结果
		async handlePaymentResult(error, paymentIntent) {
			// 打印支付回调的完整结果
			console.log('🔔 支付回调结果:', {
				error: error,
				paymentIntent: paymentIntent,
				timestamp: new Date().toISOString(),
				clientSecret: this.clientSecret
			});

			if (error) {
				console.error('💥 支付失败:', {
					errorCode: error.code,
					errorMessage: error.message,
					errorType: error.type,
					errorDetails: error
				});

				throw new Error(error.message);
			} else if (paymentIntent && paymentIntent.status === 'succeeded') {
				const stripeCaptureRes = await stripeCapture({
					paymentId: paymentIntent.id
				})
				this.$toast({
					title: 'Payment Successful!',
					type: 'success'
				});
				console.log("🚀 ~ handlePaymentResult ~ stripeCaptureRes:", stripeCaptureRes)
				this.showPaymentForm = false;
				// 跳转到支付成功页面
				if (stripeCaptureRes.order_status === 1) {
					this.$tab.redirectTo(
						`/pages/paymentResult/paymentResult?orderNumber=${stripeCaptureRes.OId}&isResult=true`
					);

					this.$emit('success', paymentIntent);
				} else {
					this.$toast({
						title: 'Payment failed, please try again',
						type: 'error'
					});
				}
			}
		},

		// 初始化 Elements 并显示表单 - 使用Payment Element
		initElementsAndShowForm() {
			try {
				console.log('开始初始化 Elements 并显示表单...');

				// 检测移动端
				this.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

				// 1️⃣ 初始化 Stripe Elements
				this.elements = this.stripe.elements({
					clientSecret: this.clientSecret
				});

				// 2️⃣ 创建Payment Element替代Card Element
				this.paymentElement = this.elements.create('payment', {
					layout: {
						type: this.isMobile ? 'accordion' : 'tabs',
						defaultCollapsed: false,
						radios: false,
						spacedAccordionItems: false
					},
					paymentMethodOrder: this.getPaymentMethodOrder(),
					fields: {
						billingDetails: {
							name: 'auto',
							email: 'auto',
							address: 'never'
						}
					}
				});

				// 3️⃣ 显示支付表单
				this.showPaymentFormModal();

			} catch (error) {
				console.error('初始化 Elements 失败:', error);
				this.$toast({
					title: '初始化支付表单失败',
					type: 'error'
				});
			}
		},

		// 等待 Stripe 初始化完成
		waitForStripeInit(clientSecret) {
			const maxAttempts = 50; // 最多等待5秒
			let attempts = 0;

			const checkStripe = () => {
				attempts++;
				console.log(`等待 Stripe 初始化... 第 ${attempts} 次检查`);

				if (this.stripe) {
					console.log('Stripe 初始化完成，直接显示支付表单');
					this.$nextTick(() => {
						this.initElementsAndShowForm();
					});
					return;
				}

				if (attempts >= maxAttempts) {
					console.error('Stripe 初始化超时');
					this.$toast({
						title: '支付初始化失败，请重试',
						type: 'error'
					});
					return;
				}

				// 继续等待
				setTimeout(checkStripe, 100);
			};

			checkStripe();
		},

		// 关闭支付表单
		closePaymentForm() {
			this.showPaymentForm = false;
			if (this.paymentElement) {
				this.paymentElement.unmount();
				this.paymentElement = null;
			}
			if (this.elements) {
				this.elements = null;
			}
			this.paymentMethodReady = false;
		},
		// 获取支付方式顺序（根据环境调整）
		getPaymentMethodOrder() {
			if (location.protocol === 'https:' && this.isMobile) {
				return ['card', 'apple_pay', 'google_pay'];
			}
			return ['card']; // HTTP环境下只显示信用卡
		},

		// 获取账单详情（从 orderInfo 中提取地址信息）
		getBillingDetails() {
			// 支持两种数据结构：
			// 1. 新格式：orderInfo.billing_address (接口返回格式)
			// 2. 旧格式：orderInfo 直接包含地址信息 (向后兼容)
			const billingData = this.orderInfo?.billing_address || this.orderInfo;

			const billingDetails = {
				name: this.billingName || billingData?.name || 'Customer'
			};

			// 如果有地址信息，添加到 billing_details
			if (billingData?.address) {
				billingDetails.address = {
					line1: billingData.address.line1 || billingData.address.street || '',
					line2: billingData.address.line2 || '',
					city: billingData.address.city || '',
					state: billingData.address.state || billingData.address.province || '',
					postal_code: billingData.address.postal_code || billingData.address.zip || '',
					country: billingData.address.country || 'US'
				};
			}

			// 如果有邮箱信息，添加到 billing_details
			if (billingData?.email) {
				billingDetails.email = billingData.email;
			}

			// 如果有电话信息，添加到 billing_details
			if (billingData?.phone) {
				billingDetails.phone = billingData.phone;
			}

			console.log('📋 构建的 billing_details:', billingDetails);
			return billingDetails;
		},
	},
	mounted() {
		// 初始化移动端检测
		this.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
		console.log('📱 移动端检测:', this.isMobile ? '是' : '否');

		this.initStripe();

		// 如果初始化时就有 clientSecret，直接显示表单
		if (this.autoPay && this.clientSecret) {
			console.log('初始化时已有 clientSecret，等待 Stripe 初始化完成后显示表单');
		}
	}
}
</script>

<style lang="scss" scoped>
.stripe-button-container {
	.stripe-btn {
		width: 100%;
		height: 85rpx;
		border-radius: 69rpx;
		font-weight: 600;
		font-size: 38rpx;
	}
}

.payment-form {
	background: #fff;
	border-radius: 20rpx;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

.payment-header {
	display: flex;
	justify-content: center;
	align-items: center;
	position: relative;
	padding: 40rpx 40rpx 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
	background: #fafafa;
}

.close-btn {
	position: absolute;
	right: 30rpx;
	top: 50%;
	transform: translateY(-50%);
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	cursor: pointer;
	transition: all 0.3s ease;
}


.close-icon {
	font-size: 40rpx;
	color: #666;
	font-weight: bold;
	line-height: 1;
}

.payment-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

.form-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	padding: 20rpx;
}

.payment-element-container {}

.payment-element {
	min-height: 200rpx;
	border-radius: 12rpx;
	background: #fafafa;
}

.payment-actions {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	margin-top: 30rpx;
}

.submit-btn {
	width: 100%;
	height: 88rpx;
	border-radius: 44rpx;
	font-weight: 600;
	font-size: 32rpx;
	transition: all 0.3s ease;
}

.submit-btn:active {
	transform: translateY(2rpx);
}



.tips-text {
	font-size: 24rpx;
	color: #999;
	line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 750rpx) {


	.form-content {
		padding: 30rpx;
	}

	.payment-header {
		padding: 30rpx 30rpx 15rpx;
	}

	.payment-title {
		font-size: 32rpx;
	}
}
</style>