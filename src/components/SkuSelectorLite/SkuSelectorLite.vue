<template>
	<view class="sku-selector-lite">
		<uni-section 
			class="uni-section-box" 
			:title="item.Name_en" 
			titleFontSize="16" 
			titleColor="#262626"
			v-for="(item, indexW) in skuAttr" 
			:key="indexW"
		>
			<view class="version-list">
				<block v-for="(cItem, indexN) in item.children" :key="indexN">
					<view 
						v-if="cItem.pic" 
						@click="tapAttr(indexW, indexN, cItem)"
						:class="['picTagItem', { 'on': cItem.selected === 1 }]"
					>
						<u--image 
							:src="cItem.pic" 
							:width="picW(cItem.selected)" 
							:height="picH(cItem.selected)"
							radius="50%" 
							duration="0"
						></u--image>
					</view>
					<view 
						v-else
						:class="['tagItem', { 'on': cItem.selected === 1 }]"
						@click="tapAttr(indexW, indexN, cItem)"
					>
						{{ cItem.name }}
					</view>
				</block>
			</view>
		</uni-section>
	</view>
</template>

<script>
export default {
	name: "SkuSelectorLite",
	props: {
		// 规格属性数据
		skuData: {
			type: Object,
			default: () => ({})
		},
		// 商品ID，用于区分不同商品
		productId: {
			type: [String, Number],
			required: true
		}
	},
	data() {
		return {
			skuAttr: {}
		};
	},
	computed: {
		picW() {
			return (selected) => {
				if (selected === 1) {
					return '37rpx';
				}
				return '40rpx';
			}
		},
		picH() {
			return (selected) => {
				if (selected === 1) {
					return '37rpx';
				}
				return '40rpx';
			}
		}
	},
	watch: {
		skuData: {
			handler(newVal) {
				if (newVal && newVal.attr) {
					this.skuAttr = JSON.parse(JSON.stringify(newVal.attr));
					// 初始化时选择第一个选项
					this.initDefaultSelection();
				}
			},
			immediate: true,
			deep: true
		}
	},
	methods: {
		// 初始化默认选择（选择每个属性的第一个选项）
		initDefaultSelection() {
			if (!this.skuAttr) return;
			
			Object.keys(this.skuAttr).forEach(attrId => {
				const attribute = this.skuAttr[attrId];
				if (attribute && attribute.children && attribute.children.length > 0) {
					// 清除所有选择
					attribute.children.forEach(child => {
						child.selected = 0;
					});
					// 选择第一个选项
					attribute.children[0].selected = 1;
				}
			});
			
			// 触发选择变化事件
			this.emitSelectionChange();
		},
		
		// 点击规格选项
		tapAttr(indexW, indexN, item) {
			const currentSku = this.skuAttr[indexW].children[indexN];
			
			// 先清除同组其他选项的选择
			this.skuAttr[indexW].children.forEach(child => {
				child.selected = 0;
			});
			
			// 设置当前选项为选中
			currentSku.selected = 1;
			
			// 触发选择变化事件
			this.emitSelectionChange();
		},
		
		// 触发选择变化事件
		emitSelectionChange() {
			const selectedAttrs = this.getSelectedAttrs();
			this.$emit('selection-change', {
				productId: this.productId,
				selectedAttrs: selectedAttrs,
				skuAttr: this.skuAttr
			});
		},
		
		// 获取当前选中的规格
		getSelectedAttrs() {
			const selectedAttrs = {};
			
			Object.keys(this.skuAttr).forEach(attrId => {
				const attribute = this.skuAttr[attrId];
				if (attribute && attribute.children) {
					const selectedChild = attribute.children.find(child => child.selected === 1);
					if (selectedChild) {
						selectedAttrs[attrId] = {
							vid: selectedChild.vid,
							name: selectedChild.name,
							pic: selectedChild.pic
						};
					}
				}
			});
			
			return selectedAttrs;
		}
	}
}
</script>

<style lang="scss" scoped>
.sku-selector-lite {
	.uni-section-box {
		margin-bottom: 20rpx;
	}
	
	.version-list {
		display: flex;
		flex-wrap: wrap;
  		@include flex-gap(20rpx); // 替换了 gap 单值
		margin-top: 20rpx;
	}
	
	.tagItem {
		padding: 15rpx 25rpx;
		background: #F5F5F5;
		border-radius: 8rpx;
		font-size: 28rpx;
		color: #666;
		border: 2rpx solid transparent;
		transition: all 0.3s ease;
		
		&.on {
			background: #FF5A1E;
			color: #fff;
			border-color: #FF5A1E;
		}
	}
	
	.picTagItem {
		border-radius: 50%;
		border: 3rpx solid transparent;
		transition: all 0.3s ease;
		
		&.on {
			border-color: #FF5A1E;
		}
	}
}
</style>