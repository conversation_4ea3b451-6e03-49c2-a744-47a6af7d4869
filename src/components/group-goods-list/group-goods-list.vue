<template>
	<view class="goods-list-box">
		<view v-for="cItem in ordersProductsList" :key="cItem.ProId" class="goods-items">
			<view>
				<view class="main-center">
					<view class="goods-img-box" @click="jump">
						<u--image :src="cItem.PicPath" width="185rpx" height="132rpx" radius="15rpx"
							mode="widthFix"></u--image>
						<view class="gift-title" v-if="cItem.IsGift == 1 && cItem.GiftText != ''">
							<u--text :text="cItem.GiftText" color="#fff" bold size="10px"></u--text>
						</view>
					</view>
					<view class="goods-info-box">
						<u--text class="shop-title" :text="cItem.Name" lines="1" color="#262626" bold
							@click="jump"></u--text>

						<view class="main-info">
							<view class="main-info-left">
								<view class="collapsible-container" v-if="getSpecificationCount(cItem) > 0"
									@click.stop="toggleSpecExpand(cItem)" @tap.stop="toggleSpecExpand(cItem)">
									<view class="sku-left-box" :class="{ 'collapsed': !cItem.isSpecOpen }">
										<!-- 普通商品Sku -->
										<template v-if="cItem.BuyType == '0' && Object.keys(cItem.Property).length">
											<view v-for="(value, key) in cItem.Property" :key="key">
												<u--text :text="`${key}: ${value}`" color="#8C8C8C"
													size="27rpx"></u--text>
											</view>
										</template>

										<!-- 组合促销 主Sku -->
										<template
											v-if="cItem.BuyType == '4' && mainSku(cItem.ProId, cItem.Property) && Object.keys(cItem.Property).length">
											<view v-for="(value, key) in mainSku(cItem.ProId, cItem.Property)"
												:key="key">
												<u--text :text="`${key}:${value}`" color="#8C8C8C"
													size="27rpx"></u--text>
											</view>
										</template>

										<template v-if="cItem.BuyType == '5' && Object.keys(cItem.Property).length">
											<view v-for="(value, key) in cItem.Property" :key="key">
												<u--text :text="`${key}:${value}`" color="#8C8C8C"
													size="27rpx"></u--text>
											</view>
										</template>
									</view>
									<view class="toggle-icon" v-if="getSpecificationCount(cItem) > 2">
										<u-icon :name="cItem.isSpecOpen ? 'arrow-up' : 'arrow-down'" size="8px" bold
											color="#8C8C8C"></u-icon>
									</view>
								</view>
							</view>
							<view class="main-info-right">
								<u--text :text="`x${cItem.Qty}`" color="#8C8C8C" size="27rpx"></u--text>
							</view>
						</view>
					</view>
				</view>
				<div v-if="cItem.BuyType == '4' && cItem.package_row.length" @click="jump">
					<block v-for="(pak, cIndex) in cItem.package_row" :key="cItem.ProId">
						<OrderItem :PicPath="pak.PicPath_0" :Name_en="pak.Name_en" :isPrice="false"
							:goodsPrice="pak.Price" :isQty="true" Qty="1" style="padding-right: 0;"
							class="package-item-1">
							<template slot="skuInfo">
								<view v-for="(value, key) in mainSku(pak.ProId, cItem.Property)" :key="key">
									<u--text :text="`${key}: ${value}`" color="#8C8C8C" size="27rpx"></u--text>
								</view>
							</template>
						</OrderItem>
					</block>
				</div>
				<!-- 价格 -->
				<u--text :text="`${curItem.NewSymbol}${cItem.Price}`" size="38rpx" bold color="#262626" align="right"
					margin="10px 0 0 0" @click="jump"></u--text>
			</view>
		</view>
	</view>
</template>

<script>
import SkuMixins from '@/mixins/sku.js';

export default {
	name: "group-goods-List",
	mixins: [SkuMixins],
	props: {
		ordersProductsList: {
			type: Array,
			default: () => []
		},
		curItem: {
			type: Object,
			default: () => { }
		}
	},
	data() {
		return {

		};
	},
	methods: {
		getSpecificationCount(cItem) {
			if (cItem.BuyType == '0' && Object.keys(cItem.Property).length) {
				return Object.keys(cItem.Property).length;
			}
			if (cItem.BuyType == '4' && this.mainSku(cItem.ProId, cItem.Property) && Object.keys(cItem.Property).length) {
				return Object.keys(this.mainSku(cItem.ProId, cItem.Property)).length;
			}
			if (cItem.BuyType == '5' && Object.keys(cItem.Property).length) {
				return Object.keys(cItem.Property).length;
			}
			return 0;
		},
		toggleSpecExpand(cItem) {
			if (this.getSpecificationCount(cItem) > 2) {
				this.$set(cItem, 'isSpecOpen', !cItem.isSpecOpen);
			}
		},
		jump() {
			this.$emit('jump')
		}
	}
}
</script>

<style lang="scss" scoped>
.goods-list-box {
	overflow: hidden;

	.goods-items {
		margin-top: 30rpx;
		margin-bottom: 40rpx;
		box-sizing: border-box;

		&:first-child {
			margin-bottom: 0;
		}

		.main-center {
			display: flex;

			.goods-img-box {
				position: relative;

				.gift-title {
					background-color: #FF5A1E;
					width: fit-content;
					padding: 2px;
					border-radius: 4px;
					position: absolute;
					right: 0;
					top: -8px;
				}
			}

			.goods-info-box {
				margin-left: 21rpx;
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				/* 让标题和main-info上下分离 */
				min-height: 132rpx;

				/* 确保有足够高度，与图片高度对齐 */
				.shop-title {
					align-items: flex-start !important;
				}

				.main-info {
					display: inline-flex;
					justify-content: space-between;
					align-items: flex-end;
					/* 让main-info内容底部对齐 */
					width: 100%;
					margin-top: auto;
					/* 让main-info自动推到底部 */
				}
			}
		}
	}
}

.collapsible-container {
	display: inline-flex;
	justify-content: space-between;
	align-items: flex-start;
	gap: 20rpx;
	padding: 4rpx 0;
	border-radius: 12rpx;
	box-sizing: border-box;
	color: #8C8C8C;
	width: 346rpx;
	cursor: pointer;

	.sku-left-box {
		flex: 1;
		overflow: hidden;
		transition: all 0.3s ease;

		&.collapsed {
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2;
			/* 默认显示2行 */
			line-clamp: 2;
			overflow: hidden;
			text-overflow: ellipsis;
			line-height: 32rpx;
			max-height: 64rpx;
			/* 2行的高度：32rpx * 2 */
		}

		&:not(.collapsed) {
			display: block;
			max-height: none;
		}
	}

	.toggle-icon {
		display: flex;
		align-items: center;
		justify-content: center;
		min-height: 32rpx;
		flex-shrink: 0;
	}

	.u-icon {
		margin-top: 4rpx;
		flex-shrink: 0;
	}
}
</style>