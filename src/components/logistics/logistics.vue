<template>
	<view class="page">
		<!-- 收货地址 -->
		<view class="content">
			<view class="flex list" :class="{one: index == 0 && wlInfo.delivery_status == 1}"
				v-for="(item, index) in wlInfo.list" :key="index">
				<view class="time">
					<view class="day">{{item.timeArr[0]}}</view>
					<view>{{item.timeArr[1]}}</view>
				</view>
				<view class="info flex1">
					<view class="title">{{ item.delivery_statusTxt }}</view>
					<view class="text">{{item.context}}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			wlInfo: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			return {}
		}
	}
</script>

<style lang="scss" scoped>
	/*flex 转换成flex容器*/
	.flex {
		display: flex;
		flex-direction: row;
	}

	/*flex1 自动填充*/
	.flex1 {
		flex: 1;
	}

	/*ali-c 竖直居中*/
	.ali-c {
		display: flex;
		flex-direction: row;
		align-items: center;
	}
	//收货地址
	.content {
		padding: 31rpx;
		box-sizing: border-box;

		.list {
			&:first-child {
				.info::before {
					bottom: -20rpx;
					margin-top: 40rpx;
					border-left: 1px dashed #e5e5e5;
				}

				.title {

					&::before {
						background-color: #FF5A1E;
					}
				}
			}

			&:last-child {
				.info::before {
					height: 32rpx;
				}
			}

			&.one {
				.info::before {
					margin-top: 20rpx;
				}

				.title {
					font-weight: bold;
					font-size: 31rpx;
					color: #FF5A1E;

					&::before {
						background-color: #FF5A1E;
					}
				}

				.text {
					font-weight: bold;
					font-size: 25rpx;
					color: rgba(255, 90, 30, 0.7);
				}

				.time {
					color: #333;

					.day {
						font-size: 24rpx;
					}
				}
			}
		}

		.time {
			width: 150rpx;
			padding-right: 30rpx;
			font-size: 20rpx;
			text-align: right;
			color: #999;
			font-weight: bold;
			color: #8C8C8C;


			.day {
				margin-bottom: 4rpx;
			}
		}

		.info {
			position: relative;
			color: #999;

			&::before {
				content: "";
				position: absolute;
				left: 0;
				top: 0;
				bottom: 0;
				z-index: 1;
				width: 0;
				border-left: 1px solid #e5e5e5;
			}

			.title {
				position: relative;
				margin-bottom: 10rpx;
				padding-left: 32rpx;
				font-size: 28rpx;
				font-weight: bold;
				font-size: 29rpx;
				color: #262626;

				// &::before {
				// 	content: "";
				// 	position: absolute;
				// 	left: -12rpx;
				// 	top: 4.5px;
				// 	bottom: 0;
				// 	z-index: 1;
				// 	width: 24rpx;
				// 	height: 24rpx;
				// 	background: rgba(140, 140, 140, 0.3);
				// 	// background-color: red;
				// 	border-radius: 50%;
				// 	filter: blur(2rpx);
				// }

				&::before {
					content: "";
					position: absolute;
					left: -8rpx;
					top: 0;
					bottom: 0;
					z-index: 2;
					width: 16rpx;
					height: 16rpx;
					margin: auto 0;
					border-radius: 50%;
					background: #C0C0C0;
				}

				&.address {
					font-size: 24rpx;
					color: #333;
				}
			}

			.text {
				padding: 0 0 44rpx 32rpx;
				font-weight: bold;
				font-size: 25rpx;
				color: #8C8C8C;

			}
		}
	}
</style>