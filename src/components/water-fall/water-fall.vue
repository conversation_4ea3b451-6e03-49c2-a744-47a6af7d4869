<template>
	<view class="water-fall">
		<div class="left-water-fall">
			<div v-for="(item, index) in leftList" :key="index" class="item-wrapper">
				<slot :item="item" name="itemLeft"></slot>
			</div>
		</div>

		<div class="right-water-fall">
			<slot name="advertiseSlot"></slot>
			<div v-for="(item, index) in rightList" :key="index" class="item-wrapper">
				<slot :item="item" name="itemRight"></slot>
			</div>
		</div>
	</view>
</template>

<script>
	export default {
		name: "water-fall",
		props: {
			dataList: {
				type: Array,
				default: () => []
			},
			col: {
				type: Number,
				default: 2
			}
		},
		computed: {
			leftList() {
				return this.dataList.filter((item, index) => index % this.col === 0)
			},
			rightList() {
				return this.dataList.filter((item, index) => index % this.col !== 0)
			}
		},
		data() {
			return {

			};
		}
	}
</script>

<style lang="scss" scoped>
	.water-fall {
		display: grid;
		grid-template-columns: 1fr 1fr;
		grid-gap: 30rpx;
		margin: 0 auto;
		width:calc(100vw - 64rpx);
		.item-wrapper {
			margin-top: 32rpx;
			
			&:first-child {
				margin-top: 0;
			}
		}

		.left-water-fall {
			width: 329rpx;
			justify-content: center;
		}

		.right-water-fall {
			width: 329rpx;
			justify-content: center;
		}
	}
</style>