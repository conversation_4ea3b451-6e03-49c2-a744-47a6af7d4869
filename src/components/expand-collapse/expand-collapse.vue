<template>
	<view class="read-more-box">
		<view class="content-box" >
			<slot></slot>
		</view>
		<view class="tag" @click.stop="handleOpen" v-if="isTagShow">
			<u--text :text="isTextValue" :suffixIcon="suffixIcon" :iconStyle="iconStyle" align="center"></u--text>
		</view>
	</view>
</template>

<script>
	export default {
		name: "read-more",
		props: {
			closeText: {
				type: String,
				default: "Expand More"
			},
			openText: {
				type: String,
				default: "Put Away"
			},
			showHeight: {
				type: String,
				default: '200px'
			},
			color: {
				type: String,
				default: '#262626'
			},
			isTagShow: {
				type: Boolean,
				default: false
			},
			iconStyle: {
				type: [Object, String],
				default: () => {
					return {
						fontSize: '15px',
						color: '#262626',
						marginLeft: '5px'
					}
				}
			}
		},
		data() {
			return {
				isOpen: false,
				isMoreHeight: this.showHeight
			};
		},
		computed: {
			isTextValue() {
				return this.isOpen ? this.openText : this.closeText
			},
			suffixIcon() {
				return this.isOpen ? "arrow-up" : "arrow-down"
			}
		},
		methods: {
			handleOpen() {
				this.isOpen = !this.isOpen;
				this.isMoreHeight = this.isOpen ? 'auto' : this.showHeight;
			}
		}
	}
</script>

<style lang="scss" scoped>
	.read-more-box {
		.content-box {
			overflow: hidden;
			transition: height 0.3s ease-in-out;
		}

		.tag {
			padding: 5px 0;
			background-color: #fff;
			background-image: linear-gradient(-180deg, rgba(255, 255, 255, 0) 0%, rgb(255, 255, 255) 80%);
			padding-top: 100px;
			margin-top: -100px;
		}
	}
</style>