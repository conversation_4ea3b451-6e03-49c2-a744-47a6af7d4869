<template>
	<view class="order-item-box">
		<header>
			<slot name="header"></slot>
		</header>
		<main>
			<slot>
				<view class="main-center">
					<u--image :src="PicPath" width="180rpx" height="140rpx" radius="18rpx" mode="aspectFit"></u--image>
					<view class="main-right">
						<view class="main-title">
							<u--text :text="Name_en" lines="2" color="#262626" bold size="30rpx"></u--text>
							<u--text class="price-text" v-if="isPrice && !isGift"
								:text="`${currentPriceSymbol}${goodsPrice}`" lines="1" color="#FF5A1E" bold size="36rpx"
								align="right" margin="0 0 0 30rpx" style="width: fit-content; flex: none;    display: flex
;
    align-self: flex-start;"></u--text>
							<u--text v-else :text="`${currentPriceSymbol}${goodsPrice}`" lines="1" color="#8C8C8C" bold
								decoration="line-through" size="26rpx" align="right" margin="0 0 0 30rpx" style="width: fit-content; flex: none;margin-top:5rpx;    display: flex
;
    align-self: flex-start;"></u--text>
						</view>
						<view class="main-info">
							<view class="main-info-left">
								<slot name="skuInfo"></slot>
							</view>
							<view class="main-info-right" v-if="isQty">
								<u--text text="FREE" color="#FF5A1E" size="34rpx" v-if="isGift"
									style="font-family: PingFang SC-Medium;"></u--text>
								<u--text :text="`x${Qty}`" color="#8C8C8C" size="31rpx"></u--text>
							</view>
						</view>
					</view>
				</view>
			</slot>
		</main>
		<footer>
			<slot name="footer"></slot>
		</footer>
	</view>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
	name: "OrderItem",
	props: {
		isGift: {
			type: Boolean,
			default: false
		},
		PicPath: {
			type: String,
			default: ''
		},
		Name_en: {
			type: String,
			default: ''
		},
		goodsPrice: {
			type: String,
			default: '0.00'
		},
		goodsColor: {
			type: String,
			default: ''
		},
		goodsNum: {
			type: String,
			default: ''
		},
		Qty: {
			type: [String, Number],
			default: '0'
		},
		isPrice: {
			type: Boolean,
			default: true
		},
		isQty: {
			type: Boolean,
			default: true
		},
		customPriceSymbol: {
			type: String,
			default: ''
		},

	},
	computed: {
		...mapGetters(["priceSymbol"]),
		currentPriceSymbol() {
			if (this.customPriceSymbol) {
				return this.customPriceSymbol
			}
			return this.priceSymbol
		},
	},
	data() {
		return {

		};
	}
}
</script>

<style lang="scss" scoped>
.order-item-box {
	width: 100%;
	padding: 31rpx;
	box-sizing: border-box;
	// border-radius: 31rpx;
	background-color: #fff;

	.main-center {
		display: flex;
		justify-content: space-between;
		@include flex-gap(0, 21rpx); // 替换了 column-gap
		// align-items: center;

		.main-right {
			flex: 1;

			.main-title {
				display: flex;
				align-items: flex-start;
				align-items: center;
				margin-bottom: 12rpx;
				box-sizing: border-box;
			}

			.main-info {
				font-weight: 400;
				font-size: 27rpx;
				color: #8C8C8C;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.main-info-top {
					display: flex;
					justify-content: space-between;
					align-items: center;
				}

				.main-info-right {
					::v-deep .u-text {
						display: flex;
						justify-content: flex-end !important;
					}

				}


			}
		}
	}

	.footer-box {}
}
</style>