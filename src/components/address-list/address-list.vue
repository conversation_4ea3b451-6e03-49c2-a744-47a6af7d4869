<template>
	<view class="coupon-list-box" :style="{ height: isContentHeight }">
		<mescroll-body :down="{ use: addressData.length > 0 }" @init="mescrollInit" @down="downCallback"
			@up="upCallback">
			<scroll-view v-if="addressData.length > 0" scroll-y :style="{ height: isContentHeight }">
				<radio-group v-model="selectValue" shape="circle" placement="column" v-bind="checkboxObj"
					@change="checkboxChange">
					<label class="coupon-item" v-for="item in addressData" :key="item.AId">
						<view class="coupon-left">
							<u-avatar class="user-name" :text="avatarName(item.FirstName)" size="75rpx"
								bg-color="#ffded2" color="#FF5A1E" font-size="28rpx"></u-avatar>
						</view>
						<view class="coupon-center flex-twice">
							<view class="iflex">
								<u--text :text="item.FirstName" color="#262626" size="30rpx" bold
									margin="0 5px 0 0"></u--text>
								<u--text :text="item.PhoneNumber" color="#262626" size="30rpx" bold></u--text>
							</view>
							<u--text :text="addressDetail(item)" color="#8C8C8C" size="27rpx" bold
								style="word-break: break-all;"></u--text>
						</view>
						<view class="coupon-right">
							<radio :value="item.AId" :checked="selectValue === item.AId" color="#FF5A1E"
								style="transform:scale(0.7)" />
						</view>
					</label>
				</radio-group>
			</scroll-view>
		</mescroll-body>
	</view>
</template>

<script>
	import {
		getCoupon
	} from '@/api/orderDetails.js';
	import {
		userAddress
	} from '@/api/address.js';
	import {
		mapGetters
	} from 'vuex';
	import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";

	export default {
		name: "address-list",
		mixins: [MescrollMixin],
		props: {
			selectAddAid: {
				type: [String, Number],
				default: 0
			},
			isContentHeight: {
				type: String,
				default: '350px'
			}
		},
		data() {
			return {
				selectValue: this.selectAddAid,
				addressData: [],
				checkboxObj: {
					activeColor: '#FF5A1E',
					iconColor: '#fff'
				}
			};
		},
		computed: {
			addressDetail() {
				return (item) => {
					return `${item.Country},${item.State},${item.City},${item.AddressLine1}`
				}
			},
			avatarName() {
				return (name) => {
					return name.substring(0, 1);
				}
			}
		},
		methods: {
			downCallback() {
				this.mescroll.resetUpScroll();
			},
			upCallback(page) {
				userAddress({
					page: page.num,
					size: page.size,
					type: 0
				}).then(res => {
					if (page.num == 1) {
						this.addressData = [];
					}

					this.addressData = this.addressData.concat(res.result);
					this.mescroll.endByPage(res.result.length, res.total_pages);
				}).catch(() => {
					this.mescroll.endErr();
				})
			},
			checkboxChange(event) {
				console.log(event)
				const Aid = event.detail.value;
				const selectAddData = this.addressData.find(item => item.AId === Aid);
				this.$emit('handleSelectAdd', selectAddData)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.coupon-list-box {
		
		.user-name {
			::v-deep .u-text__value {
				font-weight: bold !important;
			}
		}

		.coupon-item {
			background: #FFFFFF;
			border-radius: 31rpx;
			border: 1px solid rgba(0, 0, 0, 0.05);
			padding: 30rpx;
			box-sizing: border-box;
			display: flex;
			align-items: center;
			margin-bottom: 30rpx;

			&:last-child {
				margin-bottom: 0;
			}

			.coupon-center {
				margin: 0 70rpx 0 20rpx;
			}
		}

		.no-address {
			height: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;

			.add-btn {
				position: fixed;
				bottom: 60rpx;
				left: 0;
				right: 0;
				width: 90%;
			}
		}
	}
</style>