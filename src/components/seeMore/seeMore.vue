<template>
	<view>
		<view v-for="(item, index) in visibleComments" :key="index">
			<slot :item="item" name="content"></slot>
		</view>

		<view @click="handleMoreReply">
			<slot name="more-btn">
				<view v-if="showMoreBtn" class="flex align-center" :style="styleObject">
					<u-line color="rgba(0,0,0,0.1)" length="36rpx" :hairline="false"></u-line>
					<u--text :text="moreReplyLen" color="#8C8C8C" size="24rpx" bold margin="0 0 0 4px"></u--text>
				</view>
			</slot>
		</view>
	</view>
</template>

<script>
export default {
	name: "seeMore",
	props: {
		dataList: {
			type: Array,
			default: () => []
		},
		isPartLen: {
			type: Number,
			default: 2
		},
		styleObject: {
			type: Object
		}
	},
	data() {
		return {
			isLoadMore: false,
			dataLists: this.dataList
		};
	},
	computed: {
		visibleComments() {
			if (this.isLoadMore) {
				return this.dataLists;
			} else {
				return this.dataLists.slice(0, this.isPartLen);
			}
		},
		moreReplyLen() {
			const dataLen = this.dataLists.length;
			return dataLen > this.isPartLen ? `More reply ${dataLen - this.isPartLen}` : '';
		},
		showMoreBtn() {
			return this.dataLists.length > this.isPartLen && !this.isLoadMore;
		}
	},
	methods: {
		handleMoreReply() {
			this.isLoadMore = !this.isLoadMore;
		}
	}
}
</script>

<style lang="scss"></style>