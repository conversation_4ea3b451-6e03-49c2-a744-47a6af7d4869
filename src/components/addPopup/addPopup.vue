<template>
	<view>
		<u-popup :show="isAddCartShow" round="15" @close="onclose" @touchmove.stop.prevent>
			<view class="add-cart-box">
				<view class="goods-box">
					<u--image class="goods-img" :src="productImg" width="181rpx" height="181rpx" radius="10"
						duration="0" mode="widthFix" />
					<text class="origin-price">{{ priceSymbol }}{{ productDetailData.Price }}</text>
					<text class="discount-price" v-if="+productDetailData.MarketPrice">{{ priceSymbol }}{{
						productDetailData.MarketPrice }}</text>
				</view>
				<scroll-view class="product-info" scroll-y="true" enhanced="true" :show-scrollbar="false">
					<uni-section class="uni-section-box" :title="item.Name_en" titleFontSize="16" titleColor="#262626"
						v-for="(item, indexW) in skuData.attr" :key="indexW">
						<view class="version-list">
							<block v-for="(cItem, indexN) in item.children" :key="indexN">
								<view v-if="cItem.pic" @click="tapAttr(indexW, indexN, cItem)"
									:class="['picTagItem', { 'on': cItem.selected === 1 }, { 'disabled': cItem.disabled }]">
									<u--image :src="cItem.pic" :width="picW(cItem.selected)"
										:height="picH(cItem.selected)" radius="50%" duration="0"></u--image>
								</view>
								<view v-else
									:class="['tagItem', { 'on': cItem.selected === 1 }, { 'disabled': cItem.disabled }]"
									@click="tapAttr(indexW, indexN, cItem)">
									{{ cItem.name }}
								</view>
							</block>
						</view>
					</uni-section>
					<view class="Quantity-box">
						<view class="Quantity-left">
							<text class="main-title">Quantity</text>
							<text class="intro" v-if="productDetailData.Stock <= 0">(out of stock)</text>
							<text class="intro" v-else-if="productDetailData.Stock">(stock: {{ productDetailData.Stock
							}})</text>
						</view>
						<view class="Quantity-right">
							<uni-number-box background="#fff" v-model="numberValue" :width="20" :min="1"
								:max="productDetailData.Stock" />
						</view>
					</view>
				</scroll-view>
				<u-button class="confirm-btn" type="error" shape="circle" text="Confirm" color="#FF5A1E"
					:loading="isSubLoading" @click="onConfirmFn"></u-button>
			</view>
		</u-popup>
	</view>
</template>

<script>
import {
	getCartPopCart,
	getCartSelectPopCart,
	getCartAddToCart
} from '@/api/products.js';
import {
	mapGetters
} from 'vuex';

export default {
	props: {
		isCart: {
			type: Boolean,
			default: true,
		},
		initSelectedSku: { type: Object || String, default: () => null },
	},
	name: "addPopup",
	data() {
		return {
			isAddCartShow: false,
			isSubLoading: false,
			productImg: '',
			colorCurrent: 0,
			numberValue: 1,
			colorList: ['#4CAF50', '#FF5A1E', '#67B5E1'],
			selectedColor: '#4CAF50',
			skuData: {},
			productDetailData: {},
			selectedSku: {},
			versionList: [{
				name: 'Single-Battery',
				checked: true
			},
			{
				name: 'Dual-Battery',
				checked: false
			}, {
				name: 'Dual-Battery',
				checked: false
			},
			{
				name: 'Dual-Battery',
				checked: false
			}
			]
		};
	},
	computed: {
		...mapGetters(['priceSymbol', 'cartNum']),
		bikeColor() {
			return (backgroundColor, index) => {
				if (this.colorCurrent === index) {
					return {
						backgroundColor,
						width: '37rpx',
						height: '37rpx',
						// padding: '12rpx',
						border: `3rpx solid ${backgroundColor}`
					}
				}
				return {
					backgroundColor
				}
			}
		},
		picW() {
			return (selected) => {
				if (selected === 1) {
					return '37rpx';
				}
				return '40rpx';
			}
		},
		picH() {
			return (selected) => {
				if (selected === 1) {
					return '37rpx';
				}
				return '40rpx';
			}
		}
	},
	watch: {
		isAddCartShow(val) {
			if (val) {
				if (this.productDetailData?.pro?.ProId) {
					this.getCartPopCart(this.productDetailData?.pro?.ProId)
				}
			}
		}
	},
	methods: {
		// 规格属性弹窗
		getCartPopCart(ProId) {
			getCartPopCart({
				ProId
			}).then(data => {
				this.skuData = data;
				// 初始化规格禁用状态
				this.initSkuDisabledStatus();
				// 回显选中
				this.setSelectedSkuFromInit();
			})
		},
		setSelectedSkuFromInit() {
			if (!this.initSelectedSku || !this.skuData?.attr) return;
			const initSelect = JSON.stringify(this.initSelectedSku) == '{}'
			for (const attrId in this.skuData.attr) {
				if (initSelect) {
					this.skuData.attr[attrId].children.forEach(child => {
						child.selected = 0;
					});
				} else {
					const selectedVid = this.initSelectedSku[`Attr_ary[${attrId}]`];
					if (selectedVid) {
						this.skuData.attr[attrId].children.forEach(child => {
							child.selected = (child.vid === selectedVid) ? 1 : 0;
						});
					}
				}
			}
			// 选中后，更新禁用状态
			this.updateSkuDisabledStatus();
			this.getSelectdSkuData();
		},
		// 规格属性选择
		getCartSelectPopCart(data) {
			getCartSelectPopCart(data).then(data => {
				const {
					Price,
					MarketPrice,
					PicPath,
					Stock
				} = data;
				this.productDetailData.Price = Price;
				this.productDetailData.MarketPrice = MarketPrice;
				this.productImg = PicPath;
				this.productDetailData.Stock = +Stock;

				// 如果当前选择的数量大于新的库存，自动调整到最大库存
				if (this.numberValue > +Stock) {
					// 如果库存为0或负数，设置为1（虽然不应该出现这种情况）
					// 如果库存大于0，调整到库存数量
					this.numberValue = +Stock > 0 ? +Stock : 1;
					console.log(`库存变更：当前数量超过新库存(${Stock})，已调整为: ${this.numberValue}`);

					// 如果库存确实为0，显示提示
					if (+Stock <= 0) {
						this.$toast({
							title: 'Selected combination is out of stock'
						});
					}
				}
			})
		},
		tapAttr(indexW, indexN, item) {
			// 检查是否被禁用（基于组合库存逻辑）
			if (item.disabled) {
				this.$toast({
					title: 'This option is not available'
				})
				return
			}
			// 注释掉原来的单个Qty检查，现在完全基于组合库存
			// if (item.Qty == 0) {
			// 	this.$toast({
			// 		title: 'Out of stock'
			// 	})
			// 	return
			// }
			const currentSku = this.skuData.attr[indexW].children[indexN];
			currentSku.selected = currentSku.selected === 1 ? 0 : 1;
			this.skuData.attr[indexW].children.map((item, index) => {
				if (index !== indexN) {
					item.selected = 0;
				}
			});

			// 更新规格禁用状态
			this.updateSkuDisabledStatus();

			this.getSelectdSkuData();

			const data = {
				ProId: this.productDetailData?.pro?.ProId
			}

			for (const key in this.selectedSku) {
				data[`Attr_ary[${key}]`] = this.selectedSku[key];
			}

			const originSkuAttr = Object.keys(this.skuData.attr);
			const selectedSkuAttr = Object.keys(this.selectedSku);

			if (originSkuAttr.length === selectedSkuAttr.length) {
				this.getCartSelectPopCart(data);
			}
		},
		getSelectdSkuData() {
			this.selectedSku = {};
			for (const attrId in this.skuData.attr) {
				const selectedChild = this.skuData.attr[attrId].children.find(child => child.selected === 1);

				if (selectedChild) {
					this.selectedSku[`Attr_ary[${attrId}]`] = selectedChild.vid;
				}
			}
		},
		onclose() {
			console.log(this.selectedSku)
			this.$emit('close', { skuData: this.skuData, selectedSku: this.selectedSku })
			this.isAddCartShow = false;
		},
		onSelectVersion(name) {
			console.log(name)
			this.versionList.map((item, index) => {
				item.checked = name === index;
			})
		},

		/**
		 * 初始化规格禁用状态
		 */
		initSkuDisabledStatus() {
			if (!this.skuData?.attr || !this.skuData?.ext_ary) return;

			console.log('初始化规格禁用状态');

			// 遍历所有规格选项，检查是否有任何组合有库存
			Object.keys(this.skuData.attr).forEach(attrId => {
				const attribute = this.skuData.attr[attrId];
				if (attribute && attribute.children) {
					attribute.children.forEach(child => {
						// 检查包含这个选项的所有组合是否有库存
						child.disabled = !this.hasAnyStockWithOption(attrId, child.vid);

						console.log(`选项 ${child.name} (${child.vid}): 禁用=${child.disabled}`);
					});
				}
			});

			// 如果有默认选中的规格，需要更新其他规格的禁用状态
			const currentSelection = this.getCurrentSelection();
			if (Object.keys(currentSelection).length > 0) {
				console.log('检测到默认选择，更新关联禁用状态');
				this.updateSkuDisabledStatus();
			}
		},

		/**
		 * 更新规格禁用状态（规格切换时调用）
		 */
		updateSkuDisabledStatus() {
			if (!this.skuData?.attr || !this.skuData?.ext_ary) return;

			console.log('更新规格禁用状态');

			// 获取当前已选择的规格
			const currentSelection = this.getCurrentSelection();
			console.log('当前选择:', currentSelection);

			// 遍历所有规格选项，更新禁用状态
			Object.keys(this.skuData.attr).forEach(attrId => {
				const attribute = this.skuData.attr[attrId];
				if (!attribute || !attribute.children) return;

				attribute.children.forEach(child => {
					// 如果这个选项已经被选中，不禁用
					if (child.selected === 1) {
						child.disabled = false;
						return;
					}

					// 构建假设选择了这个选项的组合
					const testSelection = { ...currentSelection };
					testSelection[attrId] = child.vid;

					// 检查这个组合是否有库存
					const hasStock = this.checkCombinationStock(testSelection);

					// 设置禁用状态
					child.disabled = !hasStock;

				});
			});
		},
		/**
	 * 获取海外发货选项的value
	 */
		getOverseasValue() {
			if (!this.skuData?.Overseas?.children) return 'Ov:1'; // 默认值

			const selectedOverseas = this.skuData.Overseas.children.find(item => item.selected === 1);
			let selectValue = selectedOverseas ? selectedOverseas : this.skuData.Overseas.children[0];

			// 确保selectValue.vid存在，否则使用默认值1
			const vid = selectValue?.vid || '1';
			return `Ov:${vid}`
		},
		/**
		 * 生成库存key
		 * 格式：规格1_规格2_规格3_海外代码 (按从小到大排序)
		 */
		generateStockKey(selection) {
			if (!selection || Object.keys(selection).length === 0) return '';

			// 获取所有选择的vid并按从小到大排序
			const vids = Object.values(selection).map(vid => parseInt(vid)).sort((a, b) => a - b);

			// 获取海外发货代码
			const overseasValue = this.getOverseasValue();
			// 生成库存key
			const stockKey = vids.join('_') + '_' + overseasValue;

			return stockKey;
		},


		/**
		 * 获取当前已选择的规格
		 */
		getCurrentSelection() {
			const selection = {};
			if (!this.skuData?.attr) return selection;

			Object.keys(this.skuData.attr).forEach(attrId => {
				const attribute = this.skuData.attr[attrId];
				if (attribute && attribute.children) {
					const selectedChild = attribute.children.find(child => child.selected === 1);
					if (selectedChild) {
						selection[attrId] = selectedChild.vid;
					}
				}
			});

			return selection;
		},

		/**
		 * 检查规格组合是否有库存
		 */
		checkCombinationStock(selection) {
			if (!this.skuData?.ext_ary) return false;

			const attrIds = Object.keys(this.skuData.attr);
			const selectedAttrs = Object.keys(selection);

			// 如果不是所有属性都已选择，需要检查是否有任何可能的组合有库存
			if (selectedAttrs.length < attrIds.length) {
				return this.hasAnyValidCombination(selection);
			} else {
				// 如果所有属性都已选择，直接检查这个组合的库存
				const stockKey = this.generateStockKey(selection);
				const stock = this.skuData.ext_ary[stockKey];

				const hasStock = stock && stock !== "0" && parseInt(stock) > 0;
				return hasStock;
			}
		},

		/**
		 * 检查是否有任何可能的组合有库存
		 */
		hasAnyValidCombination(partialSelection) {
			if (!this.skuData?.attr || !this.skuData?.ext_ary) return false;

			const attrIds = Object.keys(this.skuData.attr);
			const unselectedAttrs = attrIds.filter(attrId => !partialSelection.hasOwnProperty(attrId));

			// 如果没有未选择的属性，直接检查当前组合
			if (unselectedAttrs.length === 0) {
				return this.checkCombinationStock(partialSelection);
			}

			// 生成所有可能的完整组合并检查库存
			const allCombinations = this.generateAllCombinations(partialSelection, unselectedAttrs);

			for (const combination of allCombinations) {
				const stockKey = this.generateStockKey(combination);
				const stock = this.skuData.ext_ary[stockKey];
				console.log("🚀 ~ file: addPopup.vue:406 ~ stock:", stock)

				if (stock && stock !== "0" && parseInt(stock) > 0) {
					return true;
				}
			}

			return false;
		},

		/**
		 * 生成所有可能的完整组合
		 */
		generateAllCombinations(partialSelection, unselectedAttrs) {
			if (unselectedAttrs.length === 0) {
				return [partialSelection];
			}

			const combinations = [];
			const [firstAttr, ...restAttrs] = unselectedAttrs;
			const attribute = this.skuData.attr[firstAttr];

			if (attribute && attribute.children) {
				attribute.children.forEach(child => {
					const newPartialSelection = { ...partialSelection, [firstAttr]: child.vid };
					const subCombinations = this.generateAllCombinations(newPartialSelection, restAttrs);
					combinations.push(...subCombinations);
				});
			}

			return combinations;
		},

		/**
		 * 检查包含特定选项的任何组合是否有库存
		 */
		hasAnyStockWithOption(attrId, vid) {
			if (!this.skuData?.attr || !this.skuData?.ext_ary) return false;

			// 构建包含这个选项的部分选择
			const partialSelection = { [attrId]: vid };

			// 检查是否有任何包含这个选项的组合有库存
			return this.hasAnyValidCombination(partialSelection);
		},

		/**
		 * 重置规格选择（在没有可用组合时调用）
		 */
		resetSkuSelection() {
			if (!this.skuData?.attr) return;

			console.log('重置规格选择');
			// 清除所有选择
			Object.keys(this.skuData.attr).forEach(attrId => {
				const attribute = this.skuData.attr[attrId];
				if (attribute && attribute.children) {
					attribute.children.forEach(child => {
						child.selected = 0;
					});
				}
			});

			// 重新初始化禁用状态
			this.initSkuDisabledStatus();
		},
		onConfirmFn() {
			if (this.isSubLoading) {
				return;
			}


			let Attr = {};
			let idObj = {};
			let skuNameList = [];
			if (Object.keys(this.skuData.attr).length) {
				for (const attrId in this.skuData.attr) {
					const isSelectdSku = this.skuData.attr[attrId].children.every(child => child.selected !== 1);
					if (isSelectdSku) {
						skuNameList.push(this.skuData.attr[attrId].Name_en);
					}
				}

				if (skuNameList.length) {
					this.$toast({
						title: `Please select ${skuNameList.join(',')}`
					});
					return
				}

				for (const attrId in this.skuData.attr) {
					const selectedChild = this.skuData.attr[attrId].children.find(child => child.selected === 1);

					if (selectedChild) {
						Attr[attrId] = selectedChild.vid;
						idObj[`id[${attrId}]`] = selectedChild.vid;
					}
				}
			} else {
				const overseasValue = this.getOverseasValue();
				Attr = {
					"Overseas": overseasValue
				}
			}

			this.isSubLoading = true;
			const data = {
				ProId: this.productDetailData?.pro?.ProId,
				Qty: this.numberValue,
				ItemPrice: this.productDetailData?.Price,
				products_type: this.productDetailData?.Products_type,
				Attr,
				...idObj
			};

			if (!this.isCart) {

				this.isAddCartShow = false;
				this.isSubLoading = false;
				this.$emit('config', { ...data, skuData: this.skuData, selectedSku: this.selectedSku })
				return;
			}
			getCartAddToCart(data).then(res => {
				this.$toast({
					title: 'Add Successfully',
					image: "/static/assets/common/add_successfully.png"
				});

				this.$store.dispatch('getCartNum');
				this.isAddCartShow = false;
			}).finally(() => {
				this.isSubLoading = false;
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.add-cart-box {
	padding: 31rpx;
	box-sizing: border-box;
	font-family: FontPingFang SC-Bold !important;
	display: flex;
	flex-direction: column;
	max-height: 80vh; /* 限制整体高度 */

	.product-info {
		max-height: 600rpx;
		/* 根据弹窗大小调整高度 */
		flex: 1;
		
		/* 确保scroll-view能正常滚动 */
		::v-deep .uni-scroll-view {
			height: 100%;
		}
	}

	.goods-box {
		display: flex;
		align-items: center;
		margin-bottom: 31rpx;

		// justify-content: center;
		::v-deep .u-image {
			display: flex;
			justify-content: center;
		}

		.goods-img {
			width: 181rpx;
			height: 181rpx;
		}

		.origin-price {
			margin-left: 63rpx;
			margin-right: 13rpx;
			font-weight: 600;
			font-size: 46rpx;
			color: #FF5A1E;
		}

		.discount-price {
			font-weight: 500;
			font-size: 35rpx;
			color: #8C8C8C;
			text-decoration: line-through;
		}
	}


	.color-box {
		margin-top: 13rpx;
		margin-bottom: 42rpx;

		.color-title {
			font-weight: bold;
			font-size: 33rpx;
			color: #262626;
		}

		.color-item-list {
			margin-top: 19rpx;
			display: flex;
			align-items: center;
			flex-wrap: wrap;
			@include flex-gap(0, 38rpx); // 替换了 column-gap

			.color-item {
				width: 48rpx;
				height: 48rpx;
				border-radius: 50%;

				&.colorActive {
					width: 37rpx;
					height: 37rpx;
					position: relative;

					&::after {
						content: '';
						position: absolute;
						width: 12rpx;
						height: 12rpx;
						border-radius: 50%;
						background-color: red;
					}
				}
			}
		}
	}

	.uni-section-box {
		margin-bottom: 31rpx;

		::v-deep .uni-section-header {
			padding: 0 !important;
			font-weight: bold;
		}

		.version-list {
			margin-top: 19rpx;
			display: flex;
			align-items: center;
			flex-wrap: wrap;
			@include flex-gap(15rpx); // 替换了 gap 双值
			box-sizing: border-box;

			.disabled {
				opacity: 0.4;
				position: relative;
				pointer-events: auto;
			}

			.disabled::after {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: rgba(128, 128, 128, 0.4);
				border-radius: inherit;
				z-index: 1;
			}

			.tagItem {
				width: auto;
				padding: 10rpx 15rpx;
				box-sizing: border-box;
				border-radius: 12rpx;
				font-size: 29rpx;
				color: #8C8C8C;
				border: 2rpx solid #E2E2E2;

				&.on {
					color: #FFFFFF;
					background: #FF5A1E;
					border-color: #FF5A1E;
				}
			}

			.picTagItem {
				padding: 12rpx;
				border-radius: 50%;
				box-sizing: border-box;
				border: 4rpx solid transparent;
				transition: border-color .3s;

				&.on {
					border-color: #FF5A1E;
				}
			}


			// ::v-deep .u-tag {
			// 	height: 28px;
			// }

			// ::v-deep .u-tag--error {
			// 	background-color: #FF5A1E !important;
			// 	border-color: #FF5A1E !important;
			// 	color: #fff !important;

			// 	.u-tag__text--error {
			// 		font-size: 29rpx;
			// 		font-weight: bold;
			// 	}
			// }

			// ::v-deep .u-tag--error--plain {
			// 	border-color: #E2E2E2 !important;

			// 	.u-tag__text--error--plain {
			// 		color: #8C8C8C !important;
			// 		font-size: 29rpx;
			// 		font-weight: bold;
			// 	}
			// }
		}
	}

	.Quantity-box {
		margin: 42rpx auto 94rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.Quantity-left {
			.main-title {
				font-weight: 500;
				font-size: 31rpx;
				color: #262626;
			}

			.intro {
				font-weight: 500;
				font-size: 25rpx;
				color: #8C8C8C;
			}
		}

		.Quantity-right {
			::v-deep .uni-numbox__plus {
				.uni-numbox--text {
					color: #FF5A1E !important;
					font-weight: bold;
				}
			}

			::v-deep .uni-numbox__value {
				width: 50rpx !important;
				height: 54rpx !important;
				border-radius: 12rpx 12rpx 12rpx 12rpx;
				border: 2rpx solid #E2E2E2;
				box-sizing: border-box;
				font-size: 30rpx;
			}

			::v-deep .uni-numbox--text {
				font-size: 38rpx;
				padding: 0 10rpx;
			}
		}

	}

	.confirm-btn {
		height: 112rpx;
		border-radius: 31rpx;
		font-weight: bold;
		font-size: 35rpx;
		color: #FFFFFF;
		font-family: PingFang SC-Bold !important;
	}
}
</style>