/**
 * APP桥接工具 - 统一处理Android和iOS的原生功能调用
 */

/**
 * 环境配置常量
 */
const DEBUG = process.env.NODE_ENV === "development";

/**
 * 判断当前平台
 * @returns {string} 平台标识符
 */
const getPlatform = () => {
  try {
    const platform = uni.getSystemInfoSync().platform;
    return platform;
  } catch (e) {
    console.error("获取平台信息失败:", e);
    return "unknown";
  }
};

// 缓存平台类型，避免重复判断
const PLATFORM = getPlatform();
const IS_ANDROID = PLATFORM === "android";
const IS_IOS = PLATFORM === "ios";
const IS_APP = IS_ANDROID || IS_IOS;

/**
 * 统一日志处理
 * @param {...any} args - 日志参数
 */
const log = (...args) => DEBUG && console.log("[AppBridge]", ...args);
const warn = (...args) => DEBUG && console.warn("[AppBridge]", ...args);
const error = (...args) => console.error("[AppBridge]", ...args);

/**
 * 检查桥接可用性
 * @returns {boolean} 是否可用
 */
const checkBridgeAlive = () => {
  try {
    if (IS_ANDROID) {
      return !!window.zlbridge;
    } else if (IS_IOS) {
      return !!window.webkit && !!window.webkit.messageHandlers;
    }
    return false;
  } catch (e) {
    error("检测桥接状态失败:", e);
    return false;
  }
};

// 桥接是否可用
let bridgeReady = checkBridgeAlive();

/**
 * 回调管理器
 */
const callbackManager = {
  _callbacks: new Map(),

  /**
   * 设置回调
   * @param {string} key - 回调名称
   * @param {Function} fn - 回调函数
   */
  set(key, fn) {
    this._callbacks.set(key, fn);
  },

  /**
   * 调用回调
   * @param {string} key - 回调名称
   * @param {any} data - 回调数据
   */
  invoke(key, data) {
    const fn = this._callbacks.get(key);
    if (fn) {
      fn(data);
      // 调用后立即清理，避免内存泄漏
      this._callbacks.delete(key);
    }
  },
};

/**
 * 事件监听器
 */
const eventListeners = new Map();

/**
 * 处理原生事件
 * @param {string} eventName - 事件名称
 * @param {any} eventData - 事件数据
 */
const handleNativeEvent = (eventName, eventData) => {
  if (!eventName) return;

  const listeners = eventListeners.get(eventName) || [];
  if (listeners.length > 0) {
    log(`触发事件: ${eventName}`, eventData);
    listeners.forEach((callback) => {
      try {
        callback(eventData);
      } catch (e) {
        error(`事件处理异常: ${eventName}`, e);
      }
    });
  }
};

/**
 * 初始化iOS桥接回调
 */
const initIosCallbacks = () => {
  if (!IS_IOS) return;

  // 注册回调函数
  window.getToken = (token) => {
    callbackManager.invoke("getToken", token);
  };

  window.getStatusBarHeight = (height) => {
    callbackManager.invoke("getStatusBarHeight", height);
  };

  window.getBaseUrl = (url) => {
    callbackManager.invoke("getBaseUrl", url);
  };

  // 注册事件回调
  window.onNativeEvent = (eventName, eventData) => {
    handleNativeEvent(eventName, eventData);
  };

  log("iOS回调已注册");
};
// 初始化iOS回调
initIosCallbacks();

/**
 * 统一发送请求到原生端
 * @param {string} method - 方法名称
 * @param {any} [payload=null] - 请求参数
 * @returns {Promise<any>} 原生响应结果
 */
export default function appBridge(method, payload = null) {
  console.log("🚀 ~ file: appBridge.js:151 ~ method:", method)
  // 首先检查桥接状态
  if (!bridgeReady) {
    bridgeReady = checkBridgeAlive();
    if (!bridgeReady) {
      warn(`桥接当前不可用，无法调用: ${method}`);
      return Promise.reject("BRIDGE_NOT_READY");
    }
  }

  if (IS_ANDROID) {
    try {
      // 根据是否有参数决定调用方式
      let result;
      if (payload !== null && payload !== undefined) {
        // 判断参数类型，对象类型需要转为 JSON 字符串
        if (typeof payload === "object") {
          result = window.zlbridge?.[method](JSON.stringify(payload));
        } else {
          result = window.zlbridge?.[method](payload);
        }
      } else {
        result = window.zlbridge?.[method]();
      }

      console.log(`Android调用成功: ${method}`);
      return Promise.resolve(result);
    } catch (e) {
      error(`Android调用失败: ${method}`, e);
      return Promise.reject(e);
    }
  } else if (IS_IOS) {
    return new Promise((resolve, reject) => {
      // 设置回调
      callbackManager.set(method, (result) => {
        log(`iOS调用成功: ${method}`);
        resolve(result);
      });

      try {
        // 发送消息
        window.webkit?.messageHandlers[method].postMessage(payload);
      } catch (e) {
        callbackManager.invoke(method, null); // 清理回调
        error(`iOS调用失败: ${method}`, e);
        reject(e);
      }
    });
  }

  // 非APP环境
  return Promise.reject("NOT_IN_APP_ENVIRONMENT");
}

/**
 * 获取状态栏高度
 * @param {Function} callback - 回调函数，接收状态栏高度
 * @returns {Promise<number>} 状态栏高度
 */
export function getStatusBarHeight(callback) {
  return new Promise((resolve, reject) => {
    if (IS_ANDROID) {
      try {
        // Android 平台直接调用
        const height = window.zlbridge?.getSystemStatusHeight?.() || 0;
        log(`Android获取状态栏高度成功: ${height}`);
        if (callback) callback(height);
        resolve(height);
      } catch (e) {
        error("Android获取状态栏高度失败:", e);
        reject(e);
      }
    } else if (IS_IOS) {
      // iOS 平台使用回调方式
      callbackManager.set("getStatusBarHeight", (height) => {
        const safeHeight = height || 0;
        log(`iOS获取状态栏高度成功: ${safeHeight}`);
        if (callback) callback(safeHeight);
        resolve(safeHeight);
      });

      try {
        window.webkit?.messageHandlers.getStatusBarHeight.postMessage(null);
      } catch (e) {
        callbackManager.invoke("getStatusBarHeight", null); // 清理回调
        error("iOS获取状态栏高度失败:", e);
        reject(e);
      }
    } else {
      // 非APP环境，使用uni-app的系统信息
      try {
        const systemInfo = uni.getSystemInfoSync();
        const height = systemInfo.statusBarHeight || 0;
        log(`非APP环境获取状态栏高度: ${height}`);
        if (callback) callback(height);
        resolve(height);
      } catch (e) {
        error("获取系统信息失败:", e);
        reject(e);
      }
    }
  });
}
