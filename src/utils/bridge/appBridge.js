/**
 * APP桥接工具 - 统一处理Android和iOS的原生功能调用
 */

/**
 * 环境配置常量
 */
const DEBUG = process.env.NODE_ENV === "development";

/**
 * 判断当前平台
 * @returns {string} 平台标识符
 */
const getPlatform = () => {
  try {
    const platform = uni.getSystemInfoSync().platform;
    return platform;
  } catch (e) {
    console.error("获取平台信息失败:", e);
    return "unknown";
  }
};

// 缓存平台类型，避免重复判断
const PLATFORM = getPlatform();
const IS_ANDROID = PLATFORM === "android";
const IS_IOS = PLATFORM === "ios";
const IS_APP = IS_ANDROID || IS_IOS;

/**
 * 统一日志处理
 * @param {...any} args - 日志参数
 */
const log = (...args) => DEBUG && console.log("[AppBridge]", ...args);
const warn = (...args) => DEBUG && console.warn("[AppBridge]", ...args);
const error = (...args) => console.error("[AppBridge]", ...args);

/**
 * 检查桥接可用性
 * @returns {boolean} 是否可用
 */
const checkBridgeAlive = () => {
  try {
    if (IS_ANDROID) {
      return !!window.zlbridge;
    } else if (IS_IOS) {
      return !!window.webkit && !!window.webkit.messageHandlers;
    }
    return false;
  } catch (e) {
    error("检测桥接状态失败:", e);
    return false;
  }
};

// 桥接是否可用
let bridgeReady = checkBridgeAlive();

/**
 * 回调管理器
 */
const callbackManager = {
  _callbacks: new Map(),

  /**
   * 设置回调
   * @param {string} key - 回调名称
   * @param {Function} fn - 回调函数
   */
  set(key, fn) {
    this._callbacks.set(key, fn);
  },

  /**
   * 调用回调
   * @param {string} key - 回调名称
   * @param {any} data - 回调数据
   */
  invoke(key, data) {
    const fn = this._callbacks.get(key);
    if (fn) {
      fn(data);
      // 调用后立即清理，避免内存泄漏
      this._callbacks.delete(key);
    }
  },
};

/**
 * 事件监听器
 */
const eventListeners = new Map();

/**
 * 处理原生事件
 * @param {string} eventName - 事件名称
 * @param {any} eventData - 事件数据
 */
const handleNativeEvent = (eventName, eventData) => {
  if (!eventName) return;

  const listeners = eventListeners.get(eventName) || [];
  if (listeners.length > 0) {
    log(`触发事件: ${eventName}`, eventData);
    listeners.forEach((callback) => {
      try {
        callback(eventData);
      } catch (e) {
        error(`事件处理异常: ${eventName}`, e);
      }
    });
  }
};

/**
 * 初始化通用桥接回调（iOS和Android通用）
 */
const initCommonCallbacks = () => {
  // 注册通用回调函数
  window.getToken = (token) => {
    callbackManager.invoke("getToken", token);
  };

  window.getStatusBarHeight = (height) => {
    callbackManager.invoke("getStatusBarHeight", height);
  };

  window.getBaseUrl = (url) => {
    callbackManager.invoke("getBaseUrl", url);
  };
  // 注册事件回调
  window.onNativeEvent = (eventName, eventData) => {
    handleNativeEvent(eventName, eventData);
  };

  log("通用回调已注册");
};
// 初始化通用回调
initCommonCallbacks();

/**
 * 统一发送请求到原生端
 * @param {string} method - 方法名称
 * @param {any} [payload=null] - 请求参数
 * @returns {Promise<any>} 原生响应结果
 */
export default function appBridge(method, payload = null) {
  log(`调用原生方法: ${method}`, payload);

  // 首先检查桥接状态
  if (!bridgeReady) {
    bridgeReady = checkBridgeAlive();
    if (!bridgeReady) {
      warn(`桥接当前不可用，无法调用: ${method}`);
      return
    }
  }

  if (IS_ANDROID) {
    try {
      // 根据是否有参数决定调用方式
      let result;
      if (payload !== null && payload !== undefined) {
        // 判断参数类型，对象类型需要转为 JSON 字符串
        if (typeof payload === "object") {
          result = window.zlbridge?.[method](JSON.stringify(payload));
        } else {
          result = window.zlbridge?.[method](payload);
        }
      } else {
        result = window.zlbridge?.[method]();
      }

      log(`Android调用成功: ${method}`);
      return Promise.resolve(result);
    } catch (e) {
      error(`Android调用失败: ${method}`, e);
      return Promise.reject(e);
    }
  } else if (IS_IOS) {
    return new Promise((resolve, reject) => {
      // 设置超时处理
      const timeoutId = setTimeout(() => {
        callbackManager.invoke(method, null); // 清理回调
        reject(new Error(`iOS调用超时: ${method}`));
      }, 10000); // 10秒超时

      // 设置回调
      callbackManager.set(method, (result) => {
        clearTimeout(timeoutId);
        log(`iOS调用成功: ${method}`);
        resolve(result);
      });

      try {
        // 发送消息
        window.webkit?.messageHandlers[method].postMessage(payload);
      } catch (e) {
        clearTimeout(timeoutId);
        callbackManager.invoke(method, null); // 清理回调
        error(`iOS调用失败: ${method}`, e);
        reject(e);
      }
    });
  }

  // 非APP环境
  return Promise.reject(new Error("NOT_IN_APP_ENVIRONMENT"));
}

/**
 * 获取状态栏高度
 * @param {Function} callback - 回调函数，接收状态栏高度
 * @returns {Promise<number>} 状态栏高度
 */
export function getStatusBarHeight(callback) {
  // 非APP环境，直接使用uni-app的系统信息，避免桥接调用
  if (!IS_APP) {
    return new Promise((resolve) => {
      try {
        const systemInfo = uni.getSystemInfoSync();
        const height = systemInfo.statusBarHeight || 0;
        log(`非APP环境获取状态栏高度: ${height}`);
        if (callback) callback(height);
        resolve(height);
      } catch (e) {
        // 电脑环境获取失败时，返回默认值，不报错
        const defaultHeight = 0;
        log(`电脑环境使用默认状态栏高度: ${defaultHeight}`);
        if (callback) callback(defaultHeight);
        resolve(defaultHeight);
      }
    });
  }

  // APP环境才使用桥接
  if (IS_ANDROID) {
    // Android 使用专用方法名
    return appBridge("getSystemStatusHeight").then(height => {
      const safeHeight = height || 0;
      if (callback) callback(safeHeight);
      return safeHeight;
    }).catch(e => {
      error("Android获取状态栏高度失败:", e);
      const defaultHeight = 0;
      if (callback) callback(defaultHeight);
      return defaultHeight;
    });
  } else if (IS_IOS) {
    // iOS 使用统一桥接
    return appBridge("getStatusBarHeight").then(height => {
      const safeHeight = height || 0;
      if (callback) callback(safeHeight);
      return safeHeight;
    }).catch(e => {
      error("iOS获取状态栏高度失败:", e);
      const defaultHeight = 0;
      if (callback) callback(defaultHeight);
      return defaultHeight;
    });
  }

  // 兜底情况
  return Promise.resolve(0);
}

/**
 * 添加原生事件监听器
 * @param {string} eventName - 事件名称
 * @param {Function} callback - 事件回调函数
 */
export function addEventListener(eventName, callback) {
  if (!eventListeners.has(eventName)) {
    eventListeners.set(eventName, []);
  }
  eventListeners.get(eventName).push(callback);
  log(`添加事件监听器: ${eventName}`);
}

/**
 * 移除原生事件监听器
 * @param {string} eventName - 事件名称
 * @param {Function} callback - 要移除的回调函数
 */
export function removeEventListener(eventName, callback) {
  const listeners = eventListeners.get(eventName);
  if (listeners) {
    const index = listeners.indexOf(callback);
    if (index > -1) {
      listeners.splice(index, 1);
      log(`移除事件监听器: ${eventName}`);
    }
  }
}

/**
 * 检查桥接是否可用
 * @returns {boolean} 桥接可用状态
 */
export function isBridgeReady() {
  return bridgeReady;
}

/**
 * 获取当前平台信息
 * @returns {object} 平台信息
 */
export function getPlatformInfo() {
  return {
    platform: PLATFORM,
    isAndroid: IS_ANDROID,
    isIOS: IS_IOS,
    isApp: IS_APP,
    bridgeReady
  };
}

/**
 * 安全的桥接调用，在非APP环境下不会报错
 * @param {string} method - 方法名称
 * @param {any} payload - 请求参数
 * @param {any} defaultValue - 默认返回值
 * @returns {Promise<any>} 调用结果或默认值
 */
export function safeBridgeCall(method, payload = null, defaultValue = null) {
  if (!IS_APP) {
    log(`非APP环境，跳过桥接调用: ${method}`);
    return Promise.resolve(defaultValue);
  }

  return appBridge(method, payload).catch(e => {
    warn(`桥接调用失败，返回默认值: ${method}`, e);
    return defaultValue;
  });
}

/**
 * 主动调用原生端退出登录
 * @param {string} [url] - 可选的回调URL
 * @returns {Promise<any>} 调用结果
 */
export function loginOut(url) {
  log("主动调用原生退出登录:", url);
  return safeBridgeCall("loginOut", url);
}
