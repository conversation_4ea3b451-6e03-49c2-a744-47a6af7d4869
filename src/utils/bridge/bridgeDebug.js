/**
 * 桥接调试工具 - 检查Android和iOS桥接的可用方法
 */

export function debugBridge() {
  console.log('=== 桥接调试信息 ===');
  
  try {
    const systemInfo = uni.getSystemInfoSync();
    const isDev = process.env.NODE_ENV === 'development';
    
    console.log('平台信息:', systemInfo.platform);
    console.log('系统版本:', systemInfo.system);
    console.log('开发环境:', isDev);
    
    // 开发环境提示
    if (isDev && systemInfo.platform === 'web') {
      console.log('🚀 当前为开发环境，原生桥接方法不可用');
      console.log('📱 请在真机或模拟器中测试原生功能');
      return;
    }
    
    if (systemInfo.platform === 'android') {
      console.log('--- Android Bridge 调试 ---');
      
      if (window.zlbridge) {
        console.log('✅ window.zlbridge 存在');
        
        // 检查常用方法
        const methods = [
          'exitStore',
          'goPreviousPage', 
          'shareApp',
          'routeApp',
          'getSystemStatusHeight',
          'getBaseUrl',
          '_callNative'
        ];
        
        console.log('可用方法检查:');
        methods.forEach(method => {
          const exists = typeof window.zlbridge[method] === 'function';
          console.log(`  ${exists ? '✅' : '❌'} ${method}: ${typeof window.zlbridge[method]}`);
        });
        
        // 显示所有可用属性
        console.log('zlbridge 对象的所有属性:');
        Object.keys(window.zlbridge).forEach(key => {
          console.log(`  - ${key}: ${typeof window.zlbridge[key]}`);
        });
        
      } else {
        console.log('❌ window.zlbridge 不存在');
      }
      
    } else if (systemInfo.platform === 'ios') {
      console.log('--- iOS Bridge 调试 ---');
      
      if (window.webkit && window.webkit.messageHandlers) {
        console.log('✅ webkit messageHandlers 存在');
        
        const handlers = [
          'exitStore',
          'getToken',
          'getStatusBarHeight',
          'getBaseUrl'
        ];
        
        console.log('可用消息处理器:');
        handlers.forEach(handler => {
          const exists = !!window.webkit.messageHandlers[handler];
          console.log(`  ${exists ? '✅' : '❌'} ${handler}`);
        });
        
      } else {
        console.log('❌ webkit messageHandlers 不存在');
      }
    }
    
  } catch (error) {
    console.error('调试过程中出错:', error);
  }
  
  console.log('=== 调试信息结束 ===');
}

export function testExitStore() {
  console.log('=== 测试退出商城方法 ===');
  
  try {
    const systemInfo = uni.getSystemInfoSync();
    const isDev = process.env.NODE_ENV === 'development';
    
    // 开发环境处理
    if (isDev && systemInfo.platform === 'web') {
      console.log('🚀 开发环境模拟测试');
      uni.showModal({
        title: '调试测试',
        content: '开发环境中模拟退出商城\n(真机环境会调用原生方法)',
        showCancel: false,
        confirmText: '知道了'
      });
      return;
    }
    
    if (systemInfo.platform === 'android') {
      console.log('测试 Android 退出方法...');
      
      if (window.zlbridge) {
        if (typeof window.zlbridge.exitStore === 'function') {
          console.log('✅ exitStore 方法可用，即将调用...');
          window.zlbridge.exitStore();
        } else if (typeof window.zlbridge.goPreviousPage === 'function') {
          console.log('⚠️ exitStore 不可用，使用 goPreviousPage...');
          window.zlbridge.goPreviousPage();
        } else {
          console.log('❌ 两个方法都不可用');
        }
      } else {
        console.log('❌ zlbridge 不存在');
      }
      
    } else if (systemInfo.platform === 'ios') {
      console.log('测试 iOS 退出方法...');
      
      if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.exitStore) {
        console.log('✅ iOS exitStore 可用，即将调用...');
        window.webkit.messageHandlers.exitStore.postMessage(null);
      } else {
        console.log('❌ iOS exitStore 不可用');
      }
    }
    
  } catch (error) {
    console.error('测试过程中出错:', error);
  }
}

// 在控制台中暴露调试方法
if (typeof window !== 'undefined') {
  window.debugBridge = debugBridge;
  window.testExitStore = testExitStore;
} 