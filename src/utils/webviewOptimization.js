/**
 * WebView环境安全优化方案
 * 保持原有Three.js导入，通过其他方式优化性能
 */

// 1. Webpack配置优化已经在vue.config.js中设置
// 2. 代码分割配置已经启用  
// 3. 现在专注于运行时优化

class WebViewSafeOptimizer {
  constructor() {
    this.isInitialized = false
    this.performanceMarks = {}
    this.startTime = performance.now()
  }

  /**
   * 安全的Three.js优化 - 不改变导入方式
   */
  optimizeThreeJS() {
    // 等待Three.js加载完成后执行优化
    if (typeof window !== 'undefined' && window.THREE) {
      // 设置WebView友好的默认值
      if (window.THREE.WebGLRenderer) {
        const originalRenderer = window.THREE.WebGLRenderer
        window.THREE.WebGLRenderer = function(parameters = {}) {
          // WebView环境优化的默认参数
          const optimizedParams = {
            antialias: false,  // 关闭抗锯齿提升性能
            alpha: true,
            powerPreference: "high-performance",
            stencil: false,
            depth: true,
            logarithmicDepthBuffer: false,
            ...parameters
          }
          return new originalRenderer(optimizedParams)
        }
        // 保持原型链
        window.THREE.WebGLRenderer.prototype = originalRenderer.prototype
        window.THREE.WebGLRenderer.constructor = originalRenderer
      }

      console.log('✅ Three.js WebView优化已应用')
      return true
    }
    return false
  }

  /**
   * 3D场景性能优化
   */
  optimizeScene(scene, renderer, camera) {
    if (!scene || !renderer || !camera) return

    // 1. 设置优化的像素比
    const pixelRatio = Math.min(window.devicePixelRatio, 2)
    renderer.setPixelRatio(pixelRatio)

    // 2. 启用性能优化选项
    renderer.shadowMap.enabled = false // 关闭阴影提升性能
    renderer.physicallyCorrectLights = false

    // 3. 场景优化
    scene.fog = null // 移除雾效果
    scene.autoUpdate = false // 手动控制更新

    // 4. 相机优化
    camera.far = Math.min(camera.far, 100) // 减少远裁剪面距离

    console.log('✅ 3D场景性能优化已应用')
  }

  /**
   * 渐进式3D加载
   */
  async loadThreeDProgressive(container, modelUrl) {
    try {
      // 显示加载指示器
      this.showLoading(container, '正在初始化3D场景...')

      // 延迟加载，给主线程时间渲染其他UI
      await this.delay(100)

      // 检查Three.js是否已加载
      if (!window.THREE) {
        throw new Error('Three.js未加载')
      }

      // 应用性能优化
      this.optimizeThreeJS()

      // 创建基础场景
      const scene = new window.THREE.Scene()
      const camera = new window.THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000)
      const renderer = new window.THREE.WebGLRenderer()

      // 应用场景优化
      this.optimizeScene(scene, renderer, camera)

      // 添加到容器
      renderer.setSize(container.clientWidth, container.clientHeight)
      container.appendChild(renderer.domElement)

      this.hideLoading(container)
      console.log('✅ 3D场景初始化完成')

      return { scene, camera, renderer }

    } catch (error) {
      console.error('❌ 3D场景加载失败:', error)
      this.showError(container, '3D功能暂时不可用')
      throw error
    }
  }

  /**
   * 性能友好的延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 显示加载状态
   */
  showLoading(container, message = '加载中...') {
    const loadingEl = document.createElement('div')
    loadingEl.className = 'webview-loading'
    loadingEl.innerHTML = `
      <div style="
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        color: #666;
        font-size: 14px;
      ">
        <div style="
          width: 30px;
          height: 30px;
          border: 3px solid #f3f3f3;
          border-top: 3px solid #007AFF;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 10px;
        "></div>
        ${message}
      </div>
      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>
    `
    container.appendChild(loadingEl)
  }

  /**
   * 隐藏加载状态
   */
  hideLoading(container) {
    const loadingEl = container.querySelector('.webview-loading')
    if (loadingEl) {
      loadingEl.remove()
    }
  }

  /**
   * 显示错误状态
   */
  showError(container, message) {
    this.hideLoading(container)
    const errorEl = document.createElement('div')
    errorEl.innerHTML = `
      <div style="
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        color: #FF3B30;
        font-size: 14px;
      ">
        ⚠️ ${message}
      </div>
    `
    container.appendChild(errorEl)
  }

  /**
   * 内存清理
   */
  cleanupThreeJS(scene, renderer) {
    if (scene) {
      scene.traverse((object) => {
        if (object.geometry) {
          object.geometry.dispose()
        }
        if (object.material) {
          if (Array.isArray(object.material)) {
            object.material.forEach(material => material.dispose())
          } else {
            object.material.dispose()
          }
        }
      })
    }

    if (renderer) {
      renderer.dispose()
    }

    console.log('✅ Three.js资源已清理')
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    const report = {
      totalTime: performance.now() - this.startTime,
      memory: performance.memory ? {
        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB',
        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + 'MB'
      } : null,
      optimizations: [
        '✅ Webpack代码分割已启用',
        '✅ Source maps已禁用',
        '✅ 生产环境console已清理',
        '✅ 图片懒加载已启用',
        '✅ Three.js运行时优化已应用'
      ]
    }

    console.table(report)
    return report
  }
}

// 导出单例
export default new WebViewSafeOptimizer()