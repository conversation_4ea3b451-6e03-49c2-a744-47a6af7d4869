import JSEncrypt from 'jsencrypt';

const publicKey = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAqBJjxo8Zs8zAIoTFIybU
6t9oTmuHOzlTsomPTCT6vBv6AZ+BTSAJAIKMUTRmApIJ92Mn+7oWQj+6bK9jn750
4gRdWpvpRJvGWFq38KhH92q2BXGZAbTw5hNvSaE1RjP8+vLF7paXvmDukw5IFA81
eQ36GjEw/H6bwf0OvSai2aGuZNGqL01rTwt3+UdybullZGTo5tXp6VOxbrfD/eKj
w0KQTzSwyYLDrWttiqjJxiZ9I465H8WVqAjjPTEiO+dSsds3jjW07RnKzAu2no4/
lAwpSiwJnfKPAzn9CEQDMkvm5UnGZO85EiMH6bvrrsbEVq5qd7biK8GBiZp+pBmI
JQIDAQAB
-----END PUBLIC KEY-----`;

export function rsaEncrypt(data) {
  const encryptor = new JSEncrypt();
  encryptor.setPublicKey(publicKey);
  return encryptor.encrypt(data);
}