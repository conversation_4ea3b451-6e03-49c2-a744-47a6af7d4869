import axios from "axios";
import store from "@/store";
import config from "@/config";
console.log("🚀 ~ file: request.js:4 ~ config:", config);
import appBridge from "@/utils/bridge/appBridge";
import { getToken } from "@/utils/security/auth";
import errorCode from "@/utils/errorCode";
import { toast, showConfirm, tansParams } from "@/utils/common";

/**
 * 请求工具使用说明：
 *
 * 1. 默认智能返回：
 *    - 如果响应中有 data.data，返回 data.data
 *    - 如果 data.data 不存在但有业务字段，返回 data
 *    - 其他情况返回 data.data 或空对象
 *
 * 2. 强制返回原始 data：
 *    方法1：在请求配置中设置 returnRawData: true
 *    方法2：使用辅助函数 getRawData、postRawData、createRawDataRequest
 *
 * 使用示例：
 * // 智能返回（推荐）
 * service.get('/api/users')
 *
 * // 强制返回原始data - 方法1
 * service.get('/api/users', { returnRawData: true })
 *
 * // 强制返回原始data - 方法2
 * getRawData('/api/users')
 * postRawData('/api/users', userData)
 */

// 默认baseUrl，使用配置文件中的值
let cachedBaseUrl = config.baseURL;

// 异步获取baseUrl，返回Promise
let fetchBaseUrl = async function () {
  try {
    // 尝试从原生APP获取baseUrl
    const appBaseUrl = await appBridge.getBaseUrl();
    if (appBaseUrl) {
      cachedBaseUrl = appBaseUrl;
      return appBaseUrl;
    }
  } catch (e) {
    console.log("从APP获取baseUrl失败:", e);
  }
  console.log("使用默认baseUrl:", cachedBaseUrl);
  return cachedBaseUrl;
};

// 启动时尝试预加载baseUrl
fetchBaseUrl();

// 同步获取baseUrl，返回当前缓存的值
let getBaseUrl = function () {
  return cachedBaseUrl;
};

// 提供一个方法在运行时更新baseUrl
export function setAppBaseUrl(url) {
  if (url && typeof url === "string") {
    cachedBaseUrl = url;
    console.log("手动更新baseUrl:", cachedBaseUrl);
    return true;
  }
  return false;
}

const service = axios.create({
  baseURL: getBaseUrl(),
  timeout: 15 * 1000,
  headers: {
    "Content-Type": "application/x-www-form-urlencoded",
  },
});
const handleToken = (token) => {
  console.log(123, token);
  return token;
};
service.interceptors.request.use(
  (config) => {
    const isToken = (config.headers || {}).isToken === false;
    window.getToken = handleToken;
    window.webkit?.messageHandlers.getToken.postMessage(null);

    if (getToken() && !isToken) {
      config.headers["Authorization"] = handleToken(getToken());
    }

    if (config.contentType) {
      config.headers["Content-Type"] = config.contentType;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

service.interceptors.response.use(
  (res) => {
    let { errMsg, data } = res;

    const code = data.status || data.code || 200;
    const msg = errorCode[code] || data.message || errorCode["default"];

    // 检查是否需要返回原始data（通过请求配置指定）
    const returnRawData = res.config.returnRawData === true;

    if (code === 401) {
      showConfirm("登录状态已过期，您可以继续留在该页面，或者重新登录?").then(
        (res) => {
          if (res.confirm) {
            store.dispatch("LogOut").then((res) => {
              uni.reLaunch({
                url: "/pages/login",
              });
            });
          }
        }
      );
      return Promise.reject("无效的会话，或者会话已过期，请重新登录。");
    } else if (code === 500) {
      toast({
        title: msg,
      });
      return Promise.reject("500");
    } else if (code !== 200) {
      toast({
        title: msg,
      });
      return Promise.reject(code);
    }

    // 如果配置了returnRawData，直接返回data
    if (returnRawData) {
      return data;
    }

    // 智能判断返回格式
    // 如果data.data存在且不为null/undefined，返回data.data
    // 否则返回data（兼容直接在data中返回业务数据的接口）
    if (data.data !== undefined && data.data !== null) {
      return data.data;
    } else {
      // 如果data中有业务字段（除了status、code、message等系统字段），直接返回data
      const systemFields = [
        "status",
        "code",
        "message",
        "msg",
        "errMsg",
        "timestamp",
      ];
      const businessFields = Object.keys(data).filter(
        (key) => !systemFields.includes(key)
      );

      if (businessFields.length > 0) {
        return data;
      }

      // 默认返回data.data，如果不存在则返回空对象
      return data.data || {};
    }
  },
  (error) => {
    let { message } = error;

    if (message === "Network Error") {
      message = "Interface connection anomaly";
    } else if (message?.includes("timeout")) {
      message = "Interface request timeout";
    } else if (message?.includes("Request failed with status code")) {
      message = "系统接口" + message.substr(message.length - 3) + "异常";
    }

    toast({
      title: message,
    });

    return Promise.reject(error);
  }
);

// 辅助函数：创建返回原始data的请求
export function createRawDataRequest(url, options = {}) {
  return service({
    url,
    returnRawData: true,
    ...options,
  });
}

// 辅助函数：POST请求返回原始data
export function postRawData(url, data, config = {}) {
  return service({
    method: "post",
    url,
    data,
    returnRawData: true,
    ...config,
  });
}

// 辅助函数：GET请求返回原始data
export function getRawData(url, config = {}) {
  return service({
    method: "get",
    url,
    returnRawData: true,
    ...config,
  });
}

export default service;
