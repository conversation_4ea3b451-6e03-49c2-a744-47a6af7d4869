import axios from "axios";
import config from "@/config";
import { getToken } from "@/utils/security/auth";
import errorCode from "@/utils/errorCode";
import { toast, tansParams } from "@/utils/common";

/**
 * 请求工具使用说明：
 *
 * 1. 默认智能返回：
 *    - 如果响应中有 data.data，返回 data.data
 *    - 如果 data.data 不存在但有业务字段，返回 data
 *    - 其他情况返回 data.data 或空对象
 *
 * 2. 强制返回原始 data：
 *    方法1：在请求配置中设置 returnRawData: true
 *    方法2：使用辅助函数 getRawData、postRawData、createRawDataRequest
 *
 * 使用示例：
 * // 智能返回（推荐）
 * service.get('/api/users')
 *
 * // 强制返回原始data - 方法1
 * service.get('/api/users', { returnRawData: true })
 *
 * // 强制返回原始data - 方法2
 * getRawData('/api/users')
 * postRawData('/api/users', userData)
 */

// 🚨 401 拦截器状态管理，防止重复调用
let isLogoutInProgress = false;

const service = axios.create({
  baseURL: config.baseURL,
  timeout: 15 * 1000,
  headers: {
    "Content-Type": "application/x-www-form-urlencoded",
  },
});
const handleToken = (token) => {
  console.log(123, token);
  return token;
};
service.interceptors.request.use(
  (config) => {
    const isToken = (config.headers || {}).isToken === false;
    window.getToken = handleToken;
    window.webkit?.messageHandlers.getToken.postMessage(null);

    if (getToken() && !isToken) {
      config.headers["Authorization"] = handleToken(getToken());
    }

    if (config.contentType) {
      config.headers["Content-Type"] = config.contentType;
    }
    config.headers["HTTP_X_FROM_APP"] = window.zlbridge;
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);
var loginOut = () => {
  console.log('退出登录')
}
const platForm = uni.getDeviceInfo().platform;
service.interceptors.response.use(
  (res) => {
    let { errMsg, data } = res;

    const code = data.status || data.code || 200;
    const msg = errorCode[code] || data.message || errorCode["default"];

    // 检查是否需要返回原始data（通过请求配置指定）
    const returnRawData = res.config.returnRawData === true;

    // 🚨 关键修改：如果配置了returnRawData，跳过错误处理，直接返回原始数据
    if (returnRawData) {
      return data;
    }

    if (code === 401) {
      // 🚨 防止多个401请求同时触发退出登录流程
      if (isLogoutInProgress) {
        console.log('🔐 退出登录流程已在进行中，跳过重复处理');
        return Promise.reject("无效的会话，或者会话已过期，请重新登录。");
      }

      isLogoutInProgress = true;
      console.log('🔐 检测到401状态码，开始退出登录流程');
      // 优先尝试调用 APP 的 loginOut 方法
      try {
        if (platForm === 'android') {
          window?.zlbridge?.loginOut?.();

        } else if (platForm === 'ios') {
          window.webkit?.messageHandlers.loginOut.postMessage(null);
        }
        isLogoutInProgress = false
        return Promise.reject("无效的会话，或者会话已过期，请重新登录。");
      } catch (error) {
        isLogoutInProgress = false
      }
      return Promise.reject("无效的会话，或者会话已过期，请重新登录。");
    } else if (code === 500) {
      toast({
        title: msg,
      });
      return Promise.reject("500");
    } else if (code !== 200) {
      toast({
        title: msg,
      });
      return Promise.reject(code);
    }

    // 智能判断返回格式
    // 如果data.data存在且不为null/undefined，返回data.data
    // 否则返回data（兼容直接在data中返回业务数据的接口）
    if (data.data !== undefined && data.data !== null) {
      return data.data;
    } else {
      // 如果data中有业务字段（除了status、code、message等系统字段），直接返回data
      const systemFields = [
        "status",
        "code",
        "message",
        "msg",
        "errMsg",
        "timestamp",
      ];
      const businessFields = Object.keys(data).filter(
        (key) => !systemFields.includes(key)
      );

      if (businessFields.length > 0) {
        return data;
      }

      // 默认返回data.data，如果不存在则返回空对象
      return data.data || {};
    }
  },
  (error) => {
    let { message } = error;

    if (message === "Network Error") {
      message = "Interface connection anomaly";
    } else if (message?.includes("timeout")) {
      message = "Interface request timeout";
    } else if (message?.includes("Request failed with status code")) {
      message = "系统接口" + message.substr(message.length - 3) + "异常";
    }

    toast({
      title: message,
    });

    return Promise.reject(error);
  }
);

// 辅助函数：创建返回原始data的请求
export function createRawDataRequest(url, options = {}) {
  return service({
    url,
    returnRawData: true,
    ...options,
  });
}

// 辅助函数：POST请求返回原始data
export function postRawData(url, data, config = {}) {
  return service({
    method: "post",
    url,
    data,
    returnRawData: true,
    ...config,
  });
}

// 辅助函数：GET请求返回原始data
export function getRawData(url, config = {}) {
  return service({
    method: "get",
    url,
    returnRawData: true,
    ...config,
  });
}

export default service;
