/**
 * 显示消息提示框
 * @param content 提示的标题
 */
export function toast({
	className = 'my-toast-box',
	icon = 'none',
	title = '提示',
	image = '',
	duration = 1500,
	mask = false
} = {}) {
	uni.showToast({
		class: className,
		icon,
		image,
		title,
		duration,
		mask
	})
}

/**
 * 显示模态弹窗
 * @param content 提示的标题
 */
export function showConfirm(content) {
	return new Promise((resolve, reject) => {
		uni.showModal({
			title: '提示',
			content: content,
			cancelText: '取消',
			confirmText: '确定',
			success: function(res) {
				resolve(res)
			}
		})
	})
}

/**
 * 参数处理
 * @param params 参数
 */
export function tansParams(params) {
	let result = ''
	for (const propName of Object.keys(params)) {
		const value = params[propName]
		var part = encodeURIComponent(propName) + "="
		// if (value !== null && value !== "" && typeof(value) !== "undefined") {
		if (typeof value === 'object') {
			for (const key of Object.keys(value)) {
				if (value[key] !== null && value[key] !== "" && typeof(value[key]) !== 'undefined') {
					let params = propName + '[' + key + ']'
					var subPart = encodeURIComponent(params) + "="
					result += subPart + encodeURIComponent(value[key]) + "&"
				}
			}
		} else {
			result += part + encodeURIComponent(value) + "&"
		}
		// }
	}
	return result
}

/**
 * 空值判断
 */
export function isEmpty(obj) {
	if (
		typeof obj == "undefined" ||
		obj === null ||
		(typeof obj == "string" && obj.trim() == "") ||
		(typeof obj == "object" && Object.keys(obj).length === 0)
	) {
		return true;
	} else {
		return false;
	}
}

/**
 * 提示信息
 */
export function uToast({
	type = "success",
	position = "top",
	message = "提示信息",
	...params
} = {}) {
	this.$refs.uToast.show({
		type,
		position,
		message,
		...params
	})
}

/**
 * 表单重置
 */
export function resetForm(refName) {
	if (this.$refs[refName]) {
		this.$refs[refName].resetFields();
	}
}

/**
 * 图片预览
 */
export function previewImage(urls, current = 0) {
	uni.previewImage({
		urls,
		current,
		loop: true,
	})
}

/**
 * loading 提示框
 */

export function showLoading(title = 'Loading...') {
	uni.showLoading({
		title,
		mask: true
	})
}

export function hideLoading() {
	uni.hideLoading()
}

export function throttle(fn, delay) {
	var lastArgs;
	var timer;
	var delay = delay || 200;
	return function(...args) {
		lastArgs = args;
		if (!timer) {
			timer = setTimeout(() => {
				timer = null;
				fn.apply(this, lastArgs);
			}, delay);
		}
	}
}