/**
 * 键盘检测工具使用示例
 * 展示如何在不同场景下使用 keyboardDetector
 */

import { useKeyboardDetector, useSimpleKeyboardDetector, keyboardDetectorMixin } from './keyboardDetector.js';

// ========== 示例 1: 在普通 JavaScript 中使用 ==========
function example1() {
  const cleanup = useKeyboardDetector((isVisible, height) => {
    if (isVisible) {
      console.log('✅ 键盘弹出，高度：' + height + 'px');
      // 隐藏底部按钮
      document.querySelector('.submit-button').style.display = 'none';
    } else {
      console.log('❌ 键盘收起');
      // 显示底部按钮
      document.querySelector('.submit-button').style.display = 'block';
    }
  }, {
    threshold: 150 // 自定义检测阈值
  });
  
  // 在页面卸载时清理
  window.addEventListener('beforeunload', cleanup);
}

// ========== 示例 2: 在 Vue 组件中使用混入 ==========
export const AddressFormComponent = {
  mixins: [keyboardDetectorMixin],
  
  data() {
    return {
      isButtonVisible: true
    };
  },
  
  methods: {
    // 重写键盘显示回调
    onKeyboardShow(height) {
      console.log('键盘弹出，高度：', height);
      this.isButtonVisible = false;
      
      // 可以根据键盘高度调整页面布局
      this.$refs.submitButton.$el.style.marginBottom = height + 'px';
    },
    
    // 重写键盘隐藏回调
    onKeyboardHide() {
      console.log('键盘收起');
      this.isButtonVisible = true;
      
      // 恢复页面布局
      this.$refs.submitButton.$el.style.marginBottom = '0px';
    }
  },
  
  template: `
    <view>
      <input type="text" placeholder="请输入地址" />
      <button 
        ref="submitButton"
        v-show="isButtonVisible"
        class="submit-button"
      >
        确认
      </button>
    </view>
  `
};

// ========== 示例 3: 在 Vue 组件中手动使用 ==========
export const ManualUsageComponent = {
  data() {
    return {
      keyboardCleanup: null,
      isKeyboardVisible: false
    };
  },
  
  mounted() {
    this.keyboardCleanup = useKeyboardDetector((isVisible, height) => {
      this.isKeyboardVisible = isVisible;
      
      if (isVisible) {
        // 键盘弹出时的处理逻辑
        this.handleKeyboardShow(height);
      } else {
        // 键盘收起时的处理逻辑
        this.handleKeyboardHide();
      }
    });
  },
  
  beforeDestroy() {
    if (this.keyboardCleanup) {
      this.keyboardCleanup();
    }
  },
  
  methods: {
    handleKeyboardShow(height) {
      // 自定义处理逻辑
      console.log('键盘显示，高度：', height);
    },
    
    handleKeyboardHide() {
      // 自定义处理逻辑
      console.log('键盘隐藏');
    }
  }
};

// ========== 示例 4: 简化版本使用 ==========
function example4() {
  const cleanup = useSimpleKeyboardDetector((isVisible) => {
    const submitButton = document.querySelector('.submit-button');
    if (submitButton) {
      submitButton.style.display = isVisible ? 'none' : 'block';
    }
  });
  
  return cleanup;
}

// ========== 示例 5: 在 addAddress.vue 中的应用 ==========
export const AddAddressUsage = {
  mixins: [keyboardDetectorMixin],
  
  data() {
    return {
      isButtonVisible: true,
      // 其他现有数据...
    };
  },
  
  methods: {
    onKeyboardShow(height) {
      // 替换原有的键盘检测逻辑
      this.isButtonVisible = false;
      console.log('键盘弹出，高度：', height + 'px');
    },
    
    onKeyboardHide() {
      // 替换原有的键盘检测逻辑
      this.isButtonVisible = true;
      console.log('键盘收起');
    }
    
    // 移除原有的 handleFocusIn, handleFocusOut 等方法
    // 移除原有的 beforeDestroy 中的事件清理代码
  }
};

// ========== 使用建议 ==========
/*
1. 对于简单的显示/隐藏需求，使用 useSimpleKeyboardDetector
2. 对于需要获取键盘高度的场景，使用 useKeyboardDetector
3. 对于 Vue 组件，推荐使用 keyboardDetectorMixin
4. 记得在组件销毁时调用清理函数，避免内存泄漏
5. 可以通过 options.threshold 自定义键盘检测的敏感度
*/