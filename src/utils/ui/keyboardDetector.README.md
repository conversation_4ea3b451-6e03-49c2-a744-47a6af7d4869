# 键盘检测工具 (Keyboard Detector)

一个跨平台的移动端虚拟键盘检测工具，支持 VirtualKeyboard API 和降级方案。

## 特性

- ✅ **跨浏览器兼容**：支持 Chrome 94+、Safari、Firefox 等
- ✅ **自动降级**：优先使用 VirtualKeyboard API，自动降级到传统方案
- ✅ **Vue 友好**：提供 Vue 混入，开箱即用
- ✅ **内存安全**：自动清理事件监听器，防止内存泄漏
- ✅ **可配置**：支持自定义检测阈值
- ✅ **TypeScript 友好**：完整的类型定义

## 快速开始

### 1. 在 Vue 组件中使用混入（推荐）

```javascript
import { keyboardDetectorMixin } from '@/utils/keyboardDetector.js';

export default {
  mixins: [keyboardDetectorMixin],
  
  data() {
    return {
      isButtonVisible: true
    };
  },
  
  methods: {
    // 重写键盘显示回调
    onKeyboardShow(height) {
      console.log('键盘弹出，高度：', height);
      this.isButtonVisible = false;
    },
    
    // 重写键盘隐藏回调
    onKeyboardHide() {
      console.log('键盘收起');
      this.isButtonVisible = true;
    }
  }
};
```

### 2. 手动使用

```javascript
import { useKeyboardDetector } from '@/utils/keyboardDetector.js';

// 在组件的 mounted 钩子中
mounted() {
  this.keyboardCleanup = useKeyboardDetector((isVisible, height) => {
    if (isVisible) {
      console.log('键盘显示，高度：', height);
      // 隐藏底部按钮
      this.isButtonVisible = false;
    } else {
      console.log('键盘隐藏');
      // 显示底部按钮
      this.isButtonVisible = true;
    }
  }, {
    threshold: 150 // 自定义检测阈值
  });
},

// 在组件的 beforeDestroy 钩子中
beforeDestroy() {
  if (this.keyboardCleanup) {
    this.keyboardCleanup();
  }
}
```

### 3. 简化版本（仅检测显示/隐藏）

```javascript
import { useSimpleKeyboardDetector } from '@/utils/keyboardDetector.js';

const cleanup = useSimpleKeyboardDetector((isVisible) => {
  document.querySelector('.submit-button').style.display = 
    isVisible ? 'none' : 'block';
});

// 记得在适当时机调用清理函数
// cleanup();
```

## API 文档

### useKeyboardDetector(callback, options)

主要的键盘检测函数。

**参数：**
- `callback(isVisible, height)`: 回调函数
  - `isVisible`: boolean - 键盘是否可见
  - `height`: number - 键盘高度（像素）
- `options`: object - 配置选项
  - `threshold`: number - 检测阈值，默认 100px

**返回值：**
- `Function`: 清理函数，用于移除事件监听器

### useSimpleKeyboardDetector(callback)

简化版本，仅检测键盘显示/隐藏状态。

**参数：**
- `callback(isVisible)`: 回调函数
  - `isVisible`: boolean - 键盘是否可见

**返回值：**
- `Function`: 清理函数

### keyboardDetectorMixin

Vue 混入，提供以下功能：

**数据属性：**
- `isKeyboardVisible`: boolean - 键盘是否可见
- `keyboardHeight`: number - 键盘高度

**方法：**
- `onKeyboardShow(height)`: 键盘显示时的回调（可重写）
- `onKeyboardHide()`: 键盘隐藏时的回调（可重写）
- `initKeyboardDetector(options)`: 初始化键盘检测
- `destroyKeyboardDetector()`: 销毁键盘检测

## 浏览器兼容性

| 浏览器 | VirtualKeyboard API | 降级方案 | 支持状态 |
|--------|-------------------|----------|----------|
| Chrome 94+ | ✅ | ✅ | 完全支持 |
| Safari (iOS/macOS) | ❌ | ✅ | 支持 |
| Firefox | ❌ | ✅ | 支持 |
| Edge 94+ | ✅ | ✅ | 完全支持 |
| Chrome on iOS | ❌ | ✅ | 支持 |

## 工作原理

1. **VirtualKeyboard API**（Chrome 94+）
   - 使用 `navigator.virtualKeyboard.geometrychange` 事件
   - 精确获取键盘位置和尺寸
   - 性能最佳，体验最流畅

2. **Visual Viewport API**（降级方案）
   - 监听 `window.visualViewport.resize` 事件
   - 通过视口高度变化推断键盘状态
   - 兼容性好，覆盖大部分浏览器

3. **传统方案**（最终降级）
   - 监听 `window.resize` 事件
   - 通过窗口高度变化检测键盘
   - 兼容性最好，但精度较低

## 最佳实践

### 1. 选择合适的方案

- **简单场景**：使用 `useSimpleKeyboardDetector`
- **需要键盘高度**：使用 `useKeyboardDetector`
- **Vue 组件**：使用 `keyboardDetectorMixin`

### 2. 内存管理

```javascript
// ✅ 正确：记得清理
const cleanup = useKeyboardDetector(callback);
// 在组件销毁时
cleanup();

// ❌ 错误：忘记清理，可能导致内存泄漏
useKeyboardDetector(callback);
```

### 3. 自定义阈值

```javascript
// 对于高分辨率设备，可能需要更大的阈值
useKeyboardDetector(callback, {
  threshold: 200 // 默认是 100
});
```

### 4. 错误处理

```javascript
try {
  const cleanup = useKeyboardDetector(callback);
} catch (error) {
  console.warn('键盘检测初始化失败:', error);
  // 提供降级方案
}
```

## 常见问题

### Q: 为什么在某些 Android 设备上检测不准确？

A: 不同 Android 设备和浏览器的行为可能不同。可以尝试调整 `threshold` 参数，或者结合其他检测方法。

### Q: iOS Safari 上的表现如何？

A: iOS Safari 不支持 VirtualKeyboard API，但降级方案工作良好。注意 iOS 的键盘行为与 Android 有所不同。

### Q: 如何在 uni-app 中使用？

A: 完全兼容 uni-app，按照 Vue 组件的使用方式即可。

### Q: 性能影响如何？

A: 工具经过优化，使用 `requestAnimationFrame` 和事件节流，对性能影响极小。

## 更新日志

### v1.0.0
- 初始版本
- 支持 VirtualKeyboard API 和降级方案
- 提供 Vue 混入
- 完整的错误处理和内存管理

## 许可证

MIT License