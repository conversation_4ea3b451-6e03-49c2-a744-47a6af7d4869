/**
 * 键盘检测工具
 * 支持 VirtualKeyboard API (Chrome 94+) 和降级方案
 * <AUTHOR> by AI Assistant
 */

/**
 * 键盘监听器
 * @param {Function} callback - 回调函数，参数为 (isVisible, height)
 * @param {Object} options - 配置选项
 * @param {number} options.threshold - 键盘检测阈值，默认 100px
 * @returns {Function} 清理函数
 */
export function useKeyboardDetector(callback, options = {}) {
  const { threshold = 100 } = options;
  const initialHeight = window.visualViewport ? window.visualViewport.height : window.innerHeight;
  
  let geometryChangeHandler = null;
  let resizeHandler = null;
  let isDestroyed = false;
  
  // 优先使用 VirtualKeyboard API（Chrome 94+ 才支持）
  if ('virtualKeyboard' in navigator && typeof navigator.virtualKeyboard.addEventListener === 'function') {
    try {
      navigator.virtualKeyboard.overlaysContent = true;
      
      geometryChangeHandler = function(event) {
        if (isDestroyed) return;
        const keyboardHeight = event.target.boundingRect.height;
        if (keyboardHeight > 0) {
          callback(true, keyboardHeight); // 键盘显示
        } else {
          callback(false, 0); // 键盘隐藏
        }
      };
      
      navigator.virtualKeyboard.addEventListener('geometrychange', geometryChangeHandler);
    } catch (error) {
      console.warn('VirtualKeyboard API 初始化失败，使用降级方案:', error);
      setupFallback();
    }
  } else {
    setupFallback();
  }
  
  function setupFallback() {
    // fallback：兼容旧版 Android WebView 和 Safari
    resizeHandler = function() {
      if (isDestroyed) return;
      
      const currentHeight = window.visualViewport 
        ? window.visualViewport.height 
        : window.innerHeight;
      const diff = initialHeight - currentHeight;
      
      if (diff > threshold) {
        callback(true, diff); // 键盘显示
      } else {
        callback(false, 0); // 键盘隐藏
      }
    };
    
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', resizeHandler);
    } else {
      window.addEventListener('resize', resizeHandler);
    }
  }
  
  // 返回清理函数
  return function destroy() {
    isDestroyed = true;
    
    if (geometryChangeHandler && 'virtualKeyboard' in navigator) {
      try {
        navigator.virtualKeyboard.removeEventListener('geometrychange', geometryChangeHandler);
      } catch (error) {
        console.warn('移除 VirtualKeyboard 事件监听器失败:', error);
      }
    }
    
    if (resizeHandler) {
      if (window.visualViewport) {
        window.visualViewport.removeEventListener('resize', resizeHandler);
      } else {
        window.removeEventListener('resize', resizeHandler);
      }
    }
    
    geometryChangeHandler = null;
    resizeHandler = null;
  };
}

/**
 * Vue 混入：键盘检测
 * 在组件中使用：mixins: [keyboardDetectorMixin]
 */
export const keyboardDetectorMixin = {
  data() {
    return {
      isKeyboardVisible: false,
      keyboardHeight: 0,
      keyboardDetectorCleanup: null
    };
  },
  
  mounted() {
    this.initKeyboardDetector();
  },
  
  beforeDestroy() {
    this.destroyKeyboardDetector();
  },
  
  methods: {
    initKeyboardDetector(options = {}) {
      this.keyboardDetectorCleanup = useKeyboardDetector(
        (isVisible, height) => {
          this.isKeyboardVisible = isVisible;
          this.keyboardHeight = height;
          
          if (isVisible) {
            this.onKeyboardShow && this.onKeyboardShow(height);
          } else {
            this.onKeyboardHide && this.onKeyboardHide();
          }
        },
        options
      );
    },
    
    destroyKeyboardDetector() {
      if (this.keyboardDetectorCleanup) {
        this.keyboardDetectorCleanup();
        this.keyboardDetectorCleanup = null;
      }
    },
    
    // 子组件可以重写这些方法
    onKeyboardShow(height) {
      console.log('✅ 键盘弹出，高度：' + height + 'px');
    },
    
    onKeyboardHide() {
      console.log('❌ 键盘收起');
    }
  }
};

/**
 * 简化版本：仅检测键盘是否显示（不获取高度）
 * @param {Function} callback - 回调函数，参数为 (isVisible)
 * @returns {Function} 清理函数
 */
export function useSimpleKeyboardDetector(callback) {
  return useKeyboardDetector((isVisible, height) => {
    callback(isVisible);
  });
}

export default {
  useKeyboardDetector,
  useSimpleKeyboardDetector,
  keyboardDetectorMixin
};