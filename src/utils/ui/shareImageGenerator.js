/**
 * 统一的分享图片生成器
 * 集中管理所有Canvas生成逻辑
 */

/**
 * 检测WebP支持
 */
function detectWebPSupport() {
  try {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    const webpData = canvas.toDataURL('image/webp');
    return webpData.indexOf('data:image/webp') === 0;
  } catch (e) {
    return false;
  }
}

/**
 * 检测设备性能
 */
function detectDevicePerformance() {
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  const memory = navigator.deviceMemory || 4;
  const cores = navigator.hardwareConcurrency || 4;
  const isLowEnd = memory <= 2 || cores <= 2;

  return { isMobile, isLowEnd, memory, cores };
}

/**
 * 获取最优配置
 */
function getOptimalConfig() {
  const { isMobile, isLowEnd } = detectDevicePerformance();
  const supportsWebP = detectWebPSupport();

  // 统一配置策略
  const baseConfig = {
    backgroundColor: '#ffffff',
    format: supportsWebP ? 'webp' : 'jpeg',
    timeout: 10000
  };

  if (isLowEnd) {
    return {
      ...baseConfig,
      scale: 1.5,
      quality: 0.75,
      width: 400,
      height: 500
    };
  } else if (isMobile) {
    return {
      ...baseConfig,
      scale: 2,
      quality: 0.85,
      width: 600,
      height: 750
    };
  } else {
    return {
      ...baseConfig,
      scale: 2.5,
      quality: 0.9,
      width: 800,
      height: 1000
    };
  }
}

/**
 * 简单的图片尺寸控制变量 - 直接修改这里的数值即可
 */
const IMG_WIDTH = 300;  // 图片宽度，直接修改这个数字
const IMG_HEIGHT = 210; // 图片高度，直接修改这个数字

/**
 * 专门为商品分享卡片设计的Canvas绘制 - 按照原版垂直布局
 */
async function drawToCanvas(element) {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  // 设置Canvas尺寸 - 430×304，高质量渲染
  const canvasWidth = 430;
  const canvasHeight = 304;
  const pixelRatio = Math.min(window.devicePixelRatio || 2, 3); // 限制最高3倍，平衡质量与性能

  canvas.width = canvasWidth * pixelRatio;
  canvas.height = canvasHeight * pixelRatio;
  canvas.style.width = canvasWidth + 'px';
  canvas.style.height = canvasHeight + 'px';

  // 缩放上下文以匹配设备像素比
  ctx.scale(pixelRatio, pixelRatio);

  // 启用超高质量渲染
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = 'high';
  ctx.textRenderingOptimization = 'optimizeQuality';
  ctx.antialias = true;
  ctx.textAlign = 'start';
  ctx.textBaseline = 'alphabetic';

  // 绘制白色背景
  ctx.fillStyle = '#ffffff';
  ctx.fillRect(0, 0, canvasWidth, canvasHeight);

  // 绘制圆角背景
  const borderRadius = 12;
  roundRect(ctx, 0, 0, canvasWidth, canvasHeight, borderRadius);
  ctx.fill();

  // 布局参数 - 适配430×304尺寸
  const containerPadding = 15;

  // 图片区域 - 使用简单变量控制
  let imgWidth = IMG_WIDTH;   // 直接使用设定的宽度
  let imgHeight = IMG_HEIGHT; // 直接使用设定的高度
  let imgX = (canvasWidth - imgWidth) / 2;
  let imgY = containerPadding + 5; // 顶部间距

  // 绘制商品图片
  const productImg = element.querySelector('.share-img');
  if (productImg && productImg.complete && productImg.naturalWidth > 0) {
    try {
      // 保持设定的宽高，不做额外调整

      // 绘制高质量圆角图片
      ctx.save();

      // 启用超高质量图片渲染
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = 'high';

      // 添加轻微阴影效果提升视觉质量
      ctx.shadowColor = 'rgba(0, 0, 0, 0.1)';
      ctx.shadowBlur = 2;
      ctx.shadowOffsetX = 1;
      ctx.shadowOffsetY = 1;

      roundRect(ctx, imgX, imgY, imgWidth, imgHeight, 8);
      ctx.clip();
      ctx.drawImage(productImg, imgX, imgY, imgWidth, imgHeight);

      // 重置阴影
      ctx.shadowColor = 'transparent';
      ctx.shadowBlur = 0;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;

      ctx.restore();
    } catch (e) {
      console.warn('商品图片绘制失败:', e);
      // 占满宽度的占位符
      ctx.fillStyle = '#f5f5f5';
      ctx.fillRect(imgX, imgY, imgWidth, imgHeight);
    }
  } else {
    // 占满宽度的占位符
    ctx.fillStyle = '#f5f5f5';
    ctx.fillRect(imgX, imgY, imgWidth, imgHeight);
  }

  // 信息区域 - 图片下方，适配304高度
  const infoStartY = imgY + imgHeight + 15; // 减少间距
  const infoX = containerPadding + 20;
  const infoWidth = canvasWidth - (containerPadding + 20) * 2;

  // 绘制商品标题 - 高质量字体渲染
  const titleElement = element.querySelector('.share-title');
  if (titleElement) {
    ctx.fillStyle = '#262626';
    ctx.font = 'bold 20px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';
    const title = titleElement.textContent.trim();
    const truncatedTitle = truncateText(ctx, title, infoWidth);
    ctx.fillText(truncatedTitle, infoX, infoStartY);
  }

  // 绘制商品描述 - 高质量字体渲染
  const descElement = element.querySelector('.share-desc');
  if (descElement) {
    ctx.fillStyle = '#8C8C8C';
    ctx.font = '16px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';
    const desc = descElement.textContent.trim();
    const truncatedDesc = truncateText(ctx, desc, infoWidth);
    ctx.fillText(truncatedDesc, infoX, infoStartY + 25);
  }

  // 绘制价格 - 右对齐，高质量字体渲染
  const priceElement = element.querySelector('.share-price');
  if (priceElement) {
    ctx.fillStyle = '#FF5A1E';
    ctx.font = 'bold 24px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif';
    ctx.textAlign = 'right';
    ctx.textBaseline = 'top';
    const price = priceElement.textContent.trim();
    const priceX = canvasWidth - containerPadding - 20;
    ctx.fillText(price, priceX, infoStartY + 15);
  }

  return canvas;
}

/**
 * 绘制圆角矩形
 */
function roundRect(ctx, x, y, width, height, radius) {
  ctx.beginPath();
  ctx.moveTo(x + radius, y);
  ctx.lineTo(x + width - radius, y);
  ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
  ctx.lineTo(x + width, y + height - radius);
  ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
  ctx.lineTo(x + radius, y + height);
  ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
  ctx.lineTo(x, y + radius);
  ctx.quadraticCurveTo(x, y, x + radius, y);
  ctx.closePath();
}

/**
 * 文本截断处理
 */
function truncateText(ctx, text, maxWidth) {
  if (ctx.measureText(text).width <= maxWidth) {
    return text;
  }

  let truncated = text;
  while (ctx.measureText(truncated + '...').width > maxWidth && truncated.length > 0) {
    truncated = truncated.slice(0, -1);
  }

  return truncated + '...';
}





/**
 * 生成回退图片
 */
function generateFallbackImage(productInfo) {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  canvas.width = 400;
  canvas.height = 500;

  // 背景
  ctx.fillStyle = '#ffffff';
  ctx.fillRect(0, 0, 400, 500);

  // 标题
  ctx.fillStyle = '#262626';
  ctx.font = 'bold 18px Arial';
  ctx.fillText(productInfo.title || 'Product Share', 20, 40);

  // 价格
  ctx.fillStyle = '#FF5A1E';
  ctx.font = 'bold 24px Arial';
  ctx.fillText(productInfo.price || '$0.00', 20, 80);

  // 描述
  ctx.fillStyle = '#666666';
  ctx.font = '14px Arial';
  const desc = productInfo.description || 'Share from mobile app';
  ctx.fillText(desc.substring(0, 50) + '...', 20, 110);

  return canvas.toDataURL('image/jpeg', 0.8);
}

/**
 * 主要的图片生成函数 - 专门为商品分享设计
 */
export async function generateShareImage(element, productInfo = {}) {
  try {
    console.log('🎨 开始生成分享图片...');
    const startTime = performance.now();

    // 等待图片加载
    await waitForImages(element);

    // 绘制Canvas - 使用专门的商品分享布局
    const canvas = await drawToCanvas(element);

    // 生成超高质量图片
    const dataUrl = canvas.toDataURL('image/png', 1.0); // 最高质量

    const endTime = performance.now();
    console.log(`✅ 图片生成成功，耗时: ${(endTime - startTime).toFixed(2)}ms`);

    return dataUrl;
  } catch (error) {
    console.error('❌ 图片生成失败:', error);

    // 使用回退方案
    console.log('🔄 使用回退方案...');
    return generateFallbackImage(productInfo);
  }
}

/**
 * 等待图片加载
 */
function waitForImages(element) {
  return new Promise((resolve) => {
    const images = element.querySelectorAll('img');
    if (images.length === 0) {
      resolve();
      return;
    }

    let loadedCount = 0;
    const totalImages = images.length;

    const checkComplete = () => {
      loadedCount++;
      if (loadedCount >= totalImages) {
        resolve();
      }
    };

    images.forEach(img => {
      if (img.complete) {
        checkComplete();
      } else {
        img.onload = checkComplete;
        img.onerror = checkComplete; // 即使失败也继续
      }
    });

    // 超时保护
    setTimeout(resolve, 5000);
  });
}

/**
 * 预处理图片跨域
 */
export function prepareImagesForCanvas(element) {
  const images = element.querySelectorAll('img');
  images.forEach(img => {
    if (!img.crossOrigin) {
      img.crossOrigin = 'anonymous';
    }
  });
}



/**
 * 获取设备信息（调试用）
 */
export function getDeviceInfo() {
  const { isMobile, isLowEnd, memory, cores } = detectDevicePerformance();
  const supportsWebP = detectWebPSupport();
  const config = getOptimalConfig();

  return {
    device: {
      isMobile,
      isLowEnd,
      memory: memory + 'GB',
      cores: cores + ' cores'
    },
    support: {
      webp: supportsWebP
    },
    config
  };
}
