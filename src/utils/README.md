# Utils 文件夹结构说明

## 📁 文件夹组织

```
utils/
├── payment/                    # 💳 支付相关工具
│   ├── paypalSDK.js           # PayPal SDK加载器
│   ├── stripeSDK.js           # Stripe SDK加载器
│   ├── amazonPaySDK.js        # Amazon Pay SDK加载器
│   ├── paymentSDKManager.js   # 统一SDK管理器
│   ├── sdkPreloader.js        # SDK预加载工具
│   └── routeSDKGuard.js       # 路由级SDK检查守卫
│
├── bridge/                     # 🌉 桥接相关工具
│   ├── appBridge.js           # 应用桥接工具
│   ├── bridgeDebug.js         # 桥接调试工具
│   └── communication.js       # 通信工具
│
├── security/                   # 🔐 安全相关工具
│   ├── auth.js               # 认证工具
│   └── rsa.js                # RSA加密工具
│
├── ui/                        # 🎨 UI相关工具
│   ├── keyboardDetector.js    # 键盘检测工具
│   ├── keyboardDetector.example.js
│   ├── keyboardDetector.README.md
│   └── shareImageGenerator.js # 分享图片生成工具
│
├── mainOption/                # ⚙️ 主要配置
│   └── mainDirective.js       # 主要指令
│
├── common.js                  # 🔧 通用工具
├── errorCode.js               # 📋 错误码定义
├── request.js                 # 🌐 网络请求工具
├── webviewOptimization.js     # 📱 WebView优化工具
└── README.md                  # 📖 说明文档
```

## 🎯 各模块功能说明

### 💳 Payment 模块
专门处理所有支付相关的功能：
- **SDK管理**: 统一管理PayPal、Stripe、Amazon Pay等SDK
- **预加载**: 应用启动时预加载支付SDK
- **三重保险**: 应用启动 → 路由检查 → 组件兜底
- **配置驱动**: 基于配置文件动态加载SDK

### 🌉 Bridge 模块
处理应用与原生环境的桥接：
- **通信**: 与原生应用的通信协议
- **调试**: 桥接功能的调试工具
- **接口**: 统一的桥接接口

### 🔐 Security 模块
处理安全相关功能：
- **认证**: 用户身份认证
- **加密**: RSA等加密算法

### 🎨 UI 模块
处理用户界面相关工具：
- **键盘检测**: 检测键盘状态
- **图片生成**: 分享图片生成
- **界面优化**: 各种UI优化工具

## 🔧 使用示例

### 支付SDK使用
```javascript
// 使用统一的SDK管理器
import paymentSDKManager from '@/utils/payment/paymentSDKManager.js';

// 确保某个支付方式的SDK可用
await paymentSDKManager.ensureSDK('stripe');
```

### 桥接功能使用
```javascript
// 使用应用桥接
import appBridge from '@/utils/bridge/appBridge.js';

// 调用原生功能
appBridge.callNativeMethod('methodName', params);
```

### 安全功能使用
```javascript
// 使用认证工具
import { getToken, setToken } from '@/utils/security/auth.js';

// 获取用户token
const token = getToken();
```

## 📝 文件移动记录

从原来的扁平结构重新组织为模块化结构：

- `paymentSDKManager.js` → `payment/paymentSDKManager.js`
- `paypalSDK.js` → `payment/paypalSDK.js`
- `stripeSDK.js` → `payment/stripeSDK.js`
- `amazonPaySDK.js` → `payment/amazonPaySDK.js`
- `sdkPreloader.js` → `payment/sdkPreloader.js`
- `routeSDKGuard.js` → `payment/routeSDKGuard.js`
- `appBridge.js` → `bridge/appBridge.js`
- `bridgeDebug.js` → `bridge/bridgeDebug.js`
- `communication.js` → `bridge/communication.js`
- `auth.js` → `security/auth.js`
- `rsa.js` → `security/rsa.js`
- `keyboardDetector.*` → `ui/keyboardDetector.*`
- `shareImageGenerator.js` → `ui/shareImageGenerator.js`

## ✅ 优势

1. **模块化**: 按功能分类，便于维护
2. **清晰结构**: 一目了然的文件组织
3. **易于扩展**: 新功能可以轻松添加到对应模块
4. **职责明确**: 每个模块都有明确的职责范围
5. **便于查找**: 根据功能快速定位文件

## 🔄 迁移完成

所有相关的import路径已经更新完成，系统可以正常工作。