/**
 * Stripe SDK 加载工具
 * 统一管理Stripe SDK的加载逻辑
 */

let stripeSDKPromise = null;
let sdkLoadedCallbacks = []; // 存储等待SDK加载完成的回调

/**
 * 加载Stripe SDK
 * @param {Object} config - SDK配置
 * @returns {Promise} Stripe SDK实例
 */
const loadStripeSDK = async (config = {}) => {
  // 如果已经加载过，直接返回
  if (stripeSDKPromise) {
    return stripeSDKPromise;
  }

  // 如果已经存在，直接resolve
  if (window.Stripe) {
    console.log('✅ Stripe SDK已存在');
    stripeSDKPromise = Promise.resolve(window.Stripe);
    return stripeSDKPromise;
  }

  console.log('📦 开始加载Stripe SDK...');
  
  stripeSDKPromise = new Promise(async (resolve, reject) => {
    try {
      // 动态导入Stripe SDK
      const { loadStripe } = await import('@stripe/stripe-js');
      
      const defaultConfig = {
        publishableKey: config.publishableKey || config.stripeKey,
        stripeAccount: config.stripeAccount,
        locale: config.locale || 'en'
      };

      console.log('🔧 Stripe SDK配置:', {
        publishableKey: defaultConfig.publishableKey ? '***' + defaultConfig.publishableKey.slice(-4) : 'undefined',
        stripeAccount: defaultConfig.stripeAccount,
        locale: defaultConfig.locale
      });

      if (!defaultConfig.publishableKey) {
        throw new Error('Stripe publishable key is required');
      }

      // 加载Stripe实例
      const stripe = await loadStripe(defaultConfig.publishableKey, {
        stripeAccount: defaultConfig.stripeAccount,
        locale: defaultConfig.locale
      });

      if (!stripe) {
        throw new Error('Failed to load Stripe SDK');
      }

      console.log('✅ Stripe SDK加载成功');
      console.log('🔍 SDK功能检查:', {
        hasStripe: !!stripe,
        hasElements: !!stripe.elements,
        hasConfirmPayment: !!stripe.confirmPayment
      });
      
      // 将Stripe实例保存到window对象（可选）
      window.Stripe = stripe;
      
      // 执行所有等待的回调
      sdkLoadedCallbacks.forEach(callback => callback(stripe));
      sdkLoadedCallbacks = [];
      resolve(stripe);
      
    } catch (error) {
      console.error('❌ Stripe SDK加载失败:', error);
      stripeSDKPromise = null; // 重置，允许重试
      reject(error);
    }
  });

  return stripeSDKPromise;
};

/**
 * 获取已加载的Stripe SDK
 * @returns {Object|null} Stripe SDK实例或null
 */
const getStripeSDK = () => {
  return window.Stripe || null;
};

/**
 * 检查Stripe SDK是否已加载
 * @returns {boolean} 是否已加载
 */
const isStripeSDKLoaded = () => {
  return !!(window.Stripe);
};

/**
 * 等待SDK加载完成
 * @returns {Promise} Stripe SDK实例
 */
const waitForStripeSDK = () => {
  return new Promise((resolve) => {
    if (isStripeSDKLoaded()) {
      resolve(window.Stripe);
    } else {
      sdkLoadedCallbacks.push(resolve);
    }
  });
};

/**
 * 重置SDK加载状态（用于重试）
 */
const resetStripeSDK = () => {
  stripeSDKPromise = null;
  sdkLoadedCallbacks = [];
  if (window.Stripe) {
    delete window.Stripe;
  }
};

export default loadStripeSDK;
export { 
  getStripeSDK, 
  isStripeSDKLoaded, 
  resetStripeSDK,
  waitForStripeSDK
};