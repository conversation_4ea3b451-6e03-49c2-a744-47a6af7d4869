/**
 * 路由级别的SDK预检查守卫
 * 在每次路由切换时检查并确保支付SDK可用性
 */

import paymentSDKManager from './paymentSDKManager.js';
import { 
    ROUTE_SDK_CHECK_ENABLED, 
    SDK_CHECK_INTERVAL,
    getEnabledPaymentMethods 
} from '@/config/paymentSDKConfig.js';

// SDK检查的防抖机制，避免频繁检查
let lastCheckTime = 0;
let isChecking = false;

/**
 * 路由前置守卫 - 检查SDK可用性
 */
export const beforeEachGuard = (to, from, next) => {
  // 检查全局开关
  if (!ROUTE_SDK_CHECK_ENABLED) {
    next();
    return;
  }
  
  // 检查是否需要进行SDK检查
  const currentTime = Date.now();
  const shouldCheck = currentTime - lastCheckTime > SDK_CHECK_INTERVAL && !isChecking;
  
  if (shouldCheck) {
    console.log(`🛡️ 路由守卫检查: ${from.path || '初始'} → ${to.path}`);
    checkSDKAvailability();
  }
  
  next();
};

/**
 * 异步检查SDK可用性（不阻塞路由跳转）
 */
async function checkSDKAvailability() {
  if (isChecking) {
    console.log('⏭️ SDK检查已在进行中，跳过本次检查');
    return;
  }
  
  isChecking = true;
  lastCheckTime = Date.now();
  
  try {
    console.log('🔍 路由守卫开始检查SDK状态...');
    
    // 获取当前SDK状态
    const sdkStatus = paymentSDKManager.getSDKStatus();
    console.log('📊 当前SDK状态:', sdkStatus);
    
    const promises = [];
    const enabledPayments = getEnabledPaymentMethods();
    
    // 动态检查所有启用的支付方式
    enabledPayments.forEach(({ key, name }) => {
      if (!sdkStatus[key]?.loaded && !sdkStatus[key]?.loading) {
        console.log(`🔄 路由守卫触发${name} SDK检查`);
        promises.push(
          paymentSDKManager.ensureSDK(key).catch(error => {
            console.warn(`⚠️ 路由守卫${name} SDK检查失败:`, error);
          })
        );
      }
    });
    
    if (promises.length > 0) {
      // 并行检查所有需要的SDK
      await Promise.allSettled(promises);
      console.log('✅ 路由守卫SDK检查完成');
    } else {
      console.log('✅ 路由守卫检查：所有SDK都已可用');
    }
    
  } catch (error) {
    console.error('❌ 路由守卫SDK检查出错:', error);
  } finally {
    isChecking = false;
  }
}

/**
 * 手动触发SDK检查（供其他模块调用）
 */
export const triggerSDKCheck = () => {
  // 重置时间戳，强制检查
  lastCheckTime = 0;
  return checkSDKAvailability();
};

/**
 * 重置检查状态（用于测试或特殊情况）
 */
export const resetCheckState = () => {
  lastCheckTime = 0;
  isChecking = false;
  console.log('🔄 路由守卫检查状态已重置');
};

export default {
  beforeEachGuard,
  triggerSDKCheck,
  resetCheckState
};