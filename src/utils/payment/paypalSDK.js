import { loadScript } from "@paypal/paypal-js";
import config from "@/config";

let paypalSDKPromise = null;
let sdkLoadedCallbacks = [];

/**
 * 检查PayPal SDK是否已加载
 */
export function isPayPalSDKLoaded() {
  return !!(window.paypal && window.paypal.Buttons);
}

/**
 * 等待PayPal SDK加载完成
 */
export function waitForPayPalSDK() {
  return new Promise((resolve) => {
    if (isPayPalSDKLoaded()) {
      resolve(window.paypal);
    } else {
      sdkLoadedCallbacks.push(resolve);
    }
  });
}

/**
 * Load PayPal SDK and render buttons to a specified container element
 * @param {string} containerId - The ID of the container element to render PayPal buttons
 * @returns {Promise<boolean>} - Returns true if successful, false otherwise
 */
async function loadPayPalSDK(containerId = "#your-container-element") {
  // 如果已经加载过，直接返回
  if (paypalSDKPromise) {
    return paypalSDKPromise;
  }

  // 如果已经存在，直接resolve
  if (isPayPalSDKLoaded()) {
    console.log('✅ PayPal SDK已存在');
    paypalSDKPromise = Promise.resolve(window.paypal);
    return paypalSDKPromise;
  }

  console.log('📦 开始加载PayPal SDK...');
  
  paypalSDKPromise = (async () => {
    try {
      window.paypal = await loadScript({ clientId: config.payment.paypal.clientId });
      
      // 执行所有等待的回调
      sdkLoadedCallbacks.forEach(callback => callback(window.paypal));
      sdkLoadedCallbacks = [];
      
      console.log('✅ PayPal SDK加载成功');
      return window.paypal;
    } catch (error) {
      console.error("failed to load the PayPal JS SDK script", error);
      paypalSDKPromise = null; // 重置，允许重试
      return false;
    }
  })();

  return paypalSDKPromise;
}

export default loadPayPalSDK;
