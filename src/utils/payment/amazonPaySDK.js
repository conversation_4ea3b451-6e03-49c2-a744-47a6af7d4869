/**
 * Amazon Pay SDK 加载工具
 * 类似于 paypalSDK.js 的实现方式
 */

let amazonPaySDKPromise = null;
let sdkLoadedCallbacks = []; // 存储等待SDK加载完成的回调

/**
 * 加载Amazon Pay SDK
 * @param {Object} config - SDK配置
 * @returns {Promise} Amazon Pay SDK实例
 */
const loadAmazonPaySDK = (config = {}) => {
  // 如果已经加载过，直接返回
  if (amazonPaySDKPromise) {
    return amazonPaySDKPromise;
  }

  // 如果已经存在，直接resolve
  if (window.amazon && window.amazon.Pay) {
    console.log('✅ Amazon Pay SDK已存在');
    amazonPaySDKPromise = Promise.resolve(window.amazon);
    return amazonPaySDKPromise;
  }

  console.log('📦 开始加载Amazon Pay SDK...');
  
  amazonPaySDKPromise = new Promise((resolve, reject) => {
    // 默认配置
    const defaultConfig = {
      sdkSrc: 'https://static-na.payments-amazon.com/checkout.js',
      region: 'US', // NA, EU, JP
      sandbox: true
    };

    const finalConfig = { ...defaultConfig, ...config };
    
    // 根据地区选择SDK URL
    const sdkUrls = {
      'NA': 'https://static-na.payments-amazon.com/checkout.js',
      'US': 'https://static-na.payments-amazon.com/checkout.js', // US使用NA的SDK
      'EU': 'https://static-eu.payments-amazon.com/checkout.js', 
      'JP': 'https://static-fe.payments-amazon.com/checkout.js'
    };

    const sdkUrl = finalConfig.sdkSrc || sdkUrls[finalConfig.region] || sdkUrls.NA;

    console.log('🔧 Amazon Pay SDK配置:', {
      url: sdkUrl,
      region: finalConfig.region,
      sandbox: finalConfig.sandbox
    });

    const script = document.createElement('script');
    script.src = sdkUrl;
    script.async = true;
    
    script.onload = () => {
      console.log('✅ Amazon Pay SDK加载成功');
      console.log('🔍 SDK功能检查:', {
        hasAmazon: !!window.amazon,
        hasPay: !!window.amazon?.Pay,
        hasRenderButton: !!window.amazon?.Pay?.renderButton
      });
      
      if (window.amazon && window.amazon.Pay) {
        // 执行所有等待的回调
        sdkLoadedCallbacks.forEach(callback => callback(window.amazon));
        sdkLoadedCallbacks = [];
        resolve(window.amazon);
      } else {
        reject(new Error('Amazon Pay SDK加载后未找到必需的API'));
      }
    };

    script.onerror = (error) => {
      console.error('❌ Amazon Pay SDK加载失败:', error);
      amazonPaySDKPromise = null; // 重置，允许重试
      reject(new Error('Amazon Pay SDK加载失败'));
    };

    // 添加到页面
    document.head.appendChild(script);
  });

  return amazonPaySDKPromise;
};

/**
 * 获取已加载的Amazon Pay SDK
 * @returns {Object|null} Amazon Pay SDK实例或null
 */
const getAmazonPaySDK = () => {
  return window.amazon || null;
};

/**
 * 检查Amazon Pay SDK是否已加载
 * @returns {boolean} 是否已加载
 */
const isAmazonPaySDKLoaded = () => {
  return !!(window.amazon && window.amazon.Pay);
};

/**
 * 等待SDK加载完成
 * @returns {Promise} Amazon Pay SDK实例
 */
const waitForAmazonPaySDK = () => {
  return new Promise((resolve) => {
    if (isAmazonPaySDKLoaded()) {
      resolve(window.amazon);
    } else {
      sdkLoadedCallbacks.push(resolve);
    }
  });
};

/**
 * 重置SDK加载状态（用于重试）
 */
const resetAmazonPaySDK = () => {
  amazonPaySDKPromise = null;
  sdkLoadedCallbacks = [];
};

export default loadAmazonPaySDK;
export { 
  getAmazonPaySDK, 
  isAmazonPaySDKLoaded, 
  resetAmazonPaySDK,
  waitForAmazonPaySDK
}; 