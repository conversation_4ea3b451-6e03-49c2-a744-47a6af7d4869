/**
 * 支付SDK管理器
 * 统一管理所有支付SDK的加载、检测和重新加载
 */

// SDK加载器通过配置文件动态导入
import { 
    PAYMENT_SDK_CONFIG, 
    getEnabledPaymentMethods, 
    getPaymentConfigById,
    SDK_LOAD_TIMEOUT 
} from '@/config/paymentSDKConfig.js';

class PaymentSDKManager {
    constructor() {
        // 动态生成SDK状态缓存（基于配置文件）
        this.sdkStatus = this.initializeSDKStatus();
        
        // 检查初始状态
        this.checkInitialSDKStatus();
    }
    
    /**
     * 初始化SDK状态（基于配置动态生成）
     */
    initializeSDKStatus() {
        const status = {};
        const enabledPayments = getEnabledPaymentMethods();
        
        enabledPayments.forEach(({ key }) => {
            status[key] = {
                loaded: false,
                loading: false,
                error: null
            };
        });
        
        return status;
    }
    
    /**
     * 检查初始SDK状态
     */
    checkInitialSDKStatus() {
        const enabledPayments = getEnabledPaymentMethods();
        
        enabledPayments.forEach(({ key, name, sdkChecker }) => {
            if (typeof window !== 'undefined' && sdkChecker()) {
                this.sdkStatus[key].loaded = true;
                console.log(`✅ ${name} SDK 已加载`);
            }
        });
    }
    
    /**
     * 确保PayPal SDK已加载
     */
    async ensurePayPalSDK() {
        // 实时检查SDK状态
        if (typeof window !== 'undefined' && window.paypal) {
            this.sdkStatus.paypal.loaded = true;
            this.sdkStatus.paypal.loading = false;
            console.log('✅ PayPal SDK 已经可用，无需重新加载');
            return true;
        }
        
        if (this.sdkStatus.paypal.loaded) {
            return true;
        }
        
        if (this.sdkStatus.paypal.loading) {
            // 如果正在加载，等待加载完成
            console.log('⏳ PayPal SDK 正在加载中，等待完成...');
            return this.waitForSDK('paypal');
        }
        
        try {
            this.sdkStatus.paypal.loading = true;
            this.sdkStatus.paypal.error = null;
            
            console.log('🔄 开始加载 PayPal SDK...');
            await loadPayPalSDK();
            
            this.sdkStatus.paypal.loaded = true;
            this.sdkStatus.paypal.loading = false;
            
            console.log('✅ PayPal SDK 加载成功');
            return true;
            
        } catch (error) {
            this.sdkStatus.paypal.loading = false;
            this.sdkStatus.paypal.error = error;
            
            console.error('❌ PayPal SDK 加载失败:', error);
            throw error;
        }
    }
    
    /**
     * 确保Amazon Pay SDK已加载
     */
    async ensureAmazonPaySDK() {
        // 实时检查SDK状态
        if (typeof window !== 'undefined' && window.amazon && window.amazon.Pay) {
            this.sdkStatus.amazonPay.loaded = true;
            this.sdkStatus.amazonPay.loading = false;
            console.log('✅ Amazon Pay SDK 已经可用，无需重新加载');
            return true;
        }
        
        if (this.sdkStatus.amazonPay.loaded) {
            return true;
        }
        
        if (this.sdkStatus.amazonPay.loading) {
            // 如果正在加载，等待加载完成
            console.log('⏳ Amazon Pay SDK 正在加载中，等待完成...');
            return this.waitForSDK('amazonPay');
        }
        
        try {
            this.sdkStatus.amazonPay.loading = true;
            this.sdkStatus.amazonPay.error = null;
            
            console.log('🔄 开始加载 Amazon Pay SDK...');
            
            const amazonConfig = {
                region: 'US', // 与后端保持一致
                sandbox: config.amazonPay?.sandbox !== false,
                sdkSrc: config.amazonPay?.sdkSrc
            };
            
            await loadAmazonPaySDK(amazonConfig);
            
            this.sdkStatus.amazonPay.loaded = true;
            this.sdkStatus.amazonPay.loading = false;
            
            console.log('✅ Amazon Pay SDK 加载成功');
            return true;
            
        } catch (error) {
            this.sdkStatus.amazonPay.loading = false;
            this.sdkStatus.amazonPay.error = error;
            
            console.error('❌ Amazon Pay SDK 加载失败:', error);
            throw error;
        }
    }
    
    /**
     * 等待SDK加载完成
     */
    async waitForSDK(sdkName, timeout = 10000) {
        const startTime = Date.now();
        
        return new Promise((resolve, reject) => {
            const checkStatus = () => {
                if (this.sdkStatus[sdkName].loaded) {
                    resolve(true);
                    return;
                }
                
                if (this.sdkStatus[sdkName].error) {
                    reject(this.sdkStatus[sdkName].error);
                    return;
                }
                
                if (Date.now() - startTime > timeout) {
                    reject(new Error(`${sdkName} SDK 加载超时`));
                    return;
                }
                
                if (!this.sdkStatus[sdkName].loading) {
                    reject(new Error(`${sdkName} SDK 加载已停止`));
                    return;
                }
                
                setTimeout(checkStatus, 100);
            };
            
            checkStatus();
        });
    }
    
    /**
     * 通用的SDK确保方法（基于配置）
     */
    async ensureSDK(paymentKey) {
        const config = PAYMENT_SDK_CONFIG[paymentKey];
        if (!config || !config.enabled) {
            console.log(`💡 支付方式 ${paymentKey} 未启用或不存在`);
            return false;
        }
        
        // 实时检查SDK状态
        if (typeof window !== 'undefined' && config.sdkChecker()) {
            this.sdkStatus[paymentKey].loaded = true;
            this.sdkStatus[paymentKey].loading = false;
            console.log(`✅ ${config.name} SDK 已经可用，无需重新加载`);
            return true;
        }
        
        if (this.sdkStatus[paymentKey].loaded) {
            return true;
        }
        
        if (this.sdkStatus[paymentKey].loading) {
            console.log(`⏳ ${config.name} SDK 正在加载中，等待完成...`);
            return this.waitForSDK(paymentKey);
        }
        
        try {
            this.sdkStatus[paymentKey].loading = true;
            this.sdkStatus[paymentKey].error = null;
            
            console.log(`🔄 开始加载 ${config.name} SDK...`);
            
            // 动态加载SDK
            const sdkLoader = await config.sdkLoader();
            await sdkLoader(config.preloadConfig);
            
            this.sdkStatus[paymentKey].loaded = true;
            this.sdkStatus[paymentKey].loading = false;
            
            console.log(`✅ ${config.name} SDK 加载成功`);
            return true;
            
        } catch (error) {
            this.sdkStatus[paymentKey].loading = false;
            this.sdkStatus[paymentKey].error = error;
            
            console.error(`❌ ${config.name} SDK 加载失败:`, error);
            throw error;
        }
    }

    /**
     * 根据支付方式ID确保对应SDK已加载
     */
    async ensureSDKByPaymentId(paymentId) {
        const config = getPaymentConfigById(paymentId);
        
        if (!config) {
            console.log(`💡 支付方式 ${paymentId} 无需预加载SDK`);
            return true;
        }
        
        try {
            // 找到对应的payment key
            const paymentKey = Object.keys(PAYMENT_SDK_CONFIG).find(
                key => PAYMENT_SDK_CONFIG[key].paymentId === parseInt(paymentId)
            );
            
            if (!paymentKey) {
                console.log(`💡 支付方式 ${paymentId} 无需预加载SDK`);
                return true;
            }
            
            await this.ensureSDK(paymentKey);
            return true;
            
        } catch (error) {
            console.error(`❌ 支付方式 ${paymentId} 对应的SDK加载失败:`, error);
            throw error;
        }
    }
    
    /**
     * 批量确保多个SDK已加载
     */
    async ensureMultipleSDKs(paymentIds) {
        const promises = paymentIds.map(id => this.ensureSDKByPaymentId(id));
        
        try {
            await Promise.allSettled(promises);
            console.log('✅ 批量SDK检查完成');
            return true;
        } catch (error) {
            console.error('❌ 批量SDK检查失败:', error);
            throw error;
        }
    }
    
    /**
     * 确保所有启用的SDK已加载
     */
    async ensureAllEnabledSDKs() {
        const enabledPayments = getEnabledPaymentMethods();
        const promises = enabledPayments.map(({ key }) => this.ensureSDK(key));
        
        try {
            await Promise.allSettled(promises);
            console.log('✅ 所有启用的SDK检查完成');
            return true;
        } catch (error) {
            console.error('❌ 批量SDK检查失败:', error);
            throw error;
        }
    }
    
    /**
     * 获取SDK状态信息
     */
    getSDKStatus() {
        return {
            paypal: { ...this.sdkStatus.paypal },
            amazonPay: { ...this.sdkStatus.amazonPay }
        };
    }
    
    /**
     * 重置SDK状态（强制重新加载）
     */
    resetSDKStatus(sdkName) {
        if (sdkName) {
            this.sdkStatus[sdkName] = {
                loaded: false,
                loading: false,
                error: null
            };
        } else {
            // 重置所有SDK状态
            Object.keys(this.sdkStatus).forEach(key => {
                this.sdkStatus[key] = {
                    loaded: false,
                    loading: false,
                    error: null
                };
            });
        }
    }
}

// 创建单例实例
const paymentSDKManager = new PaymentSDKManager();

export default paymentSDKManager;