/**
 * SDK预加载工具
 * 在应用启动时预加载所有支付相关的SDK
 */

import paymentSDKManager from "./paymentSDKManager.js";
import { 
    SDK_PRELOAD_ENABLED, 
    getEnabledPaymentMethods 
} from "../config/paymentSDKConfig.js";

/**
 * 预加载所有支付SDK
 */
export async function preloadPaymentSDKs() {
  // 检查全局开关
  if (!SDK_PRELOAD_ENABLED) {
    console.log('💡 SDK预加载已禁用');
    return;
  }
  
  console.log('🚀 开始预加载支付SDK...');
  
  const promises = [];
  const enabledPayments = getEnabledPaymentMethods();
  
  // 动态预加载所有启用的支付SDK
  enabledPayments.forEach(({ key, name, sdkLoader, preloadConfig }) => {
    try {
      // 标记为正在加载
      paymentSDKManager.sdkStatus[key].loading = true;
      paymentSDKManager.sdkStatus[key].error = null;
      
      promises.push(
        sdkLoader()
          .then(loader => loader(preloadConfig))
          .then(() => {
            console.log(`✅ ${name} SDK预加载完成`);
            // 更新SDK管理器状态
            paymentSDKManager.sdkStatus[key].loaded = true;
            paymentSDKManager.sdkStatus[key].loading = false;
          })
          .catch(error => {
            console.warn(`⚠️ ${name} SDK预加载失败:`, error);
            // 更新SDK管理器状态
            paymentSDKManager.sdkStatus[key].loaded = false;
            paymentSDKManager.sdkStatus[key].loading = false;
            paymentSDKManager.sdkStatus[key].error = error;
          })
      );
      
      console.log(`📦 ${name} SDK预加载中...`, preloadConfig);
    } catch (error) {
      console.warn(`⚠️ ${name} SDK预加载配置失败:`, error);
      paymentSDKManager.sdkStatus[key].loading = false;
      paymentSDKManager.sdkStatus[key].error = error;
    }
  });
  
  // 等待所有SDK加载完成（不阻塞应用启动）
  Promise.allSettled(promises).then(results => {
    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.filter(r => r.status === 'rejected').length;
    
    console.log(`🎯 支付SDK预加载完成: ${successful}个成功, ${failed}个失败`);
    
    if (failed > 0) {
      console.log('❌ 失败的SDK:', results.filter(r => r.status === 'rejected').map(r => r.reason));
    }
  });
}

export default preloadPaymentSDKs; 