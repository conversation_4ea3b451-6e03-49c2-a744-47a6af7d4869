const fs = require('fs');
const path = require('path');

// 版本文件路径
const versionPath = path.join(__dirname, 'src/version.json');

console.log('📈 递增版本号...');

try {
  // 读取当前版本信息
  let versionData = { version: '1.0.0', buildNumber: 1 };

  if (fs.existsSync(versionPath)) {
    const versionContent = fs.readFileSync(versionPath, 'utf8');
    versionData = JSON.parse(versionContent);
  }

  console.log('📋 当前版本:', versionData.version);
  console.log('📋 当前构建号:', versionData.buildNumber);

  // 递增构建号
  versionData.buildNumber += 1;

  // 更新版本号逻辑
  const versionParts = versionData.version.split('.');
  let majorVersion = parseInt(versionParts[0]) || 1;
  let minorVersion = parseInt(versionParts[1]) || 0;

  // 当构建号达到10时，进入下一个版本
  if (versionData.buildNumber >= 10) {
    console.log('🔄 构建号达到10，升级版本...');

    // 次版本号递增
    minorVersion += 1;

    // 重置构建号为1
    versionData.buildNumber = 1;

    console.log(`📈 版本升级: ${majorVersion}.${minorVersion - 1}.x → ${majorVersion}.${minorVersion}.1`);
  }

  versionData.version = `${majorVersion}.${minorVersion}.${versionData.buildNumber}`;

  // 写回文件
  fs.writeFileSync(versionPath, JSON.stringify(versionData, null, 2), 'utf8');

  console.log('✅ 版本号已更新!');
  console.log('🆕 新版本:', versionData.version);
  console.log('🆕 新构建号:', versionData.buildNumber);

} catch (error) {
  console.error('❌ 版本号递增失败:', error.message);
  process.exit(1);
} 