const fs = require('fs');
const path = require('path');

console.log('🔄 开始重命名构建输出目录...');

// 定义路径
const distDir = path.join(__dirname, 'dist/build');
const h5Dir = path.join(distDir, 'h5');
const webDir = path.join(distDir, 'web');

// 检查h5目录是否存在
if (!fs.existsSync(h5Dir)) {
  console.log('❌ 找不到 dist/build/h5 目录');
  process.exit(1);
}

try {
  // 如果web目录已存在，先删除
  if (fs.existsSync(webDir)) {
    console.log('🗑️  删除已存在的 web 目录...');
    fs.rmSync(webDir, { recursive: true, force: true });
  }

  // 重命名 h5 目录为 web
  console.log('📁 重命名 h5 目录为 web...');
  fs.renameSync(h5Dir, webDir);

  // 修复HTML文件中的路径
  const htmlPath = path.join(webDir, 'index.html');
  
  if (fs.existsSync(htmlPath)) {
    console.log('🔧 修复HTML文件中的路径...');
    
    let htmlContent = fs.readFileSync(htmlPath, 'utf8');
    
    // 显示修复前的路径
    const beforePaths = htmlContent.match(/(href|src)="\/[^"]*"/g) || [];
    if (beforePaths.length > 0) {
      console.log('🔍 发现需要修复的绝对路径:');
      beforePaths.forEach(path => console.log('  -', path));
    }

    // 修复绝对路径为相对路径
    htmlContent = htmlContent
      // 修复CSS文件路径 (href="/static/..." -> href="./static/...")
      .replace(/href="\/static\//g, 'href="./static/')
      // 修复JS文件路径 (src="/static/..." -> src="./static/...")
      .replace(/src="\/static\//g, 'src="./static/')
      // 修复其他可能的绝对路径，但排除http/https链接
      .replace(/="\/(?!http|https|\/)/g, '="./')
      // 确保base标签存在
      .replace(/<head([^>]*)>/, '<head$1>\n  <base href="./">')
      // 如果已经有base标签，确保href是相对路径
      .replace(/<base\s+href="\/[^"]*"/g, '<base href="./"');

    // 显示修复后的结果
    const afterPaths = htmlContent.match(/(href|src)="\.\/[^"]*"/g) || [];
    console.log('✅ 修复后的相对路径:');
    afterPaths.forEach(path => console.log('  +', path));

    // 写回文件
    fs.writeFileSync(htmlPath, htmlContent, 'utf8');
    
    console.log('✅ HTML路径修复完成！');
    console.log('📋 修复的路径数量:', beforePaths.length);
  }

  console.log('🎉 构建输出目录重命名完成！');
  console.log('📁 输出目录: dist/build/web');
  console.log('🌐 现在可以直接双击 dist/build/web/index.html 在浏览器中打开！');

} catch (error) {
  console.error('❌ 重命名过程中出现错误:', error.message);
  process.exit(1);
} 